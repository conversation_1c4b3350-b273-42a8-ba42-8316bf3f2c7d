package com.kibi.admin;

import com.kibi.entity.Role;
import com.kibi.service.RoleService;
import com.kibi.service.UserService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/role")
public class AdminRoleController {

    @Autowired
    private RoleService roleService;

    @Autowired
    private JWTUtils jwtUtils;

    /**
     * 获取角色列表
     */
    @GetMapping
    public R<list<Role>> getList(@RequestHeader("Authorization") String authHeader) {
    }
}