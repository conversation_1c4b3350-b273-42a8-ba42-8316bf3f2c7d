<template>
  <view class="system-monitor-panel">
    <!-- 页面标题 -->
    <view class="panel-header">
      <view class="header-title">
        <text class="title-icon">📊</text>
        <text class="title-text">系统监控</text>
      </view>
      <view class="header-actions">
        <view class="refresh-btn" @click="refreshData" :class="{ loading: isRefreshing }">
          <text class="refresh-icon">🔄</text>
          <text>刷新</text>
        </view>
        <view class="auto-refresh-toggle" @click="toggleAutoRefresh">
          <text class="toggle-icon">{{ autoRefresh ? '⏸️' : '▶️' }}</text>
          <text>{{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}</text>
        </view>
      </view>
    </view>

    <!-- 系统概览卡片 -->
    <view class="overview-cards">
      <view class="card-row">
        <view class="metric-card server-status" :class="serverStatus.status">
          <view class="card-header">
            <text class="card-icon">🖥️</text>
            <text class="card-title">服务器状态</text>
          </view>
          <view class="card-content">
            <view class="status-indicator">
              <text class="status-dot"></text>
              <text class="status-text">{{ serverStatus.text }}</text>
            </view>
            <view class="uptime">
              <text class="uptime-label">运行时间:</text>
              <text class="uptime-value">{{ serverStatus.uptime }}</text>
            </view>
          </view>
        </view>

        <view class="metric-card cpu-usage">
          <view class="card-header">
            <text class="card-icon">⚡</text>
            <text class="card-title">CPU使用率</text>
          </view>
          <view class="card-content">
            <view class="metric-value">{{ systemMetrics.cpuUsage }}%</view>
            <view class="metric-chart">
              <view class="progress-bar">
                <view
                  class="progress-fill"
                  :style="{ width: systemMetrics.cpuUsage + '%' }"
                  :class="getCpuStatusClass(systemMetrics.cpuUsage)"
                ></view>
              </view>
            </view>
          </view>
        </view>

        <view class="metric-card memory-usage">
          <view class="card-header">
            <text class="card-icon">💾</text>
            <text class="card-title">内存使用</text>
          </view>
          <view class="card-content">
            <view class="metric-value">{{ systemMetrics.memoryUsage }}%</view>
            <view class="memory-details">
              <text class="memory-text">{{ systemMetrics.usedMemory }}GB / {{ systemMetrics.totalMemory }}GB</text>
            </view>
            <view class="metric-chart">
              <view class="progress-bar">
                <view
                  class="progress-fill"
                  :style="{ width: systemMetrics.memoryUsage + '%' }"
                  :class="getMemoryStatusClass(systemMetrics.memoryUsage)"
                ></view>
              </view>
            </view>
          </view>
        </view>

        <view class="metric-card disk-usage">
          <view class="card-header">
            <text class="card-icon">💿</text>
            <text class="card-title">磁盘使用</text>
          </view>
          <view class="card-content">
            <view class="metric-value">{{ systemMetrics.diskUsage }}%</view>
            <view class="disk-details">
              <text class="disk-text">{{ systemMetrics.usedDisk }}GB / {{ systemMetrics.totalDisk }}GB</text>
            </view>
            <view class="metric-chart">
              <view class="progress-bar">
                <view
                  class="progress-fill"
                  :style="{ width: systemMetrics.diskUsage + '%' }"
                  :class="getDiskStatusClass(systemMetrics.diskUsage)"
                ></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细监控面板 -->
    <view class="monitor-panels">
      <!-- 数据库性能监控 -->
      <view class="monitor-panel database-panel">
        <view class="panel-title">
          <text class="panel-icon">🗄️</text>
          <text class="panel-text">数据库性能</text>
        </view>
        <view class="panel-content">
          <view class="db-metrics">
            <view class="db-metric">
              <text class="metric-label">连接数</text>
              <text class="metric-value">{{ databaseMetrics.connections }}</text>
            </view>
            <view class="db-metric">
              <text class="metric-label">查询/秒</text>
              <text class="metric-value">{{ databaseMetrics.queriesPerSecond }}</text>
            </view>
            <view class="db-metric">
              <text class="metric-label">平均响应时间</text>
              <text class="metric-value">{{ databaseMetrics.avgResponseTime }}ms</text>
            </view>
            <view class="db-metric">
              <text class="metric-label">慢查询</text>
              <text class="metric-value">{{ databaseMetrics.slowQueries }}</text>
            </view>
          </view>
          <view class="db-status" :class="databaseMetrics.status">
            <text class="status-dot"></text>
            <text class="status-text">{{ getDatabaseStatusText(databaseMetrics.status) }}</text>
          </view>
        </view>
      </view>

      <!-- 应用程序健康状况 -->
      <view class="monitor-panel app-health-panel">
        <view class="panel-title">
          <text class="panel-icon">🏥</text>
          <text class="panel-text">应用健康状况</text>
        </view>
        <view class="panel-content">
          <view class="health-checks">
            <view
              v-for="check in healthChecks"
              :key="check.name"
              class="health-check"
              :class="check.status"
            >
              <view class="check-info">
                <text class="check-name">{{ check.name }}</text>
                <text class="check-description">{{ check.description }}</text>
              </view>
              <view class="check-status">
                <text class="status-icon">{{ getHealthStatusIcon(check.status) }}</text>
                <text class="status-text">{{ getHealthStatusText(check.status) }}</text>
              </view>
              <view v-if="check.lastCheck" class="check-time">
                <text>最后检查: {{ formatTime(check.lastCheck) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 系统告警和问题提示 -->
    <view class="alerts-section" v-if="alerts.length > 0">
      <view class="section-title">
        <text class="title-icon">⚠️</text>
        <text class="title-text">系统告警</text>
      </view>
      <view class="alerts-list">
        <view
          v-for="alert in alerts"
          :key="alert.id"
          class="alert-item"
          :class="alert.level"
        >
          <view class="alert-header">
            <text class="alert-icon">{{ getAlertIcon(alert.level) }}</text>
            <text class="alert-title">{{ alert.title }}</text>
            <text class="alert-time">{{ formatTime(alert.timestamp) }}</text>
          </view>
          <view class="alert-message">{{ alert.message }}</view>
          <view class="alert-actions" v-if="alert.actions">
            <view
              v-for="action in alert.actions"
              :key="action.name"
              class="alert-action"
              @click="handleAlertAction(alert, action)"
            >
              {{ action.label }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 实时日志流 -->
    <view class="logs-section">
      <view class="section-title">
        <text class="title-icon">📋</text>
        <text class="title-text">实时日志</text>
        <view class="logs-controls">
          <view class="log-level-filter">
            <text
              v-for="level in logLevels"
              :key="level"
              class="level-tag"
              :class="{ active: selectedLogLevels.includes(level) }"
              @click="toggleLogLevel(level)"
            >
              {{ level }}
            </text>
          </view>
          <view class="clear-logs-btn" @click="clearLogs">
            <text>清空日志</text>
          </view>
        </view>
      </view>
      <view class="logs-container">
        <view
          v-for="log in filteredLogs"
          :key="log.id"
          class="log-entry"
          :class="log.level.toLowerCase()"
        >
          <text class="log-time">{{ formatLogTime(log.timestamp) }}</text>
          <text class="log-level">{{ log.level }}</text>
          <text class="log-message">{{ log.message }}</text>
        </view>
        <view v-if="filteredLogs.length === 0" class="no-logs">
          <text>暂无日志数据</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { http } from '@/utils/http'

// 接口类型定义
interface ServerStatus {
  status: 'healthy' | 'warning' | 'error'
  text: string
  uptime: string
}

interface SystemMetrics {
  cpuUsage: number
  memoryUsage: number
  usedMemory: number
  totalMemory: number
  diskUsage: number
  usedDisk: number
  totalDisk: number
}

interface DatabaseMetrics {
  connections: number
  queriesPerSecond: number
  avgResponseTime: number
  slowQueries: number
  status: 'healthy' | 'warning' | 'error'
}

interface HealthCheck {
  name: string
  description: string
  status: 'healthy' | 'warning' | 'error'
  lastCheck: string
}

interface Alert {
  id: string
  level: 'info' | 'warning' | 'error' | 'critical'
  title: string
  message: string
  timestamp: string
  actions?: Array<{
    name: string
    label: string
  }>
}

interface LogEntry {
  id: string
  timestamp: string
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR'
  message: string
}

// 响应式数据
const isRefreshing = ref(false)
const autoRefresh = ref(true)
const refreshInterval = ref<number | null>(null)

// 服务器状态
const serverStatus = ref<ServerStatus>({
  status: 'healthy',
  text: '运行正常',
  uptime: '0天 0小时 0分钟'
})

// 系统指标
const systemMetrics = ref<SystemMetrics>({
  cpuUsage: 0,
  memoryUsage: 0,
  usedMemory: 0,
  totalMemory: 0,
  diskUsage: 0,
  usedDisk: 0,
  totalDisk: 0
})

// 数据库指标
const databaseMetrics = ref<DatabaseMetrics>({
  connections: 0,
  queriesPerSecond: 0,
  avgResponseTime: 0,
  slowQueries: 0,
  status: 'healthy'
})

// 健康检查
const healthChecks = ref<HealthCheck[]>([])

// 系统告警
const alerts = ref<Alert[]>([])

// 日志相关
const logs = ref<LogEntry[]>([])
const logLevels = ['DEBUG', 'INFO', 'WARN', 'ERROR']
const selectedLogLevels = ref<string[]>(['INFO', 'WARN', 'ERROR'])

// 计算属性
const filteredLogs = computed(() => {
  return logs.value.filter(log => selectedLogLevels.value.includes(log.level))
})

// 方法
const refreshData = async () => {
  if (isRefreshing.value) return

  isRefreshing.value = true
  try {
    await Promise.all([
      fetchServerStatus(),
      fetchSystemMetrics(),
      fetchDatabaseMetrics(),
      fetchHealthChecks(),
      fetchAlerts()
    ])
  } catch (error) {
    console.error('刷新监控数据失败:', error)
    uni.showToast({
      title: '刷新数据失败',
      icon: 'error'
    })
  } finally {
    isRefreshing.value = false
  }
}

const fetchServerStatus = async () => {
  try {
    const response = await http({
      url: '/admin/monitor/server-status',
      method: 'GET'
    })

    if (response.code === 200 && response.data) {
      serverStatus.value = response.data
    }
  } catch (error) {
    console.error('获取服务器状态失败:', error)
    // 使用模拟数据
    serverStatus.value = {
      status: 'healthy',
      text: '运行正常',
      uptime: '15天 8小时 32分钟'
    }
  }
}

const fetchSystemMetrics = async () => {
  try {
    const response = await http({
      url: '/admin/monitor/system-metrics',
      method: 'GET'
    })

    if (response.code === 200 && response.data) {
      systemMetrics.value = response.data
    }
  } catch (error) {
    console.error('获取系统指标失败:', error)
    // 使用模拟数据
    systemMetrics.value = {
      cpuUsage: Math.floor(Math.random() * 80) + 10,
      memoryUsage: Math.floor(Math.random() * 70) + 20,
      usedMemory: 6.4,
      totalMemory: 16,
      diskUsage: Math.floor(Math.random() * 60) + 30,
      usedDisk: 245,
      totalDisk: 500
    }
  }
}

const fetchDatabaseMetrics = async () => {
  try {
    const response = await http({
      url: '/admin/monitor/database-metrics',
      method: 'GET'
    })

    if (response.code === 200 && response.data) {
      databaseMetrics.value = response.data
    }
  } catch (error) {
    console.error('获取数据库指标失败:', error)
    // 使用模拟数据
    databaseMetrics.value = {
      connections: Math.floor(Math.random() * 50) + 10,
      queriesPerSecond: Math.floor(Math.random() * 100) + 50,
      avgResponseTime: Math.floor(Math.random() * 50) + 10,
      slowQueries: Math.floor(Math.random() * 5),
      status: 'healthy'
    }
  }
}

const fetchHealthChecks = async () => {
  try {
    const response = await http({
      url: '/admin/monitor/health-checks',
      method: 'GET'
    })

    if (response.code === 200 && response.data) {
      healthChecks.value = response.data
    }
  } catch (error) {
    console.error('获取健康检查失败:', error)
    // 使用模拟数据
    healthChecks.value = [
      {
        name: 'API服务',
        description: '主要API服务运行状态',
        status: 'healthy',
        lastCheck: new Date().toISOString()
      },
      {
        name: '数据库连接',
        description: '数据库连接池状态',
        status: 'healthy',
        lastCheck: new Date().toISOString()
      },
      {
        name: '缓存服务',
        description: 'Redis缓存服务状态',
        status: 'warning',
        lastCheck: new Date().toISOString()
      },
      {
        name: '文件存储',
        description: '文件存储服务状态',
        status: 'healthy',
        lastCheck: new Date().toISOString()
      }
    ]
  }
}

const fetchAlerts = async () => {
  try {
    const response = await http({
      url: '/admin/monitor/alerts',
      method: 'GET'
    })

    if (response.code === 200 && response.data) {
      alerts.value = response.data
    }
  } catch (error) {
    console.error('获取系统告警失败:', error)
    // 使用模拟数据
    alerts.value = [
      {
        id: '1',
        level: 'warning',
        title: '内存使用率较高',
        message: '系统内存使用率已达到75%，建议检查内存泄漏问题',
        timestamp: new Date().toISOString(),
        actions: [
          { name: 'check_memory', label: '检查内存' },
          { name: 'restart_service', label: '重启服务' }
        ]
      }
    ]
  }
}

const fetchLogs = async () => {
  try {
    const response = await http({
      url: '/admin/monitor/logs',
      method: 'GET',
      data: {
        levels: selectedLogLevels.value,
        limit: 100
      }
    })

    if (response.code === 200 && response.data) {
      logs.value = response.data
    }
  } catch (error) {
    console.error('获取日志失败:', error)
    // 使用模拟数据
    const mockLogs = [
      {
        id: '1',
        timestamp: new Date().toISOString(),
        level: 'INFO' as const,
        message: '用户登录成功: <EMAIL>'
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 60000).toISOString(),
        level: 'WARN' as const,
        message: '数据库连接池使用率较高: 85%'
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 120000).toISOString(),
        level: 'ERROR' as const,
        message: 'API请求失败: /api/users/profile - 500 Internal Server Error'
      }
    ]
    logs.value = mockLogs
  }
}

// 工具方法
const getCpuStatusClass = (usage: number) => {
  if (usage >= 80) return 'critical'
  if (usage >= 60) return 'warning'
  return 'normal'
}

const getMemoryStatusClass = (usage: number) => {
  if (usage >= 85) return 'critical'
  if (usage >= 70) return 'warning'
  return 'normal'
}

const getDiskStatusClass = (usage: number) => {
  if (usage >= 90) return 'critical'
  if (usage >= 75) return 'warning'
  return 'normal'
}

const getDatabaseStatusText = (status: string) => {
  const statusMap = {
    healthy: '运行正常',
    warning: '性能警告',
    error: '连接异常'
  }
  return statusMap[status] || '未知状态'
}

const getHealthStatusIcon = (status: string) => {
  const iconMap = {
    healthy: '✅',
    warning: '⚠️',
    error: '❌'
  }
  return iconMap[status] || '❓'
}

const getHealthStatusText = (status: string) => {
  const textMap = {
    healthy: '正常',
    warning: '警告',
    error: '异常'
  }
  return textMap[status] || '未知'
}

const getAlertIcon = (level: string) => {
  const iconMap = {
    info: 'ℹ️',
    warning: '⚠️',
    error: '❌',
    critical: '🚨'
  }
  return iconMap[level] || 'ℹ️'
}

const formatTime = (timestamp: string) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now.getTime() - time.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const formatLogTime = (timestamp: string) => {
  const time = new Date(timestamp)
  return time.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value

  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }

  refreshInterval.value = setInterval(() => {
    refreshData()
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

const toggleLogLevel = (level: string) => {
  const index = selectedLogLevels.value.indexOf(level)
  if (index > -1) {
    selectedLogLevels.value.splice(index, 1)
  } else {
    selectedLogLevels.value.push(level)
  }

  // 重新获取日志
  fetchLogs()
}

const clearLogs = () => {
  logs.value = []
  uni.showToast({
    title: '日志已清空',
    icon: 'success'
  })
}

const handleAlertAction = (alert: Alert, action: { name: string; label: string }) => {
  uni.showModal({
    title: '确认操作',
    content: `确定要执行"${action.label}"操作吗？`,
    success: (res) => {
      if (res.confirm) {
        // 这里可以调用相应的API
        uni.showToast({
          title: `正在执行${action.label}...`,
          icon: 'loading'
        })

        // 模拟操作完成
        setTimeout(() => {
          uni.showToast({
            title: '操作完成',
            icon: 'success'
          })

          // 移除已处理的告警
          const index = alerts.value.findIndex(a => a.id === alert.id)
          if (index > -1) {
            alerts.value.splice(index, 1)
          }
        }, 2000)
      }
    }
  })
}

// 生命周期
onMounted(() => {
  // 初始化数据
  refreshData()

  // 开始自动刷新
  if (autoRefresh.value) {
    startAutoRefresh()
  }

  // 获取日志
  fetchLogs()

  // 模拟实时日志推送
  const logInterval = setInterval(() => {
    const newLog: LogEntry = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      level: ['INFO', 'WARN', 'ERROR'][Math.floor(Math.random() * 3)] as any,
      message: `系统日志消息 - ${new Date().toLocaleTimeString()}`
    }

    logs.value.unshift(newLog)

    // 保持最多100条日志
    if (logs.value.length > 100) {
      logs.value = logs.value.slice(0, 100)
    }
  }, 10000) // 每10秒添加一条新日志

  // 清理定时器
  onUnmounted(() => {
    clearInterval(logInterval)
  })
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="scss" scoped>
@import '../styles/system-monitor-panel.scss';
</style>