<script setup lang="ts">
import { ref, onMounted } from 'vue'
import AdminLayout from './components/AdminLayout.vue'
import { http } from '@/utils/http'
import { useTokenStore } from '@/stores'

const tokenStore = useTokenStore()

onMounted(async () => {
  if (tokenStore.token) {
    const res = await http({
                url: '/admin/user/validateToken'
            })
    console.log(res)
    if (res.code == 200 & res.data) {
      isLogin.value = true
    }
  }
})

const isLogin = ref(false)
</script>

<template>
  <view v-if="isLogin">
    <AdminLayout />
  </view>
  <view v-else>登录窗口</view>
</template>

<style lang="scss" scoped>
@import './styles/admin-layout.scss';
</style>