<template>
  <view class="role-management">
    <!-- 角色管理头部 -->
    <view class="management-header">
      <view class="header-title">角色管理</view>
      <view class="header-actions">
        <button class="action-btn primary" @click="handleAddRole">
          <text class="btn-icon">➕</text>
          添加角色
        </button>
      </view>
    </view>

    <!-- 数据表格 -->
    <DataTable
      :data="roleCrud.data.value"
      :columns="roleColumns"
      :loading="roleCrud.loading.value"
      :pagination="roleCrud.pagination"
      :searchable="true"
      :sortable="true"
      :actions="tableActions"
      :selectable="true"
      :batch-actions="batchActions"
      title="角色列表"
      search-placeholder="搜索角色名称、代码"
      @search="roleCrud.search"
      @sort="roleCrud.sort"
      @page-change="roleCrud.changePage"
      @page-size-change="roleCrud.changePageSize"
      @batch-action="handleBatchAction"
    />

    <!-- 角色表单弹窗 -->
    <view v-if="showRoleForm" class="modal-overlay" @click="closeRoleForm">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="modal-title">{{ formMode === 'create' ? '添加角色' : formMode === 'edit' ? '编辑角色' : '查看角色' }}</view>
          <view class="modal-close" @click="closeRoleForm">✕</view>
        </view>
        
        <view class="modal-body">
          <DataForm
            :fields="roleFormFields"
            :data="formData"
            :mode="formMode"
            :loading="roleCrud.submitting.value"
            @submit="handleRoleSubmit"
            @cancel="closeRoleForm"
          />
        </view>
      </view>
    </view>

    <!-- 权限分配弹窗 -->
    <view v-if="showPermissionAssign" class="modal-overlay" @click="closePermissionAssign">
      <view class="modal-content large" @click.stop>
        <view class="modal-header">
          <view class="modal-title">分配权限 - {{ selectedRole?.name }}</view>
          <view class="modal-close" @click="closePermissionAssign">✕</view>
        </view>
        
        <view class="modal-body">
          <view class="permission-assign-content">
            <view class="assign-title">选择权限</view>
            <view class="permission-tree">
              <view 
                v-for="permission in permissionTree" 
                :key="permission.id"
                class="permission-node"
              >
                <label class="permission-item">
                  <input 
                    type="checkbox" 
                    :value="permission.id"
                    v-model="selectedPermissionIds"
                    class="permission-checkbox"
                    @change="handlePermissionChange(permission)"
                  />
                  <view class="permission-info">
                    <view class="permission-name">{{ permission.name }}</view>
                    <view class="permission-desc">{{ permission.description }}</view>
                  </view>
                </label>
                
                <!-- 子权限 -->
                <view v-if="permission.children && permission.children.length > 0" class="permission-children">
                  <label 
                    v-for="child in permission.children" 
                    :key="child.id"
                    class="permission-item child"
                  >
                    <input 
                      type="checkbox" 
                      :value="child.id"
                      v-model="selectedPermissionIds"
                      class="permission-checkbox"
                    />
                    <view class="permission-info">
                      <view class="permission-name">{{ child.name }}</view>
                      <view class="permission-desc">{{ child.description }}</view>
                    </view>
                  </label>
                </view>
              </view>
            </view>
            
            <view class="assign-actions">
              <button class="action-btn" @click="closePermissionAssign">取消</button>
              <button 
                class="action-btn primary" 
                @click="handlePermissionAssign"
                :disabled="assigningPermission"
              >
                {{ assigningPermission ? '分配中...' : '确认分配' }}
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useCrud } from '../../composables/useCrud'
import { mockRoleApi, mockPermissionApi, roleExtendedApi, permissionExtendedApi } from '../../api/user'
import DataTable from '../common/DataTable.vue'
import DataForm from '../common/DataForm.vue'
import type { Role, Permission, RoleFormData, TableColumn, TableAction, FormField } from '../../types/admin'

// 响应式数据
const showRoleForm = ref(false)
const showPermissionAssign = ref(false)
const formMode = ref<'create' | 'edit' | 'view'>('create')
const formData = ref<Partial<RoleFormData>>({})
const selectedRole = ref<Role | null>(null)
const selectedPermissionIds = ref<number[]>([])
const permissionTree = ref<Permission[]>([])
const assigningPermission = ref(false)

// 使用CRUD composable
const roleCrud = useCrud<Role>(mockRoleApi, {
  initialPagination: { pageSize: 10 },
  autoLoad: true,
  cacheKey: 'roles'
})

// 表格列配置
const roleColumns: TableColumn[] = [
  {
    key: 'id',
    title: 'ID',
    width: '80px',
    sortable: true
  },
  {
    key: 'name',
    title: '角色名称',
    sortable: true
  },
  {
    key: 'code',
    title: '角色代码',
    sortable: true
  },
  {
    key: 'description',
    title: '描述'
  },
  {
    key: 'permissionNames',
    title: '权限',
    render: (value: string[]) => value?.slice(0, 3).join(', ') + (value?.length > 3 ? '...' : '') || '无'
  },
  {
    key: 'status',
    title: '状态',
    render: (value: number) => value === 1 ? '启用' : '禁用'
  },
  {
    key: 'createTime',
    title: '创建时间'
  }
]

// 表格操作
const tableActions: TableAction[] = [
  {
    type: 'view',
    label: '查看',
    handler: handleViewRole
  },
  {
    type: 'edit',
    label: '编辑',
    handler: handleEditRole
  },
  {
    type: 'delete',
    label: '删除',
    handler: handleDeleteRole
  }
]

// 批量操作
const batchActions: TableAction[] = [
  {
    type: 'delete',
    label: '批量删除',
    handler: handleBatchDelete
  }
]

// 角色表单字段
const roleFormFields = computed<FormField[]>(() => [
  {
    key: 'name',
    label: '角色名称',
    type: 'text',
    required: true,
    placeholder: '请输入角色名称',
    rules: [
      { type: 'required', message: '角色名称不能为空' },
      { type: 'max', value: 50, message: '角色名称最多50个字符' }
    ]
  },
  {
    key: 'code',
    label: '角色代码',
    type: 'text',
    required: true,
    placeholder: '请输入角色代码（英文字母、数字、下划线）',
    disabled: formMode.value === 'edit', // 编辑时不允许修改代码
    rules: [
      { type: 'required', message: '角色代码不能为空' },
      { type: 'pattern', value: '^[a-zA-Z0-9_]+$', message: '角色代码只能包含字母、数字和下划线' },
      { type: 'max', value: 50, message: '角色代码最多50个字符' }
    ]
  },
  {
    key: 'description',
    label: '角色描述',
    type: 'textarea',
    placeholder: '请输入角色描述',
    rules: [
      { type: 'max', value: 200, message: '角色描述最多200个字符' }
    ]
  },
  {
    key: 'status',
    label: '角色状态',
    type: 'radio',
    required: true,
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  }
])

// 方法
function handleAddRole() {
  formMode.value = 'create'
  formData.value = {
    name: '',
    code: '',
    description: '',
    status: 1,
    permissionIds: []
  }
  showRoleForm.value = true
}

function handleViewRole(role: Role) {
  formMode.value = 'view'
  formData.value = { ...role }
  showRoleForm.value = true
}

function handleEditRole(role: Role) {
  formMode.value = 'edit'
  formData.value = { ...role }
  showRoleForm.value = true
}

async function handleDeleteRole(role: Role) {
  await roleCrud.remove(role.id)
}

function closeRoleForm() {
  showRoleForm.value = false
  formData.value = {}
}

async function handleRoleSubmit(data: RoleFormData) {
  try {
    if (formMode.value === 'create') {
      await roleCrud.create(data)
    } else {
      await roleCrud.update(formData.value.id!, data)
    }
    
    closeRoleForm()
  } catch (error) {
    console.error('Submit role error:', error)
  }
}

async function handleBatchAction(action: TableAction, selectedItems: Role[]) {
  if (action.type === 'delete') {
    await handleBatchDelete(selectedItems)
  }
}

async function handleBatchDelete(roles: Role[]) {
  const ids = roles.map(role => role.id)
  await roleCrud.batchRemove(ids)
}

// 权限分配相关方法
async function handleAssignPermission(role: Role) {
  selectedRole.value = role
  selectedPermissionIds.value = [...role.permissionIds]
  
  // 加载权限树
  try {
    const permissions = await mockPermissionApi.list({ page: 1, pageSize: 1000 })
    permissionTree.value = buildPermissionTree(permissions.items || [])
  } catch (error) {
    console.error('Load permissions error:', error)
    roleCrud.showError('加载权限列表失败')
  }
  
  showPermissionAssign.value = true
}

function buildPermissionTree(permissions: Permission[]): Permission[] {
  const tree: Permission[] = []
  const map = new Map<number, Permission>()
  
  // 创建映射
  permissions.forEach(permission => {
    map.set(permission.id, { ...permission, children: [] })
  })
  
  // 构建树结构
  permissions.forEach(permission => {
    const node = map.get(permission.id)!
    if (permission.parentId && map.has(permission.parentId)) {
      const parent = map.get(permission.parentId)!
      parent.children = parent.children || []
      parent.children.push(node)
    } else {
      tree.push(node)
    }
  })
  
  return tree
}

function handlePermissionChange(permission: Permission) {
  // 如果选中父权限，自动选中所有子权限
  if (selectedPermissionIds.value.includes(permission.id)) {
    if (permission.children) {
      permission.children.forEach(child => {
        if (!selectedPermissionIds.value.includes(child.id)) {
          selectedPermissionIds.value.push(child.id)
        }
      })
    }
  } else {
    // 如果取消选中父权限，自动取消选中所有子权限
    if (permission.children) {
      permission.children.forEach(child => {
        const index = selectedPermissionIds.value.indexOf(child.id)
        if (index > -1) {
          selectedPermissionIds.value.splice(index, 1)
        }
      })
    }
  }
}

function closePermissionAssign() {
  showPermissionAssign.value = false
  selectedRole.value = null
  selectedPermissionIds.value = []
  permissionTree.value = []
}

async function handlePermissionAssign() {
  if (!selectedRole.value) return
  
  try {
    assigningPermission.value = true
    
    // 这里应该调用实际的权限分配API
    // await roleExtendedApi.assignPermissions(selectedRole.value.id, selectedPermissionIds.value)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    roleCrud.showSuccess('权限分配成功')
    closePermissionAssign()
    
    // 刷新角色列表
    await roleCrud.refresh()
  } catch (error) {
    console.error('Assign permission error:', error)
    roleCrud.showError('权限分配失败')
  } finally {
    assigningPermission.value = false
  }
}

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style lang="scss" scoped>
.role-management {
  height: 100%;
  display: flex;
  flex-direction: column;

  .management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .action-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background-color: #fff;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;

          &:hover {
            background-color: #40a9ff;
          }
        }

        .btn-icon {
          font-size: 12px;
        }
      }
    }
  }

  // 弹窗样式
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background-color: #fff;
      border-radius: 8px;
      width: 90%;
      max-width: 600px;
      max-height: 80vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      &.large {
        max-width: 800px;
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #e8e8e8;

        .modal-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .modal-close {
          cursor: pointer;
          font-size: 18px;
          color: #999;
          
          &:hover {
            color: #333;
          }
        }
      }

      .modal-body {
        flex: 1;
        padding: 24px;
        overflow-y: auto;
      }
    }
  }

  // 权限分配样式
  .permission-assign-content {
    .assign-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .permission-tree {
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 8px;

      .permission-node {
        margin-bottom: 8px;

        .permission-item {
          display: flex;
          align-items: center;
          padding: 12px;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.3s ease;

          &:hover {
            background-color: #f5f5f5;
          }

          &.child {
            margin-left: 24px;
            padding: 8px 12px;
            background-color: #fafafa;
          }

          .permission-checkbox {
            margin-right: 12px;
          }

          .permission-info {
            flex: 1;

            .permission-name {
              font-size: 14px;
              color: #333;
              margin-bottom: 4px;
            }

            .permission-desc {
              font-size: 12px;
              color: #666;
            }
          }
        }

        .permission-children {
          margin-top: 4px;
        }
      }
    }

    .assign-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #e8e8e8;

      .action-btn {
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;

          &:hover {
            background-color: #40a9ff;
          }

          &:disabled {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
            color: #999;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}
</style>
