<template>
  <view class="system-tools-panel">
    <!-- 页面标题 -->
    <view class="panel-header">
      <view class="header-title">
        <text class="title-icon">🔧</text>
        <text class="title-text">系统工具</text>
      </view>
      <view class="header-description">
        <text>系统维护和管理工具集合</text>
      </view>
    </view>

    <!-- 工具分类导航 -->
    <view class="tools-categories">
      <view
        v-for="category in toolCategories"
        :key="category.id"
        class="category-tab"
        :class="{ active: activeCategory === category.id }"
        @click="switchCategory(category.id)"
      >
        <text class="category-icon">{{ category.icon }}</text>
        <text class="category-name">{{ category.name }}</text>
      </view>
    </view>

    <!-- 工具列表 -->
    <view class="tools-grid">
      <view
        v-for="tool in currentCategoryTools"
        :key="tool.id"
        class="tool-card"
        :class="{ disabled: tool.disabled }"
        @click="handleToolClick(tool)"
      >
        <view class="tool-header">
          <view class="tool-icon" :class="tool.status">
            <text>{{ tool.icon }}</text>
          </view>
          <view class="tool-status" v-if="tool.status">
            <text class="status-dot" :class="tool.status"></text>
            <text class="status-text">{{ getStatusText(tool.status) }}</text>
          </view>
        </view>

        <view class="tool-content">
          <view class="tool-title">{{ tool.title }}</view>
          <view class="tool-description">{{ tool.description }}</view>

          <view class="tool-meta" v-if="tool.lastUsed || tool.size">
            <text v-if="tool.lastUsed" class="last-used">
              最后使用: {{ formatTime(tool.lastUsed) }}
            </text>
            <text v-if="tool.size" class="tool-size">
              大小: {{ tool.size }}
            </text>
          </view>
        </view>

        <view class="tool-actions">
          <view
            v-if="tool.quickActions"
            class="quick-actions"
          >
            <view
              v-for="action in tool.quickActions"
              :key="action.name"
              class="quick-action"
              @click.stop="handleQuickAction(tool, action)"
            >
              <text class="action-icon">{{ action.icon }}</text>
              <text class="action-text">{{ action.label }}</text>
            </view>
          </view>

          <view class="primary-action" @click.stop="handleToolClick(tool)">
            <text class="action-text">{{ tool.actionText || '打开' }}</text>
            <text class="action-arrow">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 工具详情弹窗 -->
    <view v-if="showToolModal" class="tool-modal-overlay" @click="closeToolModal">
      <view class="tool-modal" @click.stop>
        <view class="modal-header">
          <view class="modal-title">
            <text class="modal-icon">{{ selectedTool?.icon }}</text>
            <text class="modal-text">{{ selectedTool?.title }}</text>
          </view>
          <view class="modal-close" @click="closeToolModal">
            <text>✕</text>
          </view>
        </view>

        <view class="modal-content">
          <!-- 数据库备份工具 -->
          <view v-if="selectedTool?.id === 'database-backup'" class="backup-tool">
            <view class="tool-section">
              <view class="section-title">备份选项</view>
              <view class="backup-options">
                <view class="option-item">
                  <text class="option-label">备份类型:</text>
                  <view class="option-select">
                    <text
                      v-for="type in backupTypes"
                      :key="type.value"
                      class="select-option"
                      :class="{ active: selectedBackupType === type.value }"
                      @click="selectedBackupType = type.value"
                    >
                      {{ type.label }}
                    </text>
                  </view>
                </view>

                <view class="option-item">
                  <text class="option-label">压缩:</text>
                  <view class="option-toggle" @click="enableCompression = !enableCompression">
                    <view class="toggle-switch" :class="{ active: enableCompression }">
                      <view class="toggle-handle"></view>
                    </view>
                    <text class="toggle-text">{{ enableCompression ? '启用' : '禁用' }}</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="tool-section">
              <view class="section-title">备份历史</view>
              <view class="backup-history">
                <view
                  v-for="backup in backupHistory"
                  :key="backup.id"
                  class="backup-item"
                >
                  <view class="backup-info">
                    <text class="backup-name">{{ backup.name }}</text>
                    <text class="backup-time">{{ formatTime(backup.createTime) }}</text>
                  </view>
                  <view class="backup-size">{{ backup.size }}</view>
                  <view class="backup-actions">
                    <text class="backup-action" @click="downloadBackup(backup)">下载</text>
                    <text class="backup-action delete" @click="deleteBackup(backup)">删除</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 日志查看器 -->
          <view v-else-if="selectedTool?.id === 'log-viewer'" class="log-viewer-tool">
            <view class="tool-section">
              <view class="section-title">日志过滤</view>
              <view class="log-filters">
                <view class="filter-item">
                  <text class="filter-label">日志级别:</text>
                  <view class="filter-tags">
                    <text
                      v-for="level in logLevels"
                      :key="level"
                      class="filter-tag"
                      :class="{ active: selectedLogLevels.includes(level) }"
                      @click="toggleLogLevel(level)"
                    >
                      {{ level }}
                    </text>
                  </view>
                </view>

                <view class="filter-item">
                  <text class="filter-label">时间范围:</text>
                  <view class="time-range">
                    <text
                      v-for="range in timeRanges"
                      :key="range.value"
                      class="time-option"
                      :class="{ active: selectedTimeRange === range.value }"
                      @click="selectedTimeRange = range.value"
                    >
                      {{ range.label }}
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <view class="tool-section">
              <view class="section-title">实时日志</view>
              <view class="log-container">
                <view
                  v-for="log in filteredLogs"
                  :key="log.id"
                  class="log-entry"
                  :class="log.level.toLowerCase()"
                >
                  <text class="log-time">{{ formatLogTime(log.timestamp) }}</text>
                  <text class="log-level">{{ log.level }}</text>
                  <text class="log-message">{{ log.message }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 缓存管理工具 -->
          <view v-else-if="selectedTool?.id === 'cache-management'" class="cache-tool">
            <view class="tool-section">
              <view class="section-title">缓存统计</view>
              <view class="cache-stats">
                <view class="stat-item">
                  <text class="stat-label">总缓存数</text>
                  <text class="stat-value">{{ cacheStats.totalKeys }}</text>
                </view>
                <view class="stat-item">
                  <text class="stat-label">内存使用</text>
                  <text class="stat-value">{{ cacheStats.memoryUsage }}</text>
                </view>
                <view class="stat-item">
                  <text class="stat-label">命中率</text>
                  <text class="stat-value">{{ cacheStats.hitRate }}%</text>
                </view>
              </view>
            </view>

            <view class="tool-section">
              <view class="section-title">缓存操作</view>
              <view class="cache-operations">
                <view class="operation-btn" @click="clearAllCache">
                  <text class="btn-icon">🗑️</text>
                  <text class="btn-text">清空所有缓存</text>
                </view>
                <view class="operation-btn" @click="refreshCacheStats">
                  <text class="btn-icon">🔄</text>
                  <text class="btn-text">刷新统计</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 系统清理工具 -->
          <view v-else-if="selectedTool?.id === 'system-cleanup'" class="cleanup-tool">
            <view class="tool-section">
              <view class="section-title">清理选项</view>
              <view class="cleanup-options">
                <view
                  v-for="option in cleanupOptions"
                  :key="option.id"
                  class="cleanup-option"
                  :class="{ selected: option.selected }"
                  @click="option.selected = !option.selected"
                >
                  <view class="option-checkbox">
                    <text v-if="option.selected">✓</text>
                  </view>
                  <view class="option-info">
                    <text class="option-title">{{ option.title }}</text>
                    <text class="option-desc">{{ option.description }}</text>
                    <text class="option-size">预计释放: {{ option.estimatedSize }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <view class="footer-actions">
            <view class="action-btn secondary" @click="closeToolModal">
              <text>取消</text>
            </view>
            <view class="action-btn primary" @click="executeToolAction">
              <text>{{ getExecuteButtonText() }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 进度弹窗 -->
    <view v-if="showProgressModal" class="progress-modal-overlay">
      <view class="progress-modal">
        <view class="progress-header">
          <text class="progress-title">{{ progressInfo.title }}</text>
        </view>
        <view class="progress-content">
          <view class="progress-bar">
            <view
              class="progress-fill"
              :style="{ width: progressInfo.percentage + '%' }"
            ></view>
          </view>
          <text class="progress-text">{{ progressInfo.message }}</text>
          <text class="progress-percentage">{{ progressInfo.percentage }}%</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { http } from '@/utils/http'

// 接口类型定义
interface Tool {
  id: string
  title: string
  description: string
  icon: string
  category: string
  status?: 'healthy' | 'warning' | 'error'
  disabled?: boolean
  lastUsed?: string
  size?: string
  actionText?: string
  quickActions?: Array<{
    name: string
    label: string
    icon: string
  }>
}

interface ToolCategory {
  id: string
  name: string
  icon: string
}

interface BackupItem {
  id: string
  name: string
  createTime: string
  size: string
}

interface LogEntry {
  id: string
  timestamp: string
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR'
  message: string
}

interface CacheStats {
  totalKeys: number
  memoryUsage: string
  hitRate: number
}

interface CleanupOption {
  id: string
  title: string
  description: string
  estimatedSize: string
  selected: boolean
}

// 响应式数据
const activeCategory = ref('database')
const showToolModal = ref(false)
const selectedTool = ref<Tool | null>(null)
const showProgressModal = ref(false)

// 工具分类
const toolCategories = ref<ToolCategory[]>([
  { id: 'database', name: '数据库工具', icon: '🗄️' },
  { id: 'system', name: '系统工具', icon: '⚙️' },
  { id: 'monitoring', name: '监控工具', icon: '📊' },
  { id: 'maintenance', name: '维护工具', icon: '🔧' }
])

// 系统工具列表
const systemTools = ref<Tool[]>([
  {
    id: 'database-backup',
    title: '数据库备份',
    description: '创建和管理数据库备份文件',
    icon: '💾',
    category: 'database',
    status: 'healthy',
    lastUsed: '2024-01-15T10:30:00Z',
    actionText: '创建备份',
    quickActions: [
      { name: 'quick-backup', label: '快速备份', icon: '⚡' },
      { name: 'view-history', label: '查看历史', icon: '📋' }
    ]
  },
  {
    id: 'log-viewer',
    title: '日志查看器',
    description: '查看和分析系统日志文件',
    icon: '📋',
    category: 'monitoring',
    status: 'healthy',
    lastUsed: '2024-01-15T09:15:00Z',
    actionText: '查看日志'
  },
  {
    id: 'cache-management',
    title: '缓存管理',
    description: '管理和清理系统缓存',
    icon: '🗂️',
    category: 'system',
    status: 'warning',
    lastUsed: '2024-01-14T16:45:00Z',
    actionText: '管理缓存',
    quickActions: [
      { name: 'clear-cache', label: '清空缓存', icon: '🗑️' },
      { name: 'refresh-stats', label: '刷新统计', icon: '🔄' }
    ]
  },
  {
    id: 'system-cleanup',
    title: '系统清理',
    description: '清理临时文件和无用数据',
    icon: '🧹',
    category: 'maintenance',
    status: 'healthy',
    lastUsed: '2024-01-13T14:20:00Z',
    actionText: '开始清理'
  },
  {
    id: 'performance-monitor',
    title: '性能监控',
    description: '监控系统性能指标',
    icon: '📈',
    category: 'monitoring',
    status: 'healthy',
    actionText: '查看监控'
  },
  {
    id: 'user-session',
    title: '用户会话管理',
    description: '管理在线用户会话',
    icon: '👥',
    category: 'system',
    status: 'healthy',
    actionText: '管理会话'
  }
])

// 备份相关数据
const selectedBackupType = ref('full')
const enableCompression = ref(true)
const backupTypes = [
  { value: 'full', label: '完整备份' },
  { value: 'incremental', label: '增量备份' },
  { value: 'differential', label: '差异备份' }
]

const backupHistory = ref<BackupItem[]>([
  {
    id: '1',
    name: 'backup_2024_01_15_full.sql',
    createTime: '2024-01-15T10:30:00Z',
    size: '245.6 MB'
  },
  {
    id: '2',
    name: 'backup_2024_01_14_incremental.sql',
    createTime: '2024-01-14T10:30:00Z',
    size: '12.3 MB'
  },
  {
    id: '3',
    name: 'backup_2024_01_13_full.sql',
    createTime: '2024-01-13T10:30:00Z',
    size: '238.9 MB'
  }
])

// 日志相关数据
const logLevels = ['DEBUG', 'INFO', 'WARN', 'ERROR']
const selectedLogLevels = ref(['INFO', 'WARN', 'ERROR'])
const selectedTimeRange = ref('1h')
const timeRanges = [
  { value: '15m', label: '15分钟' },
  { value: '1h', label: '1小时' },
  { value: '6h', label: '6小时' },
  { value: '24h', label: '24小时' }
]

const logs = ref<LogEntry[]>([])

// 缓存统计数据
const cacheStats = ref<CacheStats>({
  totalKeys: 1247,
  memoryUsage: '156.8 MB',
  hitRate: 94.2
})

// 清理选项
const cleanupOptions = ref<CleanupOption[]>([
  {
    id: 'temp-files',
    title: '临时文件',
    description: '清理系统临时文件和缓存',
    estimatedSize: '2.3 GB',
    selected: true
  },
  {
    id: 'log-files',
    title: '过期日志',
    description: '删除30天前的日志文件',
    estimatedSize: '856 MB',
    selected: true
  },
  {
    id: 'unused-assets',
    title: '未使用资源',
    description: '清理未引用的图片和文件',
    estimatedSize: '1.2 GB',
    selected: false
  },
  {
    id: 'database-cache',
    title: '数据库缓存',
    description: '清理数据库查询缓存',
    estimatedSize: '445 MB',
    selected: false
  }
])

// 进度信息
const progressInfo = ref({
  title: '',
  message: '',
  percentage: 0
})

// 计算属性
const currentCategoryTools = computed(() => {
  return systemTools.value.filter(tool => tool.category === activeCategory.value)
})

const filteredLogs = computed(() => {
  return logs.value.filter(log => selectedLogLevels.value.includes(log.level))
})

// 方法
const switchCategory = (categoryId: string) => {
  activeCategory.value = categoryId
}

const handleToolClick = (tool: Tool) => {
  if (tool.disabled) return

  selectedTool.value = tool
  showToolModal.value = true

  // 根据工具类型加载相应数据
  if (tool.id === 'log-viewer') {
    fetchLogs()
  } else if (tool.id === 'cache-management') {
    fetchCacheStats()
  }
}

const handleQuickAction = async (tool: Tool, action: { name: string; label: string; icon: string }) => {
  switch (action.name) {
    case 'quick-backup':
      await executeQuickBackup()
      break
    case 'view-history':
      selectedTool.value = tool
      showToolModal.value = true
      break
    case 'clear-cache':
      await clearAllCache()
      break
    case 'refresh-stats':
      await fetchCacheStats()
      break
  }
}

const closeToolModal = () => {
  showToolModal.value = false
  selectedTool.value = null
}

const getStatusText = (status: string) => {
  const statusMap = {
    healthy: '正常',
    warning: '警告',
    error: '错误'
  }
  return statusMap[status] || '未知'
}

const formatTime = (timestamp: string) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now.getTime() - time.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const formatLogTime = (timestamp: string) => {
  const time = new Date(timestamp)
  return time.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 备份相关方法
const executeQuickBackup = async () => {
  showProgressModal.value = true
  progressInfo.value = {
    title: '创建数据库备份',
    message: '正在准备备份...',
    percentage: 0
  }

  try {
    // 模拟备份进度
    for (let i = 0; i <= 100; i += 10) {
      progressInfo.value.percentage = i
      progressInfo.value.message = i < 100 ? `正在备份数据... ${i}%` : '备份完成'
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    // 添加新的备份记录
    const newBackup: BackupItem = {
      id: Date.now().toString(),
      name: `backup_${new Date().toISOString().split('T')[0]}_quick.sql`,
      createTime: new Date().toISOString(),
      size: `${Math.floor(Math.random() * 100) + 200}.${Math.floor(Math.random() * 9)} MB`
    }
    backupHistory.value.unshift(newBackup)

    uni.showToast({
      title: '备份创建成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '备份失败',
      icon: 'error'
    })
  } finally {
    showProgressModal.value = false
  }
}

const downloadBackup = (backup: BackupItem) => {
  uni.showToast({
    title: '开始下载备份文件',
    icon: 'success'
  })
  // 这里应该实现实际的下载逻辑
}

const deleteBackup = (backup: BackupItem) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除备份文件 "${backup.name}" 吗？`,
    success: (res) => {
      if (res.confirm) {
        const index = backupHistory.value.findIndex(b => b.id === backup.id)
        if (index > -1) {
          backupHistory.value.splice(index, 1)
          uni.showToast({
            title: '备份文件已删除',
            icon: 'success'
          })
        }
      }
    }
  })
}

// 日志相关方法
const fetchLogs = async () => {
  try {
    const response = await http({
      url: '/admin/monitor/logs',
      method: 'GET',
      data: {
        levels: selectedLogLevels.value,
        timeRange: selectedTimeRange.value,
        limit: 100
      }
    })

    if (response.code === 200 && response.data) {
      logs.value = response.data
    }
  } catch (error) {
    console.error('获取日志失败:', error)
    // 使用模拟数据
    logs.value = [
      {
        id: '1',
        timestamp: new Date().toISOString(),
        level: 'INFO',
        message: '用户登录成功: <EMAIL>'
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 60000).toISOString(),
        level: 'WARN',
        message: '数据库连接池使用率较高: 85%'
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 120000).toISOString(),
        level: 'ERROR',
        message: 'API请求失败: /api/users/profile - 500 Internal Server Error'
      }
    ]
  }
}

const toggleLogLevel = (level: string) => {
  const index = selectedLogLevels.value.indexOf(level)
  if (index > -1) {
    selectedLogLevels.value.splice(index, 1)
  } else {
    selectedLogLevels.value.push(level)
  }

  // 重新获取日志
  fetchLogs()
}

// 缓存相关方法
const fetchCacheStats = async () => {
  try {
    const response = await http({
      url: '/admin/tools/cache-stats',
      method: 'GET'
    })

    if (response.code === 200 && response.data) {
      cacheStats.value = response.data
    }
  } catch (error) {
    console.error('获取缓存统计失败:', error)
    // 使用模拟数据
    cacheStats.value = {
      totalKeys: Math.floor(Math.random() * 2000) + 1000,
      memoryUsage: `${Math.floor(Math.random() * 200) + 100}.${Math.floor(Math.random() * 9)} MB`,
      hitRate: Math.floor(Math.random() * 20) + 80
    }
  }
}

const clearAllCache = async () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有缓存吗？此操作不可撤销。',
    success: async (res) => {
      if (res.confirm) {
        showProgressModal.value = true
        progressInfo.value = {
          title: '清空缓存',
          message: '正在清空缓存...',
          percentage: 0
        }

        try {
          // 模拟清空进度
          for (let i = 0; i <= 100; i += 20) {
            progressInfo.value.percentage = i
            progressInfo.value.message = i < 100 ? `正在清空缓存... ${i}%` : '清空完成'
            await new Promise(resolve => setTimeout(resolve, 300))
          }

          // 更新缓存统计
          cacheStats.value.totalKeys = 0
          cacheStats.value.memoryUsage = '0 MB'

          uni.showToast({
            title: '缓存清空成功',
            icon: 'success'
          })
        } catch (error) {
          uni.showToast({
            title: '清空缓存失败',
            icon: 'error'
          })
        } finally {
          showProgressModal.value = false
        }
      }
    }
  })
}

const refreshCacheStats = async () => {
  await fetchCacheStats()
  uni.showToast({
    title: '统计已刷新',
    icon: 'success'
  })
}

// 系统清理相关方法
const executeSystemCleanup = async () => {
  const selectedOptions = cleanupOptions.value.filter(option => option.selected)

  if (selectedOptions.length === 0) {
    uni.showToast({
      title: '请选择清理选项',
      icon: 'none'
    })
    return
  }

  showProgressModal.value = true
  progressInfo.value = {
    title: '系统清理',
    message: '正在准备清理...',
    percentage: 0
  }

  try {
    const totalSteps = selectedOptions.length

    for (let i = 0; i < totalSteps; i++) {
      const option = selectedOptions[i]
      progressInfo.value.message = `正在清理: ${option.title}`
      progressInfo.value.percentage = Math.floor((i / totalSteps) * 100)

      // 模拟清理过程
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    progressInfo.value.percentage = 100
    progressInfo.value.message = '清理完成'

    uni.showToast({
      title: '系统清理完成',
      icon: 'success'
    })

    // 重置选择状态
    cleanupOptions.value.forEach(option => {
      option.selected = false
    })

  } catch (error) {
    uni.showToast({
      title: '清理失败',
      icon: 'error'
    })
  } finally {
    showProgressModal.value = false
    closeToolModal()
  }
}

// 执行工具操作
const executeToolAction = async () => {
  if (!selectedTool.value) return

  switch (selectedTool.value.id) {
    case 'database-backup':
      await executeQuickBackup()
      break
    case 'cache-management':
      await clearAllCache()
      break
    case 'system-cleanup':
      await executeSystemCleanup()
      break
    default:
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
  }
}

const getExecuteButtonText = () => {
  if (!selectedTool.value) return '执行'

  switch (selectedTool.value.id) {
    case 'database-backup':
      return '创建备份'
    case 'cache-management':
      return '清空缓存'
    case 'system-cleanup':
      return '开始清理'
    default:
      return '执行'
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
  fetchCacheStats()
})
</script>

<style lang="scss" scoped>
@import '../styles/system-tools-panel.scss';
</style>