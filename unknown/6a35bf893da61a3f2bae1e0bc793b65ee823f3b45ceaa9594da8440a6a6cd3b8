<template>
  <view class="admin-sidebar" :class="{ collapsed: isCollapsed }">
    <!-- Logo区域 -->
    <view class="sidebar-logo">
      <view class="logo-icon">
        <text>🏢</text>
      </view>
      <view v-if="!isCollapsed" class="logo-text">
        <text>管理后台</text>
      </view>
    </view>

    <!-- 菜单区域 -->
    <view class="sidebar-menu">
      <template v-for="item in menuItems" :key="item.id">
        <view 
          v-if="hasPermission(item.permission)"
          class="menu-item-wrapper"
        >
          <!-- 主菜单项 -->
          <view 
            class="menu-item"
            :class="{ 
              active: isActive(item),
              'has-children': item.children && item.children.length > 0
            }"
            @click="handleMenuClick(item)"
          >
            <view class="menu-icon">
              <text>{{ item.icon }}</text>
            </view>
            <view v-if="!isCollapsed" class="menu-title">
              <text>{{ item.title }}</text>
            </view>
            <view 
              v-if="!isCollapsed && item.children && item.children.length > 0" 
              class="menu-arrow"
              :class="{ expanded: expandedMenus.includes(item.id) }"
            >
              <text>▼</text>
            </view>
            <view 
              v-if="item.badge && item.badge > 0" 
              class="menu-badge"
            >
              <text>{{ item.badge > 99 ? '99+' : item.badge }}</text>
            </view>
          </view>

          <!-- 子菜单 -->
          <view 
            v-if="!isCollapsed && item.children && item.children.length > 0"
            class="submenu"
            :class="{ expanded: expandedMenus.includes(item.id) }"
          >
            <view 
              v-for="child in item.children"
              :key="child.id"
              v-show="hasPermission(child.permission)"
              class="menu-item submenu-item"
              :class="{ active: isActive(child) }"
              @click="handleMenuClick(child)"
            >
              <view class="menu-icon">
                <text>{{ child.icon }}</text>
              </view>
              <view class="menu-title">
                <text>{{ child.title }}</text>
              </view>
              <view 
                v-if="child.badge && child.badge > 0" 
                class="menu-badge"
              >
                <text>{{ child.badge > 99 ? '99+' : child.badge }}</text>
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>

    <!-- 底部信息 -->
    <view v-if="!isCollapsed" class="sidebar-footer">
      <view class="version-info">
        <text>v1.0.0</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useUIStore } from '@/stores'
import { useDashboardStore } from '@/stores/dashboard'
import type { MenuItem } from '@/pages/admin/types/admin'

// Props
interface Props {
  collapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false
})

// Stores
const uiStore = useUIStore()
const dashboardStore = useDashboardStore()

// 响应式状态
const expandedMenus = ref<string[]>(['dashboard']) // 默认展开首页
const currentPath = ref('dashboard')
const isMobile = ref(false)

// 计算属性
const isCollapsed = computed(() => props.collapsed || uiStore.sidebarCollapsed)

// 检查是否为移动端
const checkMobile = () => {
  const systemInfo = uni.getSystemInfoSync()
  isMobile.value = systemInfo.windowWidth <= 768
}

// 菜单配置
const menuItems = ref<MenuItem[]>([
  {
    id: 'dashboard',
    title: '首页',
    icon: '📊',
    path: 'dashboard',
    permission: 'dashboard:view'
  },
  {
    id: 'system-function',
    title: '系统功能',
    icon: '⚙️',
    path: 'system-function',
    permission: 'system:manage',
    children: [
      {
        id: 'user-management',
        title: '用户管理',
        icon: '👥',
        path: 'system-function/user-management',
        permission: 'user:manage'
      },
      {
        id: 'role-management',
        title: '角色管理',
        icon: '🔐',
        path: 'system-function/role-management',
        permission: 'role:manage'
      },
      {
        id: 'partner-management',
        title: '伴侣管理',
        icon: '💝',
        path: 'system-function/partner-management',
        permission: 'partner:manage'
      },
      {
        id: 'createChat-management',
        title: '初始聊天管理',
        icon: '💬',
        path: 'system-function/createChat-management',
        permission: 'createChat:manage'
      },
      {
        id: 'permission-management',
        title: '权限管理',
        icon: '🛡️',
        path: 'system-function/permission-management',
        permission: 'permission:manage'
      }
    ]
  },
  {
    id: 'system-monitor',
    title: '系统监控',
    icon: '📈',
    path: 'system-monitor',
    permission: 'monitor:view',
    children: [
      {
        id: 'server-monitor',
        title: '服务器监控',
        icon: '🖥️',
        path: 'system-monitor/server-monitor',
        permission: 'monitor:server'
      },
      {
        id: 'database-monitor',
        title: '数据库监控',
        icon: '🗄️',
        path: 'system-monitor/database-monitor',
        permission: 'monitor:database'
      },
      {
        id: 'application-monitor',
        title: '应用监控',
        icon: '📱',
        path: 'system-monitor/application-monitor',
        permission: 'monitor:application'
      }
    ]
  },
  {
    id: 'system-tools',
    title: '系统工具',
    icon: '🔧',
    path: 'system-tools',
    permission: 'tools:use',
    children: [
      {
        id: 'database-backup',
        title: '数据库备份',
        icon: '💾',
        path: 'system-tools/database-backup',
        permission: 'tools:backup'
      },
      {
        id: 'log-viewer',
        title: '日志查看器',
        icon: '📋',
        path: 'system-tools/log-viewer',
        permission: 'tools:logs'
      },
      {
        id: 'cache-management',
        title: '缓存管理',
        icon: '🗂️',
        path: 'system-tools/cache-management',
        permission: 'tools:cache'
      },
      {
        id: 'system-cleanup',
        title: '系统清理',
        icon: '🧹',
        path: 'system-tools/system-cleanup',
        permission: 'tools:cleanup'
      }
    ]
  }
])

// 方法
const hasPermission = (permission?: string): boolean => {
  if (!permission) return true
  // 简化权限检查，实际项目中应该从用户权限store中获取
  return true
}

const isActive = (item: MenuItem): boolean => {
  return currentPath.value === item.path || currentPath.value.startsWith(item.path + '/')
}

const handleMenuClick = (item: MenuItem) => {
  // 如果有子菜单，切换展开状态
  if (item.children && item.children.length > 0) {
    toggleSubmenu(item.id)
  } else {
    // 设置当前路径
    currentPath.value = item.path

    // 更新面包屑
    updateBreadcrumb(item)

    // 设置当前模块
    dashboardStore.setCurrentModule(item.path)

    // 触发路由切换事件
    handleRouteChange(item.path)

    // 更新浏览器历史状态
    const state = { adminPath: item.path }
    const url = `#/admin/${item.path}`
    window.history.pushState(state, '', url)

    // 在移动端点击菜单后自动收起侧边栏
    if (isMobile.value) {
      uiStore.toggleSidebar()
    }
  }
}

const toggleSubmenu = (menuId: string) => {
  const index = expandedMenus.value.indexOf(menuId)
  if (index > -1) {
    expandedMenus.value.splice(index, 1)
  } else {
    expandedMenus.value.push(menuId)
  }
}

const updateBreadcrumb = (item: MenuItem) => {
  const breadcrumb = []
  
  // 查找父菜单
  const parentMenu = findParentMenu(item.path)
  if (parentMenu) {
    breadcrumb.push({
      title: parentMenu.title,
      path: parentMenu.path
    })
  }
  
  // 添加当前菜单
  breadcrumb.push({
    title: item.title,
    path: item.path
  })
  
  dashboardStore.setBreadcrumb(breadcrumb)
}

const findParentMenu = (path: string): MenuItem | null => {
  for (const menu of menuItems.value) {
    if (menu.children) {
      for (const child of menu.children) {
        if (child.path === path) {
          return menu
        }
      }
    }
  }
  return null
}

const handleRouteChange = (path: string) => {
  // 这里可以实现实际的路由切换逻辑
  // 由于UniApp的路由机制，这里主要是通知父组件进行内容切换
  console.log('切换到路径:', path)
  
  // 可以通过事件总线或者其他方式通知父组件
  uni.$emit('admin-route-change', path)
}

// 初始化
onMounted(() => {
  // 检查移动端
  checkMobile()

  // 设置默认选中的菜单
  currentPath.value = dashboardStore.currentModule || 'dashboard'

  // 展开包含当前路径的菜单
  const parentMenu = findParentMenu(currentPath.value)
  if (parentMenu && !expandedMenus.value.includes(parentMenu.id)) {
    expandedMenus.value.push(parentMenu.id)
  }

  // 监听窗口大小变化
  uni.onWindowResize(checkMobile)
})

// 监听折叠状态变化
const handleCollapseChange = () => {
  if (isCollapsed.value) {
    // 折叠时关闭所有子菜单
    expandedMenus.value = []
  } else {
    // 展开时恢复默认展开状态
    expandedMenus.value = ['dashboard']
    const parentMenu = findParentMenu(currentPath.value)
    if (parentMenu && !expandedMenus.value.includes(parentMenu.id)) {
      expandedMenus.value.push(parentMenu.id)
    }
  }
}

// 监听折叠状态
watch(() => isCollapsed.value, handleCollapseChange)
</script>

<style lang="scss" scoped>
@import '../styles/admin-sidebar.scss';
</style>