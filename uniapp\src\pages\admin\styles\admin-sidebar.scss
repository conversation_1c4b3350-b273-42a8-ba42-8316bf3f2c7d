// 管理系统侧边栏样式
.admin-sidebar {
  width: 100%;
  height: 100%;
  background-color: #001529;
  color: #fff;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  // Logo区域
  .sidebar-logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;

    .logo-icon {
      font-size: 24px;
      margin-right: 8px;
      transition: all 0.3s ease;
    }

    .logo-text {
      font-size: 18px;
      font-weight: 600;
      color: #fff;
      white-space: nowrap;
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  // 菜单区域
  .sidebar-menu {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 8px 0;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }

  // 菜单项包装器
  .menu-item-wrapper {
    margin-bottom: 2px;
  }

  // 菜单项样式
  .menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-height: 48px;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    &.active {
      background-color: #1890ff;
      color: #fff;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: #fff;
      }
    }

    &.has-children {
      .menu-title {
        flex: 1;
      }
    }

    .menu-icon {
      font-size: 18px;
      margin-right: 12px;
      width: 20px;
      text-align: center;
      flex-shrink: 0;
      transition: all 0.3s ease;
    }

    .menu-title {
      font-size: 14px;
      white-space: nowrap;
      opacity: 1;
      transition: opacity 0.3s ease;
      flex: 1;
    }

    .menu-arrow {
      font-size: 12px;
      margin-left: 8px;
      transition: transform 0.3s ease;
      flex-shrink: 0;

      &.expanded {
        transform: rotate(180deg);
      }
    }

    .menu-badge {
      background-color: #ff4d4f;
      color: #fff;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 10px;
      margin-left: 8px;
      min-width: 16px;
      text-align: center;
      flex-shrink: 0;
    }
  }

  // 子菜单样式
  .submenu {
    background-color: rgba(0, 0, 0, 0.2);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &.expanded {
      max-height: 500px; // 足够大的值来容纳子菜单
    }

    .submenu-item {
      padding-left: 48px;
      font-size: 13px;
      min-height: 40px;

      .menu-icon {
        font-size: 14px;
        margin-right: 8px;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.08);
      }

      &.active {
        background-color: rgba(24, 144, 255, 0.8);
      }
    }
  }

  // 底部信息
  .sidebar-footer {
    padding: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    flex-shrink: 0;

    .version-info {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
    }
  }

  // 折叠状态
  &.collapsed {
    .sidebar-logo {
      justify-content: center;

      .logo-icon {
        margin-right: 0;
      }

      .logo-text {
        opacity: 0;
        width: 0;
        overflow: hidden;
      }
    }

    .menu-item {
      justify-content: center;
      padding: 12px 8px;

      .menu-icon {
        margin-right: 0;
      }

      .menu-title,
      .menu-arrow,
      .menu-badge {
        opacity: 0;
        width: 0;
        overflow: hidden;
      }
    }

    .submenu {
      display: none;
    }

    .sidebar-footer {
      display: none;
    }
  }
}

// 响应式布局
@media (max-width: 768px) {
  .admin-sidebar {
    &.collapsed {
      .sidebar-logo .logo-text,
      .menu-item .menu-title,
      .menu-item .menu-arrow,
      .menu-item .menu-badge {
        opacity: 1;
        width: auto;
        overflow: visible;
      }

      .menu-item {
        justify-content: flex-start;
        padding: 12px 16px;

        .menu-icon {
          margin-right: 12px;
        }
      }

      .submenu {
        display: block;
      }

      .sidebar-footer {
        display: block;
      }
    }
  }
}

// 平板适配
@media (min-width: 769px) and (max-width: 1024px) {
  .admin-sidebar {
    .menu-item {
      padding: 10px 12px;
      min-height: 44px;

      .menu-icon {
        font-size: 16px;
      }

      .menu-title {
        font-size: 13px;
      }
    }

    .submenu .submenu-item {
      padding-left: 40px;
      min-height: 36px;
    }
  }
}

// 动画效果
.admin-sidebar {
  * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// 主题支持
.admin-sidebar.dark-theme {
  background-color: #141414;
  
  .sidebar-logo {
    border-bottom-color: rgba(255, 255, 255, 0.05);
  }

  .menu-item {
    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    &.active {
      background-color: #177ddc;
    }
  }

  .submenu {
    background-color: rgba(0, 0, 0, 0.3);

    .submenu-item {
      &:hover {
        background-color: rgba(255, 255, 255, 0.03);
      }

      &.active {
        background-color: rgba(23, 125, 220, 0.8);
      }
    }
  }

  .sidebar-footer {
    border-top-color: rgba(255, 255, 255, 0.05);
  }
}