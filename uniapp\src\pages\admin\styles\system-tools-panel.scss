// 系统工具面板样式
.system-tools-panel {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  // 页面标题
  .panel-header {
    background-color: #fff;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;

    .header-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;

      .title-icon {
        font-size: 24px;
      }

      .title-text {
        font-size: 20px;
        font-weight: 600;
        color: #333;
      }
    }

    .header-description {
      font-size: 14px;
      color: #666;
    }
  }

  // 工具分类导航
  .tools-categories {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
    background-color: #fff;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .category-tab {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      border-radius: 6px;
      background-color: #f8f9fa;
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: #e9ecef;
        color: #333;
      }

      &.active {
        background-color: #1890ff;
        color: #fff;
      }

      .category-icon {
        font-size: 16px;
      }

      .category-name {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  // 工具网格
  .tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;

    .tool-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;

        &:hover {
          transform: none;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .tool-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;

        .tool-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          background-color: #f0f0f0;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: #666;

          &.healthy {
            background-color: #f6ffed;
            color: #52c41a;
          }

          &.warning {
            background-color: #fffbe6;
            color: #faad14;
          }

          &.error {
            background-color: #fff2f0;
            color: #ff4d4f;
          }
        }

        .tool-status {
          display: flex;
          align-items: center;
          gap: 6px;

          .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;

            &.healthy {
              background-color: #52c41a;
            }

            &.warning {
              background-color: #faad14;
            }

            &.error {
              background-color: #ff4d4f;
            }
          }

          .status-text {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .tool-content {
        margin-bottom: 16px;

        .tool-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
        }

        .tool-description {
          font-size: 13px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 12px;
        }

        .tool-meta {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #999;

          .last-used, .tool-size {
            display: block;
          }
        }
      }

      .tool-actions {
        .quick-actions {
          display: flex;
          gap: 8px;
          margin-bottom: 12px;

          .quick-action {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 6px 12px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            background-color: #fafafa;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              border-color: #1890ff;
              color: #1890ff;
              background-color: #f0f8ff;
            }

            .action-icon {
              font-size: 12px;
            }
          }
        }

        .primary-action {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 16px;
          background-color: #1890ff;
          color: #fff;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: #40a9ff;
          }

          .action-text {
            font-size: 14px;
            font-weight: 500;
          }

          .action-arrow {
            font-size: 16px;
          }
        }
      }
    }
  }

  // 工具详情弹窗
  .tool-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .tool-modal {
      background-color: #fff;
      border-radius: 8px;
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;

        .modal-title {
          display: flex;
          align-items: center;
          gap: 12px;

          .modal-icon {
            font-size: 20px;
          }

          .modal-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
          }
        }

        .modal-close {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: #e8e8e8;
          }
        }
      }

      .modal-content {
        flex: 1;
        overflow-y: auto;
        padding: 24px;

        .tool-section {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .section-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 16px;
          }
        }

        // 备份工具样式
        .backup-tool {
          .backup-options {
            .option-item {
              margin-bottom: 16px;

              .option-label {
                display: block;
                font-size: 14px;
                color: #333;
                margin-bottom: 8px;
              }

              .option-select {
                display: flex;
                gap: 8px;

                .select-option {
                  padding: 8px 16px;
                  border: 1px solid #d9d9d9;
                  border-radius: 4px;
                  background-color: #fff;
                  color: #666;
                  cursor: pointer;
                  transition: all 0.3s ease;

                  &:hover {
                    border-color: #1890ff;
                    color: #1890ff;
                  }

                  &.active {
                    background-color: #1890ff;
                    border-color: #1890ff;
                    color: #fff;
                  }
                }
              }

              .option-toggle {
                display: flex;
                align-items: center;
                gap: 12px;
                cursor: pointer;

                .toggle-switch {
                  width: 44px;
                  height: 24px;
                  background-color: #d9d9d9;
                  border-radius: 12px;
                  position: relative;
                  transition: all 0.3s ease;

                  &.active {
                    background-color: #1890ff;
                  }

                  .toggle-handle {
                    width: 20px;
                    height: 20px;
                    background-color: #fff;
                    border-radius: 50%;
                    position: absolute;
                    top: 2px;
                    left: 2px;
                    transition: all 0.3s ease;
                  }

                  &.active .toggle-handle {
                    left: 22px;
                  }
                }

                .toggle-text {
                  font-size: 14px;
                  color: #666;
                }
              }
            }
          }

          .backup-history {
            .backup-item {
              display: flex;
              align-items: center;
              padding: 12px;
              border: 1px solid #f0f0f0;
              border-radius: 6px;
              margin-bottom: 8px;

              .backup-info {
                flex: 1;

                .backup-name {
                  display: block;
                  font-size: 14px;
                  color: #333;
                  margin-bottom: 4px;
                }

                .backup-time {
                  font-size: 12px;
                  color: #999;
                }
              }

              .backup-size {
                font-size: 12px;
                color: #666;
                margin-right: 16px;
              }

              .backup-actions {
                display: flex;
                gap: 8px;

                .backup-action {
                  padding: 4px 8px;
                  border: 1px solid #d9d9d9;
                  border-radius: 4px;
                  background-color: #fff;
                  color: #666;
                  font-size: 12px;
                  cursor: pointer;
                  transition: all 0.3s ease;

                  &:hover {
                    border-color: #1890ff;
                    color: #1890ff;
                  }

                  &.delete {
                    border-color: #ff4d4f;
                    color: #ff4d4f;

                    &:hover {
                      background-color: #ff4d4f;
                      color: #fff;
                    }
                  }
                }
              }
            }
          }
        }

        // 日志查看器样式
        .log-viewer-tool {
          .log-filters {
            .filter-item {
              margin-bottom: 16px;

              .filter-label {
                display: block;
                font-size: 14px;
                color: #333;
                margin-bottom: 8px;
              }

              .filter-tags {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;

                .filter-tag {
                  padding: 6px 12px;
                  border: 1px solid #d9d9d9;
                  border-radius: 4px;
                  background-color: #fff;
                  color: #666;
                  font-size: 12px;
                  cursor: pointer;
                  transition: all 0.3s ease;

                  &:hover {
                    border-color: #1890ff;
                    color: #1890ff;
                  }

                  &.active {
                    background-color: #1890ff;
                    border-color: #1890ff;
                    color: #fff;
                  }
                }
              }

              .time-range {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;

                .time-option {
                  padding: 6px 12px;
                  border: 1px solid #d9d9d9;
                  border-radius: 4px;
                  background-color: #fff;
                  color: #666;
                  font-size: 12px;
                  cursor: pointer;
                  transition: all 0.3s ease;

                  &:hover {
                    border-color: #1890ff;
                    color: #1890ff;
                  }

                  &.active {
                    background-color: #1890ff;
                    border-color: #1890ff;
                    color: #fff;
                  }
                }
              }
            }
          }

          .log-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            background-color: #fafafa;

            .log-entry {
              display: flex;
              align-items: center;
              gap: 12px;
              padding: 8px 12px;
              border-bottom: 1px solid #f0f0f0;
              font-family: 'Courier New', monospace;
              font-size: 12px;

              &:last-child {
                border-bottom: none;
              }

              &.debug {
                background-color: #f6f6f6;

                .log-level {
                  color: #999;
                }
              }

              &.info {
                background-color: #e6f7ff;

                .log-level {
                  color: #1890ff;
                }
              }

              &.warn {
                background-color: #fffbe6;

                .log-level {
                  color: #faad14;
                }
              }

              &.error {
                background-color: #fff2f0;

                .log-level {
                  color: #ff4d4f;
                }
              }

              .log-time {
                color: #999;
                min-width: 80px;
              }

              .log-level {
                font-weight: 600;
                min-width: 50px;
              }

              .log-message {
                flex: 1;
                color: #333;
                word-break: break-all;
              }
            }
          }
        }

        // 缓存管理样式
        .cache-tool {
          .cache-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 16px;

            .stat-item {
              text-align: center;
              padding: 16px;
              background-color: #f8f9fa;
              border-radius: 6px;

              .stat-label {
                display: block;
                font-size: 12px;
                color: #666;
                margin-bottom: 8px;
              }

              .stat-value {
                font-size: 20px;
                font-weight: 600;
                color: #333;
              }
            }
          }

          .cache-operations {
            display: flex;
            gap: 12px;

            .operation-btn {
              flex: 1;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
              padding: 12px 16px;
              border: 1px solid #d9d9d9;
              border-radius: 6px;
              background-color: #fff;
              color: #666;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                border-color: #1890ff;
                color: #1890ff;
              }

              .btn-icon {
                font-size: 16px;
              }

              .btn-text {
                font-size: 14px;
              }
            }
          }
        }

        // 系统清理样式
        .cleanup-tool {
          .cleanup-options {
            .cleanup-option {
              display: flex;
              align-items: flex-start;
              gap: 12px;
              padding: 16px;
              border: 1px solid #f0f0f0;
              border-radius: 6px;
              margin-bottom: 12px;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                border-color: #d9d9d9;
              }

              &.selected {
                border-color: #1890ff;
                background-color: #f0f8ff;
              }

              .option-checkbox {
                width: 18px;
                height: 18px;
                border: 2px solid #d9d9d9;
                border-radius: 3px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: #fff;
                transition: all 0.3s ease;
              }

              &.selected .option-checkbox {
                background-color: #1890ff;
                border-color: #1890ff;
              }

              .option-info {
                flex: 1;

                .option-title {
                  display: block;
                  font-size: 14px;
                  font-weight: 500;
                  color: #333;
                  margin-bottom: 4px;
                }

                .option-desc {
                  display: block;
                  font-size: 12px;
                  color: #666;
                  margin-bottom: 4px;
                }

                .option-size {
                  font-size: 12px;
                  color: #1890ff;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }

      .modal-footer {
        padding: 16px 24px;
        border-top: 1px solid #f0f0f0;

        .footer-actions {
          display: flex;
          justify-content: flex-end;
          gap: 12px;

          .action-btn {
            padding: 8px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;

            &.secondary {
              border: 1px solid #d9d9d9;
              background-color: #fff;
              color: #666;

              &:hover {
                border-color: #1890ff;
                color: #1890ff;
              }
            }

            &.primary {
              border: 1px solid #1890ff;
              background-color: #1890ff;
              color: #fff;

              &:hover {
                background-color: #40a9ff;
                border-color: #40a9ff;
              }
            }
          }
        }
      }
    }
  }

  // 进度弹窗
  .progress-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;

    .progress-modal {
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      min-width: 300px;

      .progress-header {
        margin-bottom: 20px;

        .progress-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          text-align: center;
        }
      }

      .progress-content {
        .progress-bar {
          width: 100%;
          height: 8px;
          background-color: #f0f0f0;
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 12px;

          .progress-fill {
            height: 100%;
            background-color: #1890ff;
            transition: width 0.3s ease;
          }
        }

        .progress-text {
          font-size: 14px;
          color: #666;
          text-align: center;
          margin-bottom: 8px;
        }

        .progress-percentage {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          text-align: center;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .system-tools-panel {
    padding: 12px;

    .panel-header {
      padding: 16px;
    }

    .tools-categories {
      padding: 12px;
      flex-wrap: wrap;

      .category-tab {
        padding: 8px 12px;

        .category-name {
          font-size: 12px;
        }
      }
    }

    .tools-grid {
      grid-template-columns: 1fr;
      gap: 16px;

      .tool-card {
        padding: 16px;

        .tool-header {
          .tool-icon {
            width: 40px;
            height: 40px;
            font-size: 20px;
          }
        }

        .tool-actions {
          .quick-actions {
            flex-wrap: wrap;
          }
        }
      }
    }

    .tool-modal-overlay {
      .tool-modal {
        width: 95%;
        max-height: 90vh;

        .modal-header {
          padding: 16px;
        }

        .modal-content {
          padding: 16px;

          .cache-tool {
            .cache-stats {
              grid-template-columns: 1fr;
              gap: 12px;
            }

            .cache-operations {
              flex-direction: column;
            }
          }

          .log-viewer-tool {
            .log-filters {
              .filter-tags, .time-range {
                gap: 6px;
              }
            }

            .log-container {
              max-height: 200px;

              .log-entry {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;

                .log-time, .log-level {
                  min-width: auto;
                }
              }
            }
          }

          .backup-tool {
            .backup-options {
              .option-item {
                .option-select {
                  flex-wrap: wrap;
                }
              }
            }

            .backup-history {
              .backup-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;

                .backup-actions {
                  align-self: flex-end;
                }
              }
            }
          }
        }

        .modal-footer {
          padding: 12px 16px;

          .footer-actions {
            flex-direction: column;
            gap: 8px;

            .action-btn {
              width: 100%;
              text-align: center;
            }
          }
        }
      }
    }

    .progress-modal-overlay {
      .progress-modal {
        margin: 20px;
        min-width: auto;
        width: calc(100% - 40px);
      }
    }
  }
}

// 小屏幕适配
@media (max-width: 480px) {
  .system-tools-panel {
    padding: 8px;

    .panel-header {
      padding: 12px;

      .header-title {
        .title-text {
          font-size: 18px;
        }
      }
    }

    .tools-categories {
      padding: 8px;

      .category-tab {
        padding: 6px 8px;
        flex-direction: column;
        gap: 4px;

        .category-icon {
          font-size: 14px;
        }

        .category-name {
          font-size: 10px;
        }
      }
    }

    .tools-grid {
      gap: 12px;

      .tool-card {
        padding: 12px;

        .tool-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          .tool-status {
            align-self: flex-end;
          }
        }

        .tool-content {
          .tool-title {
            font-size: 14px;
          }

          .tool-description {
            font-size: 12px;
          }
        }
      }
    }
  }
}