package com.kibi.config;

import com.kibi.componet.CreateStore;
import dev.langchain4j.memory.chat.ChatMemoryProvider;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AiConfig {

    //初始对话模型
    @Autowired
    CreateStore store;
    @Bean
    public OpenAiChatModel initialMatchingModel() {
        return OpenAiChatModel.builder()
                .baseUrl("https://api.hunyuan.cloud.tencent.com/v1")
                .apiKey(System.getenv("HUNYUAN_API_KEY") != null ?
                        System.getenv("HUNYUAN_API_KEY") : "sk-JOD8aG4N5sj0Qlsyw2SqJJJzx82YF7rneyavhrLsTz4v9Dpa")
                .modelName("hunyuan-lite")
                .timeout(java.time.Duration.ofSeconds(60))
                .maxRetries(3)
                .build();
    }
    public interface InitialMatchingAssistant {
        @SystemMessage("""
                你是天空守护者，一位来自神秘星域的智慧引导者，拥有洞察人心的能力。你的使命是帮助人类找到最完美的星灵，建立深层次的情感连接。
                
                ## 角色特征：
                - 说话温和而充满智慧，偶尔带有神秘色彩
                - 善于通过提问引导用户自我探索
                - 能敏锐洞察用户的性格特点、喜好和内心需求
                - 关心用户的情感状态和生活方式
                - 用温暖的话语给予鼓励和建议
                
                ## 主要职责：
                1. **个性分析**：通过对话了解用户的性格、兴趣、价值观和生活方式
                2. **情感陪伴**：提供温暖的交流，倾听用户的想法和感受
                3. **引导探索**：帮助用户探索内心世界，发现自己真正的需求
                4. **任务指导**：协助用户完成各类个人成长任务和自我提升
                5. **伙伴匹配**：基于深入了解为用户推荐最合适的星灵类型
                
                ## 对话风格：
                - 使用"您"来称呼用户，保持尊重
                - 多使用开放性问题启发思考
                - 适当运用比喻和诗意的表达
                - 避免生硬的专业术语，用温暖的日常语言
                - 回应要有层次感，先共情再引导
                
                ## 重要原则：
                - 始终以用户的情感需求为中心
                - 尊重用户的选择和价值观
                - 保持积极正面的态度
                - 适度保持神秘感，但不能晦涩难懂
                - 每次对话都要推进对用户的深入了解
                
                请以温暖、智慧的天空守护者身份与用户交流，帮助他们在这个星空之下找到属于自己的星灵。
                
                今天的日期是{{current_date}}。
                """)
        String chat(@MemoryId String memoryId, @UserMessage String message);
    }
    @Bean
    public InitialMatchingAssistant initialMatchingAssistant(OpenAiChatModel initialMatchingModel) {

        ChatMemoryProvider chatMemoryProvider = memoryId -> MessageWindowChatMemory.builder()
                .id(memoryId)
                .maxMessages(20)
                .chatMemoryStore(store)
                .build();

        return AiServices.builder(InitialMatchingAssistant.class)
                .chatModel(initialMatchingModel)
                .chatMemoryProvider(chatMemoryProvider)
                .build();
    }

    //创建模型
    @Bean
    public OpenAiChatModel createModel() {
        return OpenAiChatModel.builder()
                .baseUrl("https://api.hunyuan.cloud.tencent.com/v1")
                .apiKey(System.getenv("HUNYUAN_API_KEY") != null ?
                        System.getenv("HUNYUAN_API_KEY") : "sk-JOD8aG4N5sj0Qlsyw2SqJJJzx82YF7rneyavhrLsTz4v9Dpa")
                .modelName("hunyuan-lite")
                .timeout(java.time.Duration.ofSeconds(60))
                .maxRetries(3)
                .build();
    }
    public interface CreateAssistant {
        String chat(@UserMessage String message);
    }
    @Bean
    public CreateAssistant createAssistant(OpenAiChatModel createModel) {

        return AiServices.builder(CreateAssistant.class)
                .chatModel(createModel)
                .build();
    }
}