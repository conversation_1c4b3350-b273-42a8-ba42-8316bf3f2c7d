<template>
  <view class="page-transition" :class="{ 'is-transitioning': isTransitioning }">
    <transition 
      :name="transitionName" 
      mode="out-in"
      @before-enter="onBeforeEnter"
      @enter="onEnter"
      @after-enter="onAfterEnter"
      @before-leave="onBeforeLeave"
      @leave="onLeave"
      @after-leave="onAfterLeave"
    >
      <slot :key="currentKey" />
    </transition>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// Props
interface Props {
  currentKey: string | number
  direction?: 'left' | 'right' | 'up' | 'down' | 'fade'
  duration?: number
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'left',
  duration: 300,
  disabled: false
})

// Emits
const emit = defineEmits<{
  'transition-start': []
  'transition-end': []
}>()

// 响应式数据
const isTransitioning = ref(false)
const previousKey = ref(props.currentKey)

// 计算属性
const transitionName = computed(() => {
  if (props.disabled) return 'fade'
  return `slide-${props.direction}`
})

// 监听key变化
watch(() => props.currentKey, (newKey, oldKey) => {
  if (newKey !== oldKey) {
    previousKey.value = oldKey
  }
})

// 过渡事件处理
const onBeforeEnter = () => {
  isTransitioning.value = true
  emit('transition-start')
}

const onEnter = () => {
  // 进入动画开始
}

const onAfterEnter = () => {
  isTransitioning.value = false
  emit('transition-end')
}

const onBeforeLeave = () => {
  // 离开动画准备
}

const onLeave = () => {
  // 离开动画开始
}

const onAfterLeave = () => {
  // 离开动画结束
}
</script>

<style lang="scss" scoped>
.page-transition {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  &.is-transitioning {
    pointer-events: none;
  }
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 左右滑动动画
.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 上下滑动动画
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-down-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}

.slide-down-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

// 移动端优化
@media (max-width: 768px) {
  .slide-left-enter-active,
  .slide-left-leave-active,
  .slide-right-enter-active,
  .slide-right-leave-active,
  .slide-up-enter-active,
  .slide-up-leave-active,
  .slide-down-enter-active,
  .slide-down-leave-active {
    transition: transform 0.25s ease-out, opacity 0.25s ease-out;
  }
}

// 减少动画（用户偏好）
@media (prefers-reduced-motion: reduce) {
  .slide-left-enter-active,
  .slide-left-leave-active,
  .slide-right-enter-active,
  .slide-right-leave-active,
  .slide-up-enter-active,
  .slide-up-leave-active,
  .slide-down-enter-active,
  .slide-down-leave-active {
    transition: opacity 0.2s ease;
  }

  .slide-left-enter-from,
  .slide-left-leave-to,
  .slide-right-enter-from,
  .slide-right-leave-to,
  .slide-up-enter-from,
  .slide-up-leave-to,
  .slide-down-enter-from,
  .slide-down-leave-to {
    transform: none;
  }
}

// 高性能模式（低端设备）
@media (max-width: 480px) and (max-height: 800px) {
  .page-transition {
    .slide-left-enter-active,
    .slide-left-leave-active,
    .slide-right-enter-active,
    .slide-right-leave-active,
    .slide-up-enter-active,
    .slide-up-leave-active,
    .slide-down-enter-active,
    .slide-down-leave-active {
      transition: opacity 0.2s ease;
    }

    .slide-left-enter-from,
    .slide-left-leave-to,
    .slide-right-enter-from,
    .slide-right-leave-to,
    .slide-up-enter-from,
    .slide-up-leave-to,
    .slide-down-enter-from,
    .slide-down-leave-to {
      transform: none;
    }
  }
}
</style>
