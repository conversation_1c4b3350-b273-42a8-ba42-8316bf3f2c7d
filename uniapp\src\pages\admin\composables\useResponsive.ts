/**
 * 响应式设计工具
 * 提供设备检测、屏幕尺寸监听等功能
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'

export interface ScreenInfo {
  width: number
  height: number
  pixelRatio: number
  platform: string
  system: string
}

export interface DeviceType {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isSmallScreen: boolean
  isMediumScreen: boolean
  isLargeScreen: boolean
}

export interface Breakpoints {
  xs: number  // 超小屏幕
  sm: number  // 小屏幕
  md: number  // 中等屏幕
  lg: number  // 大屏幕
  xl: number  // 超大屏幕
}

// 默认断点配置
const defaultBreakpoints: Breakpoints = {
  xs: 480,
  sm: 768,
  md: 1024,
  lg: 1200,
  xl: 1600
}

export function useResponsive(customBreakpoints?: Partial<Breakpoints>) {
  // 合并断点配置
  const breakpoints = { ...defaultBreakpoints, ...customBreakpoints }
  
  // 响应式状态
  const screenInfo = ref<ScreenInfo>({
    width: 0,
    height: 0,
    pixelRatio: 1,
    platform: '',
    system: ''
  })

  // 计算设备类型
  const deviceType = computed<DeviceType>(() => {
    const width = screenInfo.value.width
    
    return {
      isMobile: width <= breakpoints.sm,
      isTablet: width > breakpoints.sm && width <= breakpoints.md,
      isDesktop: width > breakpoints.md,
      isSmallScreen: width <= breakpoints.xs,
      isMediumScreen: width > breakpoints.xs && width <= breakpoints.lg,
      isLargeScreen: width > breakpoints.lg
    }
  })

  // 当前断点
  const currentBreakpoint = computed(() => {
    const width = screenInfo.value.width
    
    if (width <= breakpoints.xs) return 'xs'
    if (width <= breakpoints.sm) return 'sm'
    if (width <= breakpoints.md) return 'md'
    if (width <= breakpoints.lg) return 'lg'
    return 'xl'
  })

  // 屏幕方向
  const orientation = computed(() => {
    const { width, height } = screenInfo.value
    return width > height ? 'landscape' : 'portrait'
  })

  // 是否为触摸设备
  const isTouchDevice = computed(() => {
    return screenInfo.value.platform.includes('mobile') || 
           screenInfo.value.platform.includes('tablet')
  })

  /**
   * 更新屏幕信息
   */
  const updateScreenInfo = () => {
    try {
      const systemInfo = uni.getSystemInfoSync()
      screenInfo.value = {
        width: systemInfo.windowWidth,
        height: systemInfo.windowHeight,
        pixelRatio: systemInfo.pixelRatio,
        platform: systemInfo.platform,
        system: systemInfo.system
      }
    } catch (error) {
      console.warn('Failed to get system info:', error)
    }
  }

  /**
   * 监听屏幕尺寸变化
   */
  const handleResize = () => {
    updateScreenInfo()
  }

  /**
   * 检查是否匹配指定断点
   */
  const matchBreakpoint = (breakpoint: keyof Breakpoints | string) => {
    if (typeof breakpoint === 'string' && breakpoint in breakpoints) {
      return screenInfo.value.width <= breakpoints[breakpoint as keyof Breakpoints]
    }
    return false
  }

  /**
   * 检查是否在指定范围内
   */
  const matchRange = (min?: number, max?: number) => {
    const width = screenInfo.value.width
    
    if (min !== undefined && max !== undefined) {
      return width >= min && width <= max
    } else if (min !== undefined) {
      return width >= min
    } else if (max !== undefined) {
      return width <= max
    }
    
    return true
  }

  /**
   * 获取响应式值
   */
  const getResponsiveValue = <T>(values: {
    xs?: T
    sm?: T
    md?: T
    lg?: T
    xl?: T
    default: T
  }): T => {
    const bp = currentBreakpoint.value
    return values[bp as keyof typeof values] ?? values.default
  }

  /**
   * 获取响应式类名
   */
  const getResponsiveClass = (baseClass: string) => {
    const classes = [baseClass]
    
    // 添加设备类型类名
    if (deviceType.value.isMobile) classes.push(`${baseClass}--mobile`)
    if (deviceType.value.isTablet) classes.push(`${baseClass}--tablet`)
    if (deviceType.value.isDesktop) classes.push(`${baseClass}--desktop`)
    
    // 添加断点类名
    classes.push(`${baseClass}--${currentBreakpoint.value}`)
    
    // 添加方向类名
    classes.push(`${baseClass}--${orientation.value}`)
    
    return classes.join(' ')
  }

  /**
   * 获取网格列数
   */
  const getGridColumns = (config: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    default: number
  }) => {
    return getResponsiveValue(config)
  }

  /**
   * 获取间距大小
   */
  const getSpacing = (config: {
    xs?: number | string
    sm?: number | string
    md?: number | string
    lg?: number | string
    xl?: number | string
    default: number | string
  }) => {
    return getResponsiveValue(config)
  }

  /**
   * 检查是否需要显示移动端布局
   */
  const shouldUseMobileLayout = computed(() => {
    return deviceType.value.isMobile || screenInfo.value.width <= 600
  })

  /**
   * 检查是否需要紧凑布局
   */
  const shouldUseCompactLayout = computed(() => {
    return deviceType.value.isMobile || deviceType.value.isTablet
  })

  /**
   * 获取安全区域信息
   */
  const getSafeArea = () => {
    try {
      const systemInfo = uni.getSystemInfoSync()
      return {
        top: systemInfo.safeAreaInsets?.top || 0,
        bottom: systemInfo.safeAreaInsets?.bottom || 0,
        left: systemInfo.safeAreaInsets?.left || 0,
        right: systemInfo.safeAreaInsets?.right || 0
      }
    } catch (error) {
      console.warn('Failed to get safe area:', error)
      return { top: 0, bottom: 0, left: 0, right: 0 }
    }
  }

  // 生命周期
  onMounted(() => {
    updateScreenInfo()
    
    // 监听窗口大小变化
    uni.onWindowResize(handleResize)
  })

  onUnmounted(() => {
    // 移除监听器
    uni.offWindowResize(handleResize)
  })

  return {
    // 状态
    screenInfo: computed(() => screenInfo.value),
    deviceType,
    currentBreakpoint,
    orientation,
    isTouchDevice,
    shouldUseMobileLayout,
    shouldUseCompactLayout,
    
    // 方法
    updateScreenInfo,
    matchBreakpoint,
    matchRange,
    getResponsiveValue,
    getResponsiveClass,
    getGridColumns,
    getSpacing,
    getSafeArea,
    
    // 断点配置
    breakpoints
  }
}

// 创建全局响应式实例
export const globalResponsive = useResponsive()
