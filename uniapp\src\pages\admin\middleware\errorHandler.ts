/**
 * 全局错误处理中间件
 * 提供统一的错误处理、用户友好的错误提示和网络错误处理
 */

export interface ErrorInfo {
  code?: string | number
  message: string
  type: 'network' | 'api' | 'validation' | 'permission' | 'system' | 'timeout'
  details?: any
  timestamp: number
  url?: string
  method?: string
}

export interface ErrorHandlerOptions {
  showToast?: boolean
  showModal?: boolean
  logError?: boolean
  redirectOnAuth?: boolean
  retryable?: boolean
}

class ErrorHandler {
  private errorLog: ErrorInfo[] = []
  private maxLogSize = 100
  private retryAttempts = new Map<string, number>()
  private maxRetries = 3

  /**
   * 处理错误
   */
  handle(error: any, options: ErrorHandlerOptions = {}): ErrorInfo {
    const errorInfo = this.parseError(error)
    
    // 默认选项
    const defaultOptions: ErrorHandlerOptions = {
      showToast: true,
      showModal: false,
      logError: true,
      redirectOnAuth: true,
      retryable: false
    }
    
    const finalOptions = { ...defaultOptions, ...options }
    
    // 记录错误日志
    if (finalOptions.logError) {
      this.logError(errorInfo)
    }
    
    // 显示用户提示
    this.showUserFeedback(errorInfo, finalOptions)
    
    // 处理特殊错误类型
    this.handleSpecialErrors(errorInfo, finalOptions)
    
    return errorInfo
  }

  /**
   * 解析错误信息
   */
  private parseError(error: any): ErrorInfo {
    const timestamp = Date.now()
    
    // 网络错误
    if (error.errMsg && error.errMsg.includes('request:fail')) {
      return {
        type: 'network',
        message: this.getNetworkErrorMessage(error.errMsg),
        details: error,
        timestamp
      }
    }
    
    // API响应错误
    if (error.statusCode) {
      return {
        type: 'api',
        code: error.statusCode,
        message: this.getApiErrorMessage(error.statusCode, error.data),
        details: error,
        timestamp,
        url: error.config?.url,
        method: error.config?.method
      }
    }
    
    // 超时错误
    if (error.errMsg && error.errMsg.includes('timeout')) {
      return {
        type: 'timeout',
        message: '请求超时，请检查网络连接',
        details: error,
        timestamp
      }
    }
    
    // 权限错误
    if (error.message && error.message.includes('权限')) {
      return {
        type: 'permission',
        message: error.message,
        details: error,
        timestamp
      }
    }
    
    // 验证错误
    if (error.name === 'ValidationError' || error.type === 'validation') {
      return {
        type: 'validation',
        message: error.message || '数据验证失败',
        details: error,
        timestamp
      }
    }
    
    // 系统错误
    return {
      type: 'system',
      message: error.message || '系统错误，请稍后重试',
      details: error,
      timestamp
    }
  }

  /**
   * 获取网络错误消息
   */
  private getNetworkErrorMessage(errMsg: string): string {
    if (errMsg.includes('timeout')) {
      return '网络连接超时，请检查网络设置'
    }
    if (errMsg.includes('fail')) {
      return '网络连接失败，请检查网络设置'
    }
    if (errMsg.includes('abort')) {
      return '请求已取消'
    }
    return '网络错误，请稍后重试'
  }

  /**
   * 获取API错误消息
   */
  private getApiErrorMessage(statusCode: number, data?: any): string {
    const message = data?.message || data?.msg
    
    switch (statusCode) {
      case 400:
        return message || '请求参数错误'
      case 401:
        return '登录已过期，请重新登录'
      case 403:
        return '没有权限执行此操作'
      case 404:
        return '请求的资源不存在'
      case 408:
        return '请求超时，请稍后重试'
      case 429:
        return '请求过于频繁，请稍后重试'
      case 500:
        return '服务器内部错误'
      case 502:
        return '网关错误，请稍后重试'
      case 503:
        return '服务暂时不可用，请稍后重试'
      case 504:
        return '网关超时，请稍后重试'
      default:
        return message || `请求失败 (${statusCode})`
    }
  }

  /**
   * 显示用户反馈
   */
  private showUserFeedback(errorInfo: ErrorInfo, options: ErrorHandlerOptions) {
    if (options.showModal) {
      uni.showModal({
        title: '错误提示',
        content: errorInfo.message,
        showCancel: false,
        confirmText: '确定'
      })
    } else if (options.showToast) {
      const icon = this.getToastIcon(errorInfo.type)
      uni.showToast({
        title: errorInfo.message,
        icon: icon,
        duration: this.getToastDuration(errorInfo.type),
        mask: false
      })
    }
  }

  /**
   * 获取Toast图标
   */
  private getToastIcon(type: string): 'success' | 'error' | 'loading' | 'none' {
    switch (type) {
      case 'network':
      case 'timeout':
      case 'api':
      case 'system':
        return 'error'
      case 'validation':
      case 'permission':
        return 'none'
      default:
        return 'none'
    }
  }

  /**
   * 获取Toast显示时长
   */
  private getToastDuration(type: string): number {
    switch (type) {
      case 'validation':
        return 2000
      case 'permission':
        return 3000
      case 'network':
      case 'timeout':
      case 'api':
      case 'system':
        return 4000
      default:
        return 3000
    }
  }

  /**
   * 处理特殊错误类型
   */
  private handleSpecialErrors(errorInfo: ErrorInfo, options: ErrorHandlerOptions) {
    // 处理认证错误
    if (errorInfo.code === 401 && options.redirectOnAuth) {
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/login/login'
        })
      }, 1500)
    }
    
    // 处理权限错误
    if (errorInfo.type === 'permission') {
      // 可以跳转到无权限页面或显示权限申请
      console.warn('Permission denied:', errorInfo.message)
    }
  }

  /**
   * 记录错误日志
   */
  private logError(errorInfo: ErrorInfo) {
    // 添加到内存日志
    this.errorLog.unshift(errorInfo)
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize)
    }
    
    // 持久化重要错误
    if (this.shouldPersistError(errorInfo)) {
      this.persistError(errorInfo)
    }
    
    // 控制台输出
    console.error('Error handled:', errorInfo)
  }

  /**
   * 判断是否需要持久化错误
   */
  private shouldPersistError(errorInfo: ErrorInfo): boolean {
    return errorInfo.type === 'system' || 
           errorInfo.type === 'api' && errorInfo.code === 500 ||
           errorInfo.type === 'network'
  }

  /**
   * 持久化错误到本地存储
   */
  private persistError(errorInfo: ErrorInfo) {
    try {
      const persistedErrors = uni.getStorageSync('admin_error_log') || []
      persistedErrors.unshift(errorInfo)
      
      // 只保留最近50条错误
      const limitedErrors = persistedErrors.slice(0, 50)
      uni.setStorageSync('admin_error_log', limitedErrors)
    } catch (e) {
      console.warn('Failed to persist error:', e)
    }
  }

  /**
   * 获取错误日志
   */
  getErrorLog(): ErrorInfo[] {
    return [...this.errorLog]
  }

  /**
   * 获取持久化的错误日志
   */
  getPersistedErrorLog(): ErrorInfo[] {
    try {
      return uni.getStorageSync('admin_error_log') || []
    } catch (e) {
      console.warn('Failed to get persisted error log:', e)
      return []
    }
  }

  /**
   * 清除错误日志
   */
  clearErrorLog() {
    this.errorLog = []
    try {
      uni.removeStorageSync('admin_error_log')
    } catch (e) {
      console.warn('Failed to clear persisted error log:', e)
    }
  }

  /**
   * 重试机制
   */
  async retry<T>(
    operation: () => Promise<T>,
    key: string,
    maxRetries: number = this.maxRetries
  ): Promise<T> {
    const attempts = this.retryAttempts.get(key) || 0
    
    try {
      const result = await operation()
      // 成功后清除重试计数
      this.retryAttempts.delete(key)
      return result
    } catch (error) {
      if (attempts < maxRetries) {
        this.retryAttempts.set(key, attempts + 1)
        
        // 指数退避延迟
        const delay = Math.pow(2, attempts) * 1000
        await new Promise(resolve => setTimeout(resolve, delay))
        
        return this.retry(operation, key, maxRetries)
      } else {
        // 达到最大重试次数，清除计数并抛出错误
        this.retryAttempts.delete(key)
        throw error
      }
    }
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler()

// 便捷方法
export const handleError = (error: any, options?: ErrorHandlerOptions) => {
  return errorHandler.handle(error, options)
}

export const retryOperation = <T>(
  operation: () => Promise<T>,
  key: string,
  maxRetries?: number
) => {
  return errorHandler.retry(operation, key, maxRetries)
}
