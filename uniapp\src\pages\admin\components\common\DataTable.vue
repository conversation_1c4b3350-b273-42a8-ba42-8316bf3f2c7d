<template>
  <view class="data-table">
    <!-- 表格头部 -->
    <view class="table-header">
      <view class="table-title">{{ title }}</view>
      <view class="table-actions">
        <!-- 搜索框 -->
        <input 
          v-if="searchable"
          v-model="searchKeyword"
          class="search-input"
          :placeholder="searchPlaceholder"
          @input="handleSearch"
        />
        
        <!-- 批量操作 -->
        <view v-if="selectedRows.length > 0" class="batch-actions">
          <button 
            v-for="action in batchActions" 
            :key="action.type"
            class="action-btn"
            @click="handleBatchAction(action)"
          >
            {{ action.label }}
          </button>
        </view>
        
        <!-- 操作按钮 -->
        <button 
          v-for="action in actions.filter(a => a.type === 'add')" 
          :key="action.type"
          class="action-btn primary"
          @click="action.handler()"
        >
          {{ action.label }}
        </button>
      </view>
    </view>

    <!-- 表格容器 -->
    <view class="table-container">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <view class="loading-spinner"></view>
        <view>加载中...</view>
      </view>
      
      <!-- 空状态 -->
      <view v-else-if="!data.length" class="empty-state">
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无数据</view>
        <button 
          v-if="actions.some(a => a.type === 'add')"
          class="empty-action"
          @click="actions.find(a => a.type === 'add')?.handler()"
        >
          添加数据
        </button>
      </view>
      
      <!-- 数据表格 -->
      <table v-else class="table">
        <thead class="table-head">
          <tr class="table-row">
            <!-- 批量选择列 -->
            <th v-if="selectable" class="table-cell">
              <input 
                type="checkbox" 
                :checked="isAllSelected"
                @change="handleSelectAll"
              />
            </th>
            
            <!-- 数据列 -->
            <th 
              v-for="column in columns" 
              :key="column.key"
              class="table-cell"
              :class="{ sortable: column.sortable && sortable }"
              :style="{ width: column.width }"
              @click="handleSort(column)"
            >
              {{ column.title }}
              <span 
                v-if="column.sortable && sortable"
                class="sort-icon"
                :class="{ active: sortField === column.key }"
              >
                {{ getSortIcon(column.key) }}
              </span>
            </th>
            
            <!-- 操作列 -->
            <th v-if="hasRowActions" class="table-cell">操作</th>
          </tr>
        </thead>
        
        <tbody class="table-body">
          <tr 
            v-for="(item, index) in paginatedData" 
            :key="getRowKey(item, index)"
            class="table-row"
          >
            <!-- 批量选择列 -->
            <td v-if="selectable" class="table-cell">
              <input 
                type="checkbox" 
                :checked="selectedRows.includes(getRowKey(item, index))"
                @change="handleRowSelect(item, index)"
              />
            </td>
            
            <!-- 数据列 -->
            <td 
              v-for="column in columns" 
              :key="column.key"
              class="table-cell"
            >
              <span v-if="column.render">
                {{ column.render(item[column.key], item) }}
              </span>
              <span v-else>{{ item[column.key] }}</span>
            </td>
            
            <!-- 操作列 -->
            <td v-if="hasRowActions" class="table-cell actions">
              <button 
                v-for="action in rowActions" 
                :key="action.type"
                class="action-btn"
                :class="{ danger: action.type === 'delete' }"
                @click="action.handler(item)"
              >
                {{ action.label }}
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </view>

    <!-- 表格底部分页 -->
    <view v-if="pagination && !loading && data.length" class="table-footer">
      <view class="pagination-info">
        共 {{ pagination.total }} 条记录，第 {{ pagination.current }} / {{ totalPages }} 页
      </view>
      
      <view class="pagination-controls">
        <!-- 每页大小选择器 -->
        <select 
          v-if="pagination.showSizeChanger"
          v-model="currentPageSize"
          class="page-size-selector"
          @change="handlePageSizeChange"
        >
          <option v-for="size in pageSizeOptions" :key="size" :value="size">
            {{ size }} 条/页
          </option>
        </select>
        
        <!-- 分页按钮 -->
        <button 
          class="pagination-btn"
          :disabled="pagination.current <= 1"
          @click="handlePageChange(1)"
        >
          首页
        </button>
        
        <button 
          class="pagination-btn"
          :disabled="pagination.current <= 1"
          @click="handlePageChange(pagination.current - 1)"
        >
          上一页
        </button>
        
        <button 
          v-for="page in visiblePages" 
          :key="page"
          class="pagination-btn"
          :class="{ active: page === pagination.current }"
          @click="handlePageChange(page)"
        >
          {{ page }}
        </button>
        
        <button 
          class="pagination-btn"
          :disabled="pagination.current >= totalPages"
          @click="handlePageChange(pagination.current + 1)"
        >
          下一页
        </button>
        
        <button 
          class="pagination-btn"
          :disabled="pagination.current >= totalPages"
          @click="handlePageChange(totalPages)"
        >
          末页
        </button>
        
        <!-- 快速跳转 -->
        <view v-if="pagination.showQuickJumper" class="page-jump">
          跳至
          <input 
            v-model="jumpPage"
            class="jump-input"
            type="number"
            :min="1"
            :max="totalPages"
            @keyup.enter="handlePageJump"
          />
          页
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { DataTableProps, TableAction } from '../../types/admin'

// Props定义
interface Props extends DataTableProps {
  title?: string
  searchPlaceholder?: string
  selectable?: boolean
  batchActions?: TableAction[]
  rowKey?: string | ((record: any, index: number) => string | number)
}

const props = withDefaults(defineProps<Props>(), {
  title: '数据列表',
  searchPlaceholder: '请输入搜索关键词',
  selectable: false,
  batchActions: () => [],
  rowKey: 'id'
})

// Emits定义
const emit = defineEmits<{
  search: [keyword: string]
  sort: [field: string, order: 'asc' | 'desc']
  pageChange: [page: number]
  pageSizeChange: [pageSize: number]
  batchAction: [action: TableAction, selectedItems: any[]]
}>()

// 响应式数据
const searchKeyword = ref('')
const sortField = ref('')
const sortOrder = ref<'asc' | 'desc'>('asc')
const selectedRows = ref<(string | number)[]>([])
const currentPageSize = ref(props.pagination.pageSize)
const jumpPage = ref<number>()

// 计算属性
const filteredData = computed(() => {
  if (!searchKeyword.value) return props.data
  
  return props.data.filter(item => {
    return props.columns.some(column => {
      const value = item[column.key]
      return String(value).toLowerCase().includes(searchKeyword.value.toLowerCase())
    })
  })
})

const sortedData = computed(() => {
  if (!sortField.value || !props.sortable) return filteredData.value
  
  return [...filteredData.value].sort((a, b) => {
    const aValue = a[sortField.value]
    const bValue = b[sortField.value]
    
    let result = 0
    if (aValue < bValue) result = -1
    else if (aValue > bValue) result = 1
    
    return sortOrder.value === 'desc' ? -result : result
  })
})

const paginatedData = computed(() => {
  if (!props.pagination) return sortedData.value
  
  const start = (props.pagination.current - 1) * currentPageSize.value
  const end = start + currentPageSize.value
  return sortedData.value.slice(start, end)
})

const totalPages = computed(() => {
  if (!props.pagination) return 1
  return Math.ceil(filteredData.value.length / currentPageSize.value)
})

const visiblePages = computed(() => {
  const current = props.pagination.current
  const total = totalPages.value
  const pages: number[] = []
  
  let start = Math.max(1, current - 2)
  let end = Math.min(total, current + 2)
  
  if (end - start < 4) {
    if (start === 1) {
      end = Math.min(total, start + 4)
    } else {
      start = Math.max(1, end - 4)
    }
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

const isAllSelected = computed(() => {
  return paginatedData.value.length > 0 && 
         selectedRows.value.length === paginatedData.value.length
})

const hasRowActions = computed(() => {
  return rowActions.value.length > 0
})

const rowActions = computed(() => {
  return props.actions.filter(action => action.type !== 'add')
})

const pageSizeOptions = [10, 20, 50, 100]

// 方法
const getRowKey = (record: any, index: number): string | number => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(record, index)
  }
  return record[props.rowKey] || index
}

const handleSearch = () => {
  emit('search', searchKeyword.value)
}

const handleSort = (column: any) => {
  if (!column.sortable || !props.sortable) return
  
  if (sortField.value === column.key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = column.key
    sortOrder.value = 'asc'
  }
  
  emit('sort', sortField.value, sortOrder.value)
}

const getSortIcon = (columnKey: string): string => {
  if (sortField.value !== columnKey) return '↕'
  return sortOrder.value === 'asc' ? '↑' : '↓'
}

const handleRowSelect = (item: any, index: number) => {
  const key = getRowKey(item, index)
  const selectedIndex = selectedRows.value.indexOf(key)
  
  if (selectedIndex > -1) {
    selectedRows.value.splice(selectedIndex, 1)
  } else {
    selectedRows.value.push(key)
  }
}

const handleSelectAll = () => {
  if (isAllSelected.value) {
    selectedRows.value = []
  } else {
    selectedRows.value = paginatedData.value.map((item, index) => getRowKey(item, index))
  }
}

const handleBatchAction = (action: TableAction) => {
  const selectedItems = paginatedData.value.filter((item, index) => 
    selectedRows.value.includes(getRowKey(item, index))
  )
  emit('batchAction', action, selectedItems)
}

const handlePageChange = (page: number) => {
  if (page < 1 || page > totalPages.value) return
  emit('pageChange', page)
}

const handlePageSizeChange = () => {
  emit('pageSizeChange', currentPageSize.value)
}

const handlePageJump = () => {
  if (jumpPage.value && jumpPage.value >= 1 && jumpPage.value <= totalPages.value) {
    handlePageChange(jumpPage.value)
    jumpPage.value = undefined
  }
}

// 监听器
watch(() => props.pagination.pageSize, (newSize) => {
  currentPageSize.value = newSize
})

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style lang="scss" scoped>
@import '../../styles/data-table.scss';
</style>