# 设计文档

## 概述

AI陪伴社交H5应用是一个基于AI的社交平台，用户通过与AI的初始交流来创建个性化的AI伴侣角色。系统通过分析用户对话记录自动生成角色的特质、性格描述和6维属性值，并提供30个话题维度供用户选择聊天，从而影响角色属性的动态变化。应用还包含每日任务系统、伴侣地图探索、社交互动和排行榜功能。

## 架构设计

### 系统架构

```mermaid
graph TB
    subgraph "前端层"
        A[UniApp H5应用]
        B[Vue 3 + TypeScript]
        C[Pinia状态管理]
    end
    
    subgraph "后端层"
        D[Spring Boot 3.5.3]
        E[Spring Security + JWT]
        F[MyBatis Plus]
    end
    
    subgraph "AI服务层"
        G[LangChain4j]
        H[通义千问/OpenAI]
        I[角色生成引擎]
        J[属性计算引擎]
    end
    
    subgraph "数据层"
        K[MySQL数据库]
        L[Redis缓存]
    end
    
    subgraph "外部服务"
        M[微信公众号API]
        N[文件存储服务]
    end
    
    A --> D
    D --> G
    G --> H
    D --> F
    F --> K
    D --> L
    D --> M
    A --> N
```

### 技术栈

**前端技术栈：**
- UniApp：跨平台H5应用框架
- Vue 3：前端框架
- TypeScript：类型安全
- Pinia：状态管理
- SCSS：样式预处理
- vue-element-plus-x：UI组件库

**后端技术栈：**
- Spring Boot 3.5.3：应用框架
- Spring Security：安全框架
- JWT：身份认证
- MyBatis Plus：ORM框架
- MySQL：关系型数据库
- Redis：缓存数据库
- LangChain4j：AI集成框架
- 通义千问/OpenAI：AI模型服务

## 组件和接口设计

### 核心组件

#### 1. 用户认证组件
- **UserController**: 处理用户登录、注册、token验证
- **JWTUtils**: JWT token生成和验证工具
- **微信授权集成**: 支持微信公众号授权登录

#### 2. AI对话组件
- **AiController**: 处理AI对话请求
- **AiConfig**: AI服务配置和会话管理
- **对话记录管理**: 存储和分析用户对话历史

#### 3. 角色系统组件
- **RoleController**: 角色信息管理
- **角色生成引擎**: 基于对话记录生成角色特质
- **属性计算引擎**: 动态计算6维属性值

#### 4. 伴侣系统组件
- **PartnerController**: AI伴侣管理
- **伴侣创建服务**: 根据用户偏好创建AI伴侣
- **伴侣互动服务**: 处理用户与AI伴侣的交互

#### 5. 话题系统组件
- **TopicController**: 话题管理
- **话题影响引擎**: 计算不同话题对角色属性的影响
- **对话分析服务**: 分析对话内容的情感倾向

#### 6. 任务系统组件
- **TaskController**: 每日任务管理
- **任务生成器**: 自动生成个性化任务
- **奖励系统**: 处理任务完成奖励

#### 7. 社交系统组件
- **SocialController**: 社交互动管理
- **好友系统**: 用户关系管理
- **消息系统**: 用户间消息传递

#### 8. 地图系统组件
- **MapController**: 伴侣地图管理
- **位置服务**: 伴侣位置和状态管理
- **探索引擎**: 地图探索逻辑

#### 9. 排行榜组件
- **RankingController**: 排行榜数据管理
- **统计服务**: 用户行为数据统计
- **排名计算**: 多维度排名算法

### API接口设计

#### 用户认证接口
```
POST /user/login - 用户登录
GET /user/signature - 微信JS-SDK签名
GET /user/url - 微信授权URL
POST /user/signIn - 用户签到
GET /user/signInStatus - 签到状态查询
POST /user/updateNickname - 更新昵称
POST /user/updatePhone - 更新手机号
GET /user/validateToken - Token验证
```

#### AI对话接口
```
POST /ai/aiInitialMatching - AI初始匹配对话
POST /ai/chat - 普通AI对话
POST /ai/topicChat - 话题对话
GET /ai/chatHistory - 对话历史
```

#### 角色系统接口
```
GET /role/info - 获取角色信息
POST /role/generate - 生成角色
PUT /role/update - 更新角色属性
GET /role/attributes - 获取属性详情
```

#### 伴侣系统接口
```
GET /partner/info - 获取伴侣信息
POST /partner/creator - 创建伴侣
PUT /partner/update - 更新伴侣信息
POST /partner/interact - 伴侣互动
```

#### 话题系统接口
```
GET /topic/list - 获取话题列表
GET /topic/detail/{id} - 话题详情
POST /topic/select - 选择话题
GET /topic/effects - 话题影响效果
```

#### 任务系统接口
```
GET /task/daily - 获取每日任务
POST /task/complete - 完成任务
GET /task/history - 任务历史
POST /task/claim - 领取奖励
```

#### 社交系统接口
```
GET /social/friends - 好友列表
POST /social/addFriend - 添加好友
POST /social/message - 发送消息
GET /social/messages - 消息列表
POST /social/like - 点赞
POST /social/comment - 评论
```

#### 地图系统接口
```
GET /map/partners - 获取地图伴侣
POST /map/explore - 探索地图
GET /map/locations - 获取位置信息
POST /map/interact - 地图互动
```

#### 排行榜接口
```
GET /ranking/activity - 活跃度排行
GET /ranking/social - 社交度排行
GET /ranking/task - 任务完成度排行
GET /ranking/overall - 综合排行
```

## 数据模型设计

### 核心数据表

#### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    openid VARCHAR(100) UNIQUE,
    nickname VARCHAR(50),
    head_img_url VARCHAR(500),
    phone VARCHAR(20),
    tokens INT DEFAULT 0,
    sign_in DATE,
    status TINYINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);
```

#### 角色表 (roles)
```sql
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    traits TEXT,
    description TEXT,
    spontaneous INT DEFAULT 50,
    collaborative INT DEFAULT 50,
    realist INT DEFAULT 50,
    logical INT DEFAULT 50,
    analytical INT DEFAULT 50,
    introvert INT DEFAULT 50,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### AI伴侣表 (partners)
```sql
CREATE TABLE partners (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    nickname VARCHAR(50),
    character VARCHAR(500),
    traits TEXT,
    description TEXT,
    spontaneous INT DEFAULT 50,
    collaborative INT DEFAULT 50,
    realist INT DEFAULT 50,
    logical INT DEFAULT 50,
    analytical INT DEFAULT 50,
    introvert INT DEFAULT 50,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 对话记录表 (chat_records)
```sql
CREATE TABLE chat_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    session_id VARCHAR(100),
    role ENUM('user', 'ai'),
    content TEXT,
    topic_id BIGINT,
    emotion_score DECIMAL(3,2),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 话题表 (topics)
```sql
CREATE TABLE topics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100),
    description TEXT,
    category VARCHAR(50),
    attribute_effects JSON,
    status TINYINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);
```

#### 每日任务表 (daily_tasks)
```sql
CREATE TABLE daily_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    task_type VARCHAR(50),
    title VARCHAR(200),
    description TEXT,
    target_value INT,
    current_value INT DEFAULT 0,
    reward_tokens INT,
    status ENUM('pending', 'completed', 'claimed'),
    task_date DATE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 社交关系表 (social_relations)
```sql
CREATE TABLE social_relations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    friend_id BIGINT,
    relation_type ENUM('friend', 'follow'),
    status TINYINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (friend_id) REFERENCES users(id)
);
```

#### 地图伴侣表 (map_partners)
```sql
CREATE TABLE map_partners (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50),
    character VARCHAR(500),
    location_x DECIMAL(10,6),
    location_y DECIMAL(10,6),
    traits TEXT,
    interaction_count INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);
```

#### 用户统计表 (user_statistics)
```sql
CREATE TABLE user_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    activity_score INT DEFAULT 0,
    social_score INT DEFAULT 0,
    task_completion_rate DECIMAL(5,2) DEFAULT 0,
    chat_count INT DEFAULT 0,
    login_days INT DEFAULT 0,
    last_active_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 错误处理

### 错误码设计
```java
public enum ErrorCode {
    SUCCESS(200, "成功"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    INTERNAL_ERROR(500, "服务器内部错误"),
    
    // 业务错误码
    USER_NOT_EXISTS(1001, "用户不存在"),
    TOKEN_INVALID(1002, "Token无效"),
    ALREADY_SIGNED_IN(1003, "今日已签到"),
    INSUFFICIENT_TOKENS(1004, "算力不足"),
    PARTNER_NOT_EXISTS(1005, "伴侣不存在"),
    TASK_ALREADY_COMPLETED(1006, "任务已完成"),
    INVALID_TOPIC(1007, "无效话题"),
    CHAT_LIMIT_EXCEEDED(1008, "聊天次数超限");
}
```

### 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public R handleBusinessException(BusinessException e) {
        return R.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public R handleException(Exception e) {
        log.error("系统异常", e);
        return R.error(ErrorCode.INTERNAL_ERROR);
    }
}
```

## 测试策略

### 单元测试
- 使用JUnit 5进行后端单元测试
- 覆盖所有业务逻辑和工具类
- Mock外部依赖（AI服务、微信API等）

### 集成测试
- 使用TestContainers进行数据库集成测试
- 测试API接口的完整流程
- 验证AI服务集成的正确性

### 前端测试
- 使用Vue Test Utils进行组件测试
- 测试用户交互流程
- 验证状态管理的正确性

### 性能测试
- 使用JMeter进行接口性能测试
- 测试AI对话响应时间
- 验证并发用户场景

### 安全测试
- JWT token安全性测试
- SQL注入防护测试
- XSS攻击防护测试
- 敏感数据加密测试

## 部署架构

### 开发环境
- 本地MySQL数据库
- 本地Redis缓存
- 模拟微信登录
- AI服务Mock

### 生产环境
- 云数据库MySQL
- 云缓存Redis
- CDN静态资源加速
- 负载均衡
- 容器化部署

### 监控和日志
- 应用性能监控(APM)
- 错误日志收集
- 业务指标监控
- 用户行为分析

## 安全考虑

### 数据安全
- 用户敏感信息加密存储
- 对话记录脱敏处理
- 定期数据备份

### 接口安全
- JWT token认证
- 接口访问频率限制
- 参数校验和过滤

### 隐私保护
- 用户数据删除机制
- 隐私设置功能
- 数据使用透明化

## 性能优化

### 缓存策略
- Redis缓存用户会话
- 缓存热门话题和任务
- 缓存排行榜数据

### 数据库优化
- 索引优化
- 查询优化
- 分页查询

### AI服务优化
- 对话上下文管理
- 响应时间优化
- 并发请求处理

### 前端优化
- 组件懒加载
- 图片压缩和懒加载
- 接口请求优化