/**
 * 管理员头部组件样式
 */

.admin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 64px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 100;
  
  // 左侧区域
  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
    
    .menu-toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin-right: 16px;
      border-radius: 6px;
      background: transparent;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f5f5f5;
      }
      
      .menu-icon {
        font-size: 18px;
        color: #666;
      }
    }
    
    .breadcrumb {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #666;
      
      .breadcrumb-item {
        display: flex;
        align-items: center;
        
        .separator {
          margin: 0 8px;
          color: #ccc;
        }
        
        .breadcrumb-text {
          cursor: pointer;
          transition: color 0.3s ease;
          
          &:hover {
            color: #1890ff;
          }
        }
        
        &.active .breadcrumb-text {
          color: #1890ff;
          cursor: default;
        }
      }
    }
  }
  
  // 中间区域
  .header-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 auto;
    
    .system-status {
      display: flex;
      align-items: center;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      
      .status-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 6px;
      }
      
      &.healthy {
        background: #f6ffed;
        color: #52c41a;
        
        .status-dot {
          background: #52c41a;
        }
      }
      
      &.warning {
        background: #fffbe6;
        color: #faad14;
        
        .status-dot {
          background: #faad14;
        }
      }
      
      &.error {
        background: #fff2f0;
        color: #ff4d4f;
        
        .status-dot {
          background: #ff4d4f;
        }
      }
    }
  }
  
  // 右侧区域
  .header-right {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
    gap: 8px;
    
    // 通知中心
    .notification-center {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f5f5f5;
      }
      
      .notification-icon {
        font-size: 16px;
        color: #666;
      }
      
      .notification-badge {
        position: absolute;
        top: -2px;
        right: -2px;
        min-width: 16px;
        height: 16px;
        background: #ff4d4f;
        color: #fff;
        font-size: 10px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 4px;
        border: 2px solid #fff;
      }
    }
    
    // 主题切换
    .theme-toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f5f5f5;
      }
      
      .theme-icon {
        font-size: 16px;
        color: #666;
      }
    }
    
    // 全屏切换
    .fullscreen-toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f5f5f5;
      }
      
      .fullscreen-icon {
        font-size: 16px;
        color: #666;
      }
    }
    
    // 用户信息
    .user-info {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-left: 8px;
      
      &:hover {
        background: #f5f5f5;
      }
      
      .avatar {
        width: 32px;
        height: 32px;
        margin-right: 8px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        flex-shrink: 0;
        overflow: hidden;
        
        image {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
        
        .avatar-text {
          color: #fff;
          font-size: 14px;
          font-weight: 500;
        }
      }
      
      .user-details {
        display: flex;
        flex-direction: column;
        min-width: 0;
        
        .username {
          font-size: 14px;
          color: #333;
          font-weight: 500;
          line-height: 1.2;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .role {
          font-size: 12px;
          color: #666;
          line-height: 1.2;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .last-login {
          font-size: 11px;
          color: #999;
          line-height: 1.2;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      
      .dropdown-icon {
        margin-left: 8px;
        font-size: 12px;
        color: #999;
        transition: transform 0.3s ease;
        flex-shrink: 0;
        
        &.expanded {
          transform: rotate(180deg);
        }
      }
    }
  }
}

// 用户下拉菜单样式
.user-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  right: 24px;
  min-width: 280px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  border: 1px solid #e8e8e8;
  z-index: 1001;
  overflow: hidden;
  
  .dropdown-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    
    .user-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 18px;
      font-weight: 600;
      margin-right: 12px;
      flex-shrink: 0;
    }
    
    .user-info-detail {
      flex: 1;
      min-width: 0;
      
      .username {
        font-size: 16px;
        color: #333;
        font-weight: 600;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .role {
        font-size: 13px;
        color: #666;
        margin-bottom: 2px;
      }
      
      .user-id {
        font-size: 12px;
        color: #999;
      }
    }
  }
  
  .dropdown-divider {
    height: 1px;
    background: #e8e8e8;
    margin: 4px 0;
  }
  
  .dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: #f5f5f5;
    }
    
    .icon {
      font-size: 16px;
      margin-right: 12px;
      color: #666;
      width: 16px;
      text-align: center;
    }
    
    .shortcut {
      margin-left: auto;
      font-size: 12px;
      color: #999;
      background: #f0f0f0;
      padding: 2px 6px;
      border-radius: 4px;
    }
    
    &.danger {
      color: #ff4d4f;
      
      .icon {
        color: #ff4d4f;
      }
      
      &:hover {
        background: #fff2f0;
      }
    }
  }
}

// 通知下拉菜单样式
.notification-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  right: 200px;
  width: 360px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  border: 1px solid #e8e8e8;
  z-index: 1001;
  overflow: hidden;
  
  .notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
    
    .mark-all-read {
      font-size: 12px;
      color: #1890ff;
      cursor: pointer;
      
      &:hover {
        color: #40a9ff;
      }
    }
  }
  
  .notification-list {
    max-height: 400px;
    overflow-y: auto;
    
    .notification-item {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      
      &:hover {
        background: #f5f5f5;
      }
      
      &.unread {
        background: #f6ffed;
        
        &:hover {
          background: #f0f9e8;
        }
      }
      
      .notification-icon {
        font-size: 16px;
        margin-right: 12px;
        margin-top: 2px;
        flex-shrink: 0;
      }
      
      .notification-content {
        flex: 1;
        min-width: 0;
        
        .notification-title {
          font-size: 14px;
          color: #333;
          font-weight: 500;
          margin-bottom: 4px;
          line-height: 1.4;
        }
        
        .notification-desc {
          font-size: 12px;
          color: #666;
          line-height: 1.4;
          margin-bottom: 4px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .notification-time {
          font-size: 11px;
          color: #999;
        }
      }
      
      .unread-dot {
        position: absolute;
        top: 16px;
        right: 16px;
        width: 6px;
        height: 6px;
        background: #52c41a;
        border-radius: 50%;
      }
    }
  }
  
  .notification-footer {
    padding: 12px 16px;
    text-align: center;
    border-top: 1px solid #e8e8e8;
    
    text {
      font-size: 14px;
      color: #1890ff;
      cursor: pointer;
      
      &:hover {
        color: #40a9ff;
      }
    }
  }
}

// 遮罩层
.dropdown-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: transparent;
}

// 响应式设计
@media (max-width: 768px) {
  .admin-header {
    padding: 0 16px;
    height: 56px;
    
    .header-left {
      .breadcrumb {
        display: none;
      }
    }
    
    .header-center {
      display: none;
    }
    
    .header-right {
      gap: 4px;
      
      .notification-center,
      .theme-toggle,
      .fullscreen-toggle {
        width: 32px;
        height: 32px;
        
        .notification-icon,
        .theme-icon,
        .fullscreen-icon {
          font-size: 14px;
        }
      }
      
      .user-info {
        padding: 4px 8px;
        
        .avatar {
          width: 28px;
          height: 28px;
          font-size: 12px;
        }
        
        .user-details {
          display: none;
        }
      }
    }
  }
  
  .user-dropdown {
    right: 16px;
    min-width: 240px;
  }
  
  .notification-dropdown {
    right: 60px;
    width: 300px;
  }
}

// 平板适配
@media (min-width: 769px) and (max-width: 1024px) {
  .admin-header {
    .header-right {
      .user-info {
        .user-details {
          .last-login {
            display: none;
          }
        }
      }
    }
  }
}

// 暗色主题
.admin-header.dark-theme {
  background: #1f1f1f;
  border-bottom-color: #303030;
  
  .header-left {
    .menu-toggle:hover {
      background: #262626;
    }
    
    .breadcrumb {
      color: #d9d9d9;
      
      .breadcrumb-item {
        .separator {
          color: #595959;
        }
        
        .breadcrumb-text:hover {
          color: #40a9ff;
        }
        
        &.active .breadcrumb-text {
          color: #40a9ff;
        }
      }
    }
  }
  
  .header-right {
    .notification-center:hover,
    .theme-toggle:hover,
    .fullscreen-toggle:hover {
      background: #262626;
    }
    
    .user-info {
      &:hover {
        background: #262626;
      }
      
      .user-details {
        .username {
          color: #d9d9d9;
        }
        
        .role {
          color: #8c8c8c;
        }
        
        .last-login {
          color: #595959;
        }
      }
    }
  }
  
  .user-dropdown,
  .notification-dropdown {
    background: #1f1f1f;
    border-color: #303030;
    
    .dropdown-header {
      background: #141414;
      border-bottom-color: #303030;
    }
    
    .dropdown-item {
      color: #d9d9d9;
      
      &:hover {
        background: #262626;
      }
      
      &.danger:hover {
        background: #2a1215;
      }
    }
  }
}