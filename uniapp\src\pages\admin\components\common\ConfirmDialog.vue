<template>
  <view 
    v-if="visible" 
    class="confirm-dialog-overlay"
    @click="handleOverlayClick"
  >
    <view 
      class="confirm-dialog"
      :class="[`dialog-${type}`, { 'dialog-show': showDialog }]"
      @click.stop
    >
      <!-- 对话框头部 -->
      <view class="dialog-header">
        <view class="dialog-icon">
          {{ getDialogIcon(type) }}
        </view>
        <view class="dialog-title">{{ title }}</view>
        <view 
          v-if="closable"
          class="dialog-close"
          @click="handleCancel"
        >
          ×
        </view>
      </view>
      
      <!-- 对话框内容 -->
      <view class="dialog-content">
        <view class="dialog-message">{{ message }}</view>
        
        <!-- 输入框（如果需要） -->
        <view v-if="showInput" class="dialog-input-container">
          <input
            v-model="inputValue"
            class="dialog-input"
            :placeholder="inputPlaceholder"
            :type="inputType"
            @input="handleInputChange"
          />
          <view v-if="inputError" class="input-error">{{ inputError }}</view>
        </view>
        
        <!-- 详细信息（可折叠） -->
        <view v-if="details" class="dialog-details">
          <view 
            class="details-toggle"
            @click="showDetails = !showDetails"
          >
            <view class="toggle-text">详细信息</view>
            <view class="toggle-icon" :class="{ 'expanded': showDetails }">
              ▼
            </view>
          </view>
          <view v-if="showDetails" class="details-content">
            <pre>{{ details }}</pre>
          </view>
        </view>
      </view>
      
      <!-- 对话框按钮 -->
      <view class="dialog-actions">
        <button 
          v-if="showCancel"
          class="dialog-button cancel-button"
          @click="handleCancel"
        >
          {{ cancelText }}
        </button>
        <button 
          class="dialog-button confirm-button"
          :class="[`button-${type}`, { 'loading': loading }]"
          :disabled="loading || (showInput && !isInputValid)"
          @click="handleConfirm"
        >
          <view v-if="loading" class="button-loading">⏳</view>
          {{ confirmText }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'

export interface ConfirmDialogOptions {
  type?: 'info' | 'success' | 'warning' | 'error' | 'confirm'
  title?: string
  message: string
  details?: string
  confirmText?: string
  cancelText?: string
  showCancel?: boolean
  closable?: boolean
  showInput?: boolean
  inputType?: 'text' | 'password' | 'number'
  inputPlaceholder?: string
  inputValidator?: (value: string) => string | null
  onConfirm?: (inputValue?: string) => Promise<void> | void
  onCancel?: () => void
}

const props = withDefaults(defineProps<{
  visible: boolean
  options: ConfirmDialogOptions
}>(), {
  visible: false,
  options: () => ({
    type: 'confirm',
    title: '确认',
    message: '确定要执行此操作吗？',
    confirmText: '确定',
    cancelText: '取消',
    showCancel: true,
    closable: true,
    showInput: false,
    inputType: 'text',
    inputPlaceholder: '请输入...'
  })
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'confirm': [inputValue?: string]
  'cancel': []
}>()

// 响应式数据
const showDialog = ref(false)
const showDetails = ref(false)
const inputValue = ref('')
const inputError = ref('')
const loading = ref(false)

// 计算属性
const type = computed(() => props.options.type || 'confirm')
const title = computed(() => props.options.title || getDefaultTitle(type.value))
const message = computed(() => props.options.message)
const details = computed(() => props.options.details)
const confirmText = computed(() => props.options.confirmText || '确定')
const cancelText = computed(() => props.options.cancelText || '取消')
const showCancel = computed(() => props.options.showCancel !== false)
const closable = computed(() => props.options.closable !== false)
const showInput = computed(() => props.options.showInput || false)
const inputType = computed(() => props.options.inputType || 'text')
const inputPlaceholder = computed(() => props.options.inputPlaceholder || '请输入...')

// 输入验证
const isInputValid = computed(() => {
  if (!showInput.value) return true
  if (!inputValue.value.trim()) return false
  return !inputError.value
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    showDialog.value = true
    inputValue.value = ''
    inputError.value = ''
    showDetails.value = false
  } else {
    showDialog.value = false
  }
})

/**
 * 获取默认标题
 */
const getDefaultTitle = (type: string): string => {
  const titleMap: Record<string, string> = {
    info: '提示',
    success: '成功',
    warning: '警告',
    error: '错误',
    confirm: '确认'
  }
  return titleMap[type] || '提示'
}

/**
 * 获取对话框图标
 */
const getDialogIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    info: 'ℹ️',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    confirm: '❓'
  }
  return iconMap[type] || '❓'
}

/**
 * 处理遮罩层点击
 */
const handleOverlayClick = () => {
  if (closable.value) {
    handleCancel()
  }
}

/**
 * 处理输入变化
 */
const handleInputChange = () => {
  if (props.options.inputValidator) {
    inputError.value = props.options.inputValidator(inputValue.value) || ''
  }
}

/**
 * 处理确认
 */
const handleConfirm = async () => {
  if (showInput.value && !isInputValid.value) {
    return
  }
  
  try {
    loading.value = true
    
    if (props.options.onConfirm) {
      await props.options.onConfirm(showInput.value ? inputValue.value : undefined)
    }
    
    emit('confirm', showInput.value ? inputValue.value : undefined)
    closeDialog()
  } catch (error) {
    console.error('Confirm action failed:', error)
    // 不关闭对话框，让用户重试
  } finally {
    loading.value = false
  }
}

/**
 * 处理取消
 */
const handleCancel = () => {
  if (props.options.onCancel) {
    props.options.onCancel()
  }
  
  emit('cancel')
  closeDialog()
}

/**
 * 关闭对话框
 */
const closeDialog = () => {
  showDialog.value = false
  setTimeout(() => {
    emit('update:visible', false)
  }, 300)
}
</script>

<style lang="scss" scoped>
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 21000;
  backdrop-filter: blur(4px);
}

.confirm-dialog {
  background: white;
  border-radius: 12px;
  min-width: 320px;
  max-width: 90%;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
  
  &.dialog-show {
    transform: scale(1);
    opacity: 1;
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.dialog-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.dialog-title {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.dialog-close {
  font-size: 24px;
  color: #999;
  cursor: pointer;
  transition: color 0.2s ease;
  
  &:hover {
    color: #666;
  }
}

.dialog-content {
  padding: 16px 24px 20px;
}

.dialog-message {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin-bottom: 16px;
}

.dialog-input-container {
  margin-bottom: 16px;
}

.dialog-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  
  &:focus {
    border-color: #1890ff;
    outline: none;
  }
  
  &::placeholder {
    color: #bfbfbf;
  }
}

.input-error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.dialog-details {
  margin-top: 16px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.details-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  color: #1890ff;
  font-size: 13px;
  
  &:hover {
    color: #40a9ff;
  }
}

.toggle-icon {
  transition: transform 0.2s ease;
  
  &.expanded {
    transform: rotate(180deg);
  }
}

.details-content {
  margin-top: 12px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
    color: #666;
  }
}

.dialog-actions {
  display: flex;
  gap: 12px;
  padding: 16px 24px 20px;
  justify-content: flex-end;
}

.dialog-button {
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 6px;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.cancel-button {
  background: #f5f5f5;
  color: #666;
  
  &:hover:not(:disabled) {
    background: #e6e6e6;
  }
}

.confirm-button {
  background: #1890ff;
  color: white;
  
  &:hover:not(:disabled) {
    background: #40a9ff;
  }
  
  &.button-warning {
    background: #faad14;
    
    &:hover:not(:disabled) {
      background: #ffc53d;
    }
  }
  
  &.button-error {
    background: #ff4d4f;
    
    &:hover:not(:disabled) {
      background: #ff7875;
    }
  }
  
  &.loading {
    opacity: 0.8;
  }
}

.button-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// 响应式适配
@media (max-width: 768px) {
  .confirm-dialog {
    min-width: 280px;
    margin: 20px;
  }
  
  .dialog-header {
    padding: 16px 20px 12px;
  }
  
  .dialog-content {
    padding: 12px 20px 16px;
  }
  
  .dialog-actions {
    padding: 12px 20px 16px;
    flex-direction: column-reverse;
    
    .dialog-button {
      width: 100%;
      justify-content: center;
    }
  }
}
</style>
