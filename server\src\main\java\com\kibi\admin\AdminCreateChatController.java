package com.kibi.admin;

import com.kibi.entity.CreateChat;
import com.kibi.entity.Role;
import com.kibi.service.CreateChatService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/createChat")
public class AdminCreateChatController {

    @Autowired
    private CreateChatService createChatService;

    @Autowired
    private JWTUtils jwtUtils;

    /**
     * 获取记录列表
     */
    @GetMapping
    public R<list<CreateChat>> getList(@RequestHeader("Authorization") String authHeader) {
    }
}
