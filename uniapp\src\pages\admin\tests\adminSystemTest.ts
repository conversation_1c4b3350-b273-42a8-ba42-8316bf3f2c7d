// 管理系统功能测试
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import SystemMonitorPanel from '../modules/SystemMonitorPanel.vue'
import SystemToolsPanel from '../modules/SystemToolsPanel.vue'
import { useRouteGuard } from '../composables/useRouteGuard'
import { usePageState } from '../composables/usePageState'
import { PageStateManager } from '../utils/pageStateManager'

describe('管理系统功能测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  afterEach(() => {
    // 清理测试数据
    uni.clearStorageSync()
  })

  describe('系统监控模块', () => {
    it('应该正确渲染系统监控面板', () => {
      const wrapper = mount(SystemMonitorPanel)
      
      expect(wrapper.find('.system-monitor-panel').exists()).toBe(true)
      expect(wrapper.find('.panel-header').exists()).toBe(true)
      expect(wrapper.find('.overview-cards').exists()).toBe(true)
    })

    it('应该显示系统指标卡片', () => {
      const wrapper = mount(SystemMonitorPanel)
      
      const metricCards = wrapper.findAll('.metric-card')
      expect(metricCards.length).toBeGreaterThan(0)
      
      // 检查CPU使用率卡片
      const cpuCard = wrapper.find('.metric-card.cpu-usage')
      expect(cpuCard.exists()).toBe(true)
      
      // 检查内存使用卡片
      const memoryCard = wrapper.find('.metric-card.memory-usage')
      expect(memoryCard.exists()).toBe(true)
    })

    it('应该能够刷新监控数据', async () => {
      const wrapper = mount(SystemMonitorPanel)
      
      const refreshBtn = wrapper.find('.refresh-btn')
      expect(refreshBtn.exists()).toBe(true)
      
      await refreshBtn.trigger('click')
      
      // 验证刷新状态
      expect(wrapper.vm.isRefreshing).toBe(false)
    })

    it('应该能够切换自动刷新', async () => {
      const wrapper = mount(SystemMonitorPanel)
      
      const autoRefreshToggle = wrapper.find('.auto-refresh-toggle')
      expect(autoRefreshToggle.exists()).toBe(true)
      
      const initialState = wrapper.vm.autoRefresh
      await autoRefreshToggle.trigger('click')
      
      expect(wrapper.vm.autoRefresh).toBe(!initialState)
    })
  })

  describe('系统工具模块', () => {
    it('应该正确渲染系统工具面板', () => {
      const wrapper = mount(SystemToolsPanel)
      
      expect(wrapper.find('.system-tools-panel').exists()).toBe(true)
      expect(wrapper.find('.panel-header').exists()).toBe(true)
      expect(wrapper.find('.tools-categories').exists()).toBe(true)
      expect(wrapper.find('.tools-grid').exists()).toBe(true)
    })

    it('应该显示工具分类标签', () => {
      const wrapper = mount(SystemToolsPanel)
      
      const categoryTabs = wrapper.findAll('.category-tab')
      expect(categoryTabs.length).toBeGreaterThan(0)
      
      // 检查默认选中的分类
      const activeTab = wrapper.find('.category-tab.active')
      expect(activeTab.exists()).toBe(true)
    })

    it('应该能够切换工具分类', async () => {
      const wrapper = mount(SystemToolsPanel)
      
      const categoryTabs = wrapper.findAll('.category-tab')
      if (categoryTabs.length > 1) {
        const secondTab = categoryTabs[1]
        await secondTab.trigger('click')
        
        expect(secondTab.classes()).toContain('active')
      }
    })

    it('应该显示工具卡片', () => {
      const wrapper = mount(SystemToolsPanel)
      
      const toolCards = wrapper.findAll('.tool-card')
      expect(toolCards.length).toBeGreaterThan(0)
      
      // 检查工具卡片结构
      const firstCard = toolCards[0]
      expect(firstCard.find('.tool-header').exists()).toBe(true)
      expect(firstCard.find('.tool-content').exists()).toBe(true)
      expect(firstCard.find('.tool-actions').exists()).toBe(true)
    })

    it('应该能够打开工具详情弹窗', async () => {
      const wrapper = mount(SystemToolsPanel)
      
      const toolCard = wrapper.find('.tool-card')
      await toolCard.trigger('click')
      
      expect(wrapper.vm.showToolModal).toBe(true)
      expect(wrapper.find('.tool-modal-overlay').exists()).toBe(true)
    })
  })

  describe('路由管理功能', () => {
    it('应该正确初始化路由守卫', () => {
      const routeGuard = useRouteGuard()
      
      expect(routeGuard.currentPath.value).toBe('dashboard')
      expect(routeGuard.menuItems.value.length).toBeGreaterThan(0)
    })

    it('应该能够导航到指定路径', () => {
      const routeGuard = useRouteGuard()
      
      const result = routeGuard.navigateTo('system-monitor')
      expect(result).toBe(true)
      expect(routeGuard.currentPath.value).toBe('system-monitor')
    })

    it('应该能够检查权限', () => {
      const routeGuard = useRouteGuard()
      
      // 测试有权限的路径
      expect(routeGuard.hasPermission('dashboard:view')).toBe(true)
      
      // 测试无权限的路径（当前实现返回true）
      expect(routeGuard.hasPermission('admin:super')).toBe(true)
    })

    it('应该能够生成面包屑导航', () => {
      const routeGuard = useRouteGuard()
      
      const breadcrumb = routeGuard.generateBreadcrumb('system-function/user-management')
      expect(breadcrumb.length).toBeGreaterThan(0)
      expect(breadcrumb[breadcrumb.length - 1].title).toBe('用户管理')
    })

    it('应该能够返回上一页', () => {
      const routeGuard = useRouteGuard()
      
      // 先导航到其他页面
      routeGuard.navigateTo('system-monitor')
      routeGuard.navigateTo('system-tools')
      
      // 然后返回
      const result = routeGuard.goBack()
      expect(result).toBe(true)
      expect(routeGuard.currentPath.value).toBe('system-monitor')
    })
  })

  describe('页面状态管理', () => {
    it('应该能够保存和恢复滚动位置', () => {
      const pageState = usePageState('test-page')
      
      pageState.saveScrollPosition(100)
      const scrollTop = pageState.restoreScrollPosition()
      
      expect(scrollTop).toBe(100)
    })

    it('应该能够保存和获取表单数据', () => {
      const pageState = usePageState('test-page')
      
      const formData = { name: 'test', age: 25 }
      pageState.saveFormData(formData)
      
      const savedData = pageState.getFormData()
      expect(savedData).toEqual(formData)
    })

    it('应该能够管理选中项', () => {
      const pageState = usePageState('test-page')
      
      const items = ['item1', 'item2', 'item3']
      pageState.saveSelectedItems(items)
      
      const selectedItems = pageState.getSelectedItems()
      expect(selectedItems).toEqual(items)
    })

    it('应该能够清空页面状态', () => {
      const pageState = usePageState('test-page')
      
      pageState.saveFormData({ test: 'data' })
      pageState.saveSelectedItems(['item1'])
      
      pageState.clearPageState()
      
      expect(pageState.getFormData()).toEqual({})
      expect(pageState.getSelectedItems()).toEqual([])
    })
  })

  describe('页面状态管理器', () => {
    it('应该能够创建页面状态管理器', () => {
      const manager = new PageStateManager('test-page')
      
      expect(manager).toBeDefined()
      expect(manager.getScrollTop()).toBe(0)
    })

    it('应该能够保存和恢复状态', () => {
      const manager = new PageStateManager('test-page')
      
      manager.setScrollTop(200)
      manager.setFormData({ field: 'value' })
      manager.setSelectedItems(['item1', 'item2'])
      
      // 创建新的管理器实例（模拟页面重新加载）
      const newManager = new PageStateManager('test-page')
      
      expect(newManager.getScrollTop()).toBe(200)
      expect(newManager.getFormData()).toEqual({ field: 'value' })
      expect(newManager.getSelectedItems()).toEqual(['item1', 'item2'])
    })

    it('应该能够检查状态是否过期', () => {
      const manager = new PageStateManager('test-page')
      
      expect(manager.isExpired()).toBe(false)
      
      // 模拟过期状态
      const expiredState = { ...manager.getState(), timestamp: Date.now() - 31 * 60 * 1000 }
      uni.setStorageSync('admin_page_state_test-page', expiredState)
      
      const expiredManager = new PageStateManager('test-page')
      expect(expiredManager.isExpired()).toBe(false) // 因为会重置为默认状态
    })

    it('应该能够清理过期状态', () => {
      // 创建一些测试状态
      const manager1 = new PageStateManager('page1')
      const manager2 = new PageStateManager('page2')
      
      manager1.setFormData({ test: 'data1' })
      manager2.setFormData({ test: 'data2' })
      
      // 清理过期状态
      PageStateManager.cleanupExpiredStates()
      
      // 验证状态仍然存在（因为没有过期）
      const newManager1 = new PageStateManager('page1')
      expect(newManager1.getFormData()).toEqual({ test: 'data1' })
    })
  })
})

// 集成测试
describe('管理系统集成测试', () => {
  it('应该能够完整的页面切换流程', async () => {
    const routeGuard = useRouteGuard()
    const pageState = usePageState('integration-test')
    
    // 1. 初始状态
    expect(routeGuard.currentPath.value).toBe('dashboard')
    
    // 2. 保存当前页面状态
    pageState.saveScrollPosition(50)
    pageState.saveFormData({ search: 'test' })
    
    // 3. 导航到系统监控
    const result = routeGuard.navigateTo('system-monitor')
    expect(result).toBe(true)
    expect(routeGuard.currentPath.value).toBe('system-monitor')
    
    // 4. 验证面包屑更新
    const breadcrumb = routeGuard.breadcrumb.value
    expect(breadcrumb.length).toBeGreaterThan(0)
    expect(breadcrumb[breadcrumb.length - 1].path).toBe('system-monitor')
    
    // 5. 返回上一页
    const backResult = routeGuard.goBack()
    expect(backResult).toBe(true)
    expect(routeGuard.currentPath.value).toBe('dashboard')
    
    // 6. 验证状态恢复
    expect(pageState.restoreScrollPosition()).toBe(50)
    expect(pageState.getFormData()).toEqual({ search: 'test' })
  })

  it('应该能够处理权限检查', () => {
    const routeGuard = useRouteGuard()
    
    // 测试有权限的路径
    expect(routeGuard.canNavigate('dashboard')).toBe(true)
    expect(routeGuard.canNavigate('system-monitor')).toBe(true)
    expect(routeGuard.canNavigate('system-tools')).toBe(true)
    
    // 测试不存在的路径
    expect(routeGuard.canNavigate('non-existent-path')).toBe(false)
  })
})
