<template>
  <view class="user-management">
    <!-- 用户管理头部 -->
    <view class="management-header">
      <view class="header-title">用户管理</view>
      <view class="header-actions">
        <button
          v-if="hasPermission(PERMISSIONS.USER_CREATE)"
          class="action-btn primary"
          @click="handleAddUser"
        >
          <text class="btn-icon">➕</text>
          添加用户
        </button>
      </view>
    </view>

    <!-- 数据表格 -->
    <DataTable
      :data="userCrud.data.value"
      :columns="userColumns"
      :loading="userCrud.loading.value"
      :pagination="userCrud.pagination"
      :searchable="true"
      :sortable="true"
      :actions="tableActions"
      :selectable="true"
      :batch-actions="batchActions"
      title="用户列表"
      search-placeholder="搜索用户名、昵称、邮箱"
      @search="userCrud.search"
      @sort="userCrud.sort"
      @page-change="userCrud.changePage"
      @page-size-change="userCrud.changePageSize"
      @batch-action="handleBatchAction"
    />

    <!-- 用户表单弹窗 -->
    <view v-if="showUserForm" class="modal-overlay" @click="closeUserForm">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="modal-title">{{ formMode === 'create' ? '添加用户' : formMode === 'edit' ? '编辑用户' : '查看用户' }}</view>
          <view class="modal-close" @click="closeUserForm">✕</view>
        </view>
        
        <view class="modal-body">
          <DataForm
            :fields="userFormFields"
            :data="formData"
            :mode="formMode"
            :loading="userCrud.submitting.value"
            @submit="handleUserSubmit"
            @cancel="closeUserForm"
          />
        </view>
      </view>
    </view>

    <!-- 角色分配弹窗 -->
    <view v-if="showRoleAssign" class="modal-overlay" @click="closeRoleAssign">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="modal-title">分配角色 - {{ selectedUser?.nickname }}</view>
          <view class="modal-close" @click="closeRoleAssign">✕</view>
        </view>
        
        <view class="modal-body">
          <view class="role-assign-content">
            <view class="assign-title">选择角色</view>
            <view class="role-list">
              <label 
                v-for="role in availableRoles" 
                :key="role.id"
                class="role-item"
              >
                <input 
                  type="checkbox" 
                  :value="role.id"
                  v-model="selectedRoleIds"
                  class="role-checkbox"
                />
                <view class="role-info">
                  <view class="role-name">{{ role.name }}</view>
                  <view class="role-desc">{{ role.description }}</view>
                </view>
              </label>
            </view>
            
            <view class="assign-actions">
              <button class="action-btn" @click="closeRoleAssign">取消</button>
              <button 
                class="action-btn primary" 
                @click="handleRoleAssign"
                :disabled="assigningRole"
              >
                {{ assigningRole ? '分配中...' : '确认分配' }}
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useCrud } from '../../composables/useCrud'
import { usePermission, PERMISSIONS } from '../../composables/usePermission'
import { useOperationLog } from '../../composables/useOperationLog'
import { mockUserApi, mockRoleApi, userExtendedApi, roleExtendedApi } from '../../api/user'
import DataTable from '../common/DataTable.vue'
import DataForm from '../common/DataForm.vue'
import type { User, Role, UserFormData, TableColumn, TableAction, FormField } from '../../types/admin'

// 响应式数据
const showUserForm = ref(false)
const showRoleAssign = ref(false)
const formMode = ref<'create' | 'edit' | 'view'>('create')
const formData = ref<Partial<UserFormData>>({})
const selectedUser = ref<User | null>(null)
const selectedRoleIds = ref<number[]>([])
const availableRoles = ref<Role[]>([])
const assigningRole = ref(false)

// 权限和日志管理
const { hasPermission } = usePermission()
const { logUserOperation } = useOperationLog()

// 使用CRUD composable
const userCrud = useCrud<User>(mockUserApi, {
  initialPagination: { pageSize: 10 },
  autoLoad: true,
  cacheKey: 'users'
})

// 表格列配置
const userColumns: TableColumn[] = [
  {
    key: 'id',
    title: 'ID',
    width: '80px',
    sortable: true
  },
  {
    key: 'username',
    title: '用户名',
    sortable: true
  },
  {
    key: 'nickname',
    title: '昵称',
    sortable: true
  },
  {
    key: 'email',
    title: '邮箱'
  },
  {
    key: 'phone',
    title: '手机号'
  },
  {
    key: 'roleNames',
    title: '角色',
    render: (value: string[]) => value?.join(', ') || '无'
  },
  {
    key: 'status',
    title: '状态',
    render: (value: number) => value === 1 ? '启用' : '禁用'
  },
  {
    key: 'lastLoginTime',
    title: '最后登录',
    render: (value: string) => value || '从未登录'
  }
]

// 表格操作（基于权限过滤）
const tableActions = computed<TableAction[]>(() => {
  const actions: TableAction[] = []

  if (hasPermission(PERMISSIONS.USER_VIEW)) {
    actions.push({
      type: 'view',
      label: '查看',
      handler: handleViewUser
    })
  }

  if (hasPermission(PERMISSIONS.USER_UPDATE)) {
    actions.push({
      type: 'edit',
      label: '编辑',
      handler: handleEditUser
    })
  }

  if (hasPermission(PERMISSIONS.USER_DELETE)) {
    actions.push({
      type: 'delete',
      label: '删除',
      handler: handleDeleteUser
    })
  }

  return actions
})

// 批量操作（基于权限过滤）
const batchActions = computed<TableAction[]>(() => {
  const actions: TableAction[] = []

  if (hasPermission(PERMISSIONS.USER_DELETE)) {
    actions.push({
      type: 'delete',
      label: '批量删除',
      handler: handleBatchDelete
    })
  }

  return actions
})

// 用户表单字段
const userFormFields = computed<FormField[]>(() => [
  {
    key: 'username',
    label: '用户名',
    type: 'text',
    required: true,
    placeholder: '请输入用户名',
    rules: [
      { type: 'required', message: '用户名不能为空' },
      { type: 'min', value: 3, message: '用户名至少3个字符' },
      { type: 'max', value: 20, message: '用户名最多20个字符' }
    ]
  },
  {
    key: 'nickname',
    label: '昵称',
    type: 'text',
    required: true,
    placeholder: '请输入昵称',
    rules: [
      { type: 'required', message: '昵称不能为空' },
      { type: 'max', value: 50, message: '昵称最多50个字符' }
    ]
  },
  {
    key: 'email',
    label: '邮箱',
    type: 'email',
    placeholder: '请输入邮箱地址',
    rules: [
      { type: 'email', message: '请输入正确的邮箱格式' }
    ]
  },
  {
    key: 'phone',
    label: '手机号',
    type: 'text',
    placeholder: '请输入手机号',
    rules: [
      { type: 'pattern', value: '^1[3-9]\\d{9}$', message: '请输入正确的手机号格式' }
    ]
  },
  {
    key: 'password',
    label: '密码',
    type: 'password',
    required: formMode.value === 'create',
    placeholder: formMode.value === 'create' ? '请输入密码' : '留空则不修改密码',
    condition: (data) => formMode.value !== 'view',
    rules: formMode.value === 'create' ? [
      { type: 'required', message: '密码不能为空' },
      { type: 'min', value: 6, message: '密码至少6个字符' }
    ] : []
  },
  {
    key: 'confirmPassword',
    label: '确认密码',
    type: 'password',
    required: formMode.value === 'create',
    placeholder: '请再次输入密码',
    condition: (data) => formMode.value !== 'view' && (formMode.value === 'create' || data.password),
    rules: formMode.value === 'create' ? [
      { type: 'required', message: '请确认密码' }
    ] : []
  },
  {
    key: 'status',
    label: '用户状态',
    type: 'radio',
    required: true,
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  }
])

// 方法
function handleAddUser() {
  if (!hasPermission(PERMISSIONS.USER_CREATE)) {
    uni.showToast({
      title: '没有权限创建用户',
      icon: 'error'
    })
    return
  }

  formMode.value = 'create'
  formData.value = {
    username: '',
    nickname: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    status: 1,
    roleIds: []
  }
  showUserForm.value = true
}

function handleViewUser(user: User) {
  formMode.value = 'view'
  formData.value = { ...user }
  showUserForm.value = true
}

function handleEditUser(user: User) {
  formMode.value = 'edit'
  formData.value = { ...user, password: '', confirmPassword: '' }
  showUserForm.value = true
}

async function handleDeleteUser(user: User) {
  if (!hasPermission(PERMISSIONS.USER_DELETE)) {
    uni.showToast({
      title: '没有权限删除用户',
      icon: 'error'
    })
    return
  }

  try {
    const beforeData = { ...user }
    const success = await userCrud.remove(user.id)

    if (success) {
      await logUserOperation('delete', user.id, beforeData, undefined)
    }
  } catch (error) {
    console.error('Delete user error:', error)
    await logUserOperation('delete', user.id, undefined, undefined)
  }
}

function closeUserForm() {
  showUserForm.value = false
  formData.value = {}
}

async function handleUserSubmit(data: UserFormData) {
  try {
    // 权限检查
    const requiredPermission = formMode.value === 'create' ? PERMISSIONS.USER_CREATE : PERMISSIONS.USER_UPDATE
    if (!hasPermission(requiredPermission)) {
      uni.showToast({
        title: `没有权限${formMode.value === 'create' ? '创建' : '更新'}用户`,
        icon: 'error'
      })
      return
    }

    // 验证确认密码
    if (data.password && data.password !== data.confirmPassword) {
      uni.showToast({
        title: '两次输入的密码不一致',
        icon: 'error'
      })
      return
    }

    // 移除确认密码字段
    const submitData = { ...data }
    delete submitData.confirmPassword

    let result
    const beforeData = formMode.value === 'edit' ? formData.value : undefined

    if (formMode.value === 'create') {
      result = await userCrud.create(submitData)
      if (result) {
        await logUserOperation('create', result.id, undefined, result)
      }
    } else {
      result = await userCrud.update(formData.value.id!, submitData)
      if (result) {
        await logUserOperation('update', formData.value.id!, beforeData, result)
      }
    }

    closeUserForm()
  } catch (error) {
    console.error('Submit user error:', error)
    // 记录失败日志
    await logUserOperation(
      formMode.value === 'create' ? 'create' : 'update',
      formData.value.id,
      undefined,
      undefined
    )
  }
}

async function handleBatchAction(action: TableAction, selectedItems: User[]) {
  if (action.type === 'delete') {
    await handleBatchDelete(selectedItems)
  }
}

async function handleBatchDelete(users: User[]) {
  const ids = users.map(user => user.id)
  await userCrud.batchRemove(ids)
}

// 角色分配相关方法
async function handleAssignRole(user: User) {
  selectedUser.value = user
  selectedRoleIds.value = [...user.roleIds]
  
  // 加载可用角色
  try {
    const roles = await mockRoleApi.list({ page: 1, pageSize: 100 })
    availableRoles.value = roles.items || []
  } catch (error) {
    console.error('Load roles error:', error)
    userCrud.showError('加载角色列表失败')
  }
  
  showRoleAssign.value = true
}

function closeRoleAssign() {
  showRoleAssign.value = false
  selectedUser.value = null
  selectedRoleIds.value = []
}

async function handleRoleAssign() {
  if (!selectedUser.value) return

  if (!hasPermission(PERMISSIONS.USER_UPDATE)) {
    uni.showToast({
      title: '没有权限分配用户角色',
      icon: 'error'
    })
    return
  }

  try {
    assigningRole.value = true

    const beforeData = { roleIds: [...selectedUser.value.roleIds] }
    const afterData = { roleIds: [...selectedRoleIds.value] }

    // 这里应该调用实际的角色分配API
    // await userExtendedApi.assignRoles(selectedUser.value.id, selectedRoleIds.value)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 记录操作日志
    await logUserOperation('assign-roles', selectedUser.value.id, beforeData, afterData)

    userCrud.showSuccess('角色分配成功')
    closeRoleAssign()

    // 刷新用户列表
    await userCrud.refresh()
  } catch (error) {
    console.error('Assign role error:', error)
    userCrud.showError('角色分配失败')

    // 记录失败日志
    await logUserOperation('assign-roles', selectedUser.value?.id, undefined, undefined)
  } finally {
    assigningRole.value = false
  }
}

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style lang="scss" scoped>
.user-management {
  height: 100%;
  display: flex;
  flex-direction: column;

  .management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .action-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background-color: #fff;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;

          &:hover {
            background-color: #40a9ff;
          }
        }

        .btn-icon {
          font-size: 12px;
        }
      }
    }
  }

  // 弹窗样式
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background-color: #fff;
      border-radius: 8px;
      width: 90%;
      max-width: 600px;
      max-height: 80vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #e8e8e8;

        .modal-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .modal-close {
          cursor: pointer;
          font-size: 18px;
          color: #999;
          
          &:hover {
            color: #333;
          }
        }
      }

      .modal-body {
        flex: 1;
        padding: 24px;
        overflow-y: auto;
      }
    }
  }

  // 角色分配样式
  .role-assign-content {
    .assign-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .role-list {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 8px;

      .role-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: #f5f5f5;
        }

        .role-checkbox {
          margin-right: 12px;
        }

        .role-info {
          flex: 1;

          .role-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
          }

          .role-desc {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }

    .assign-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #e8e8e8;

      .action-btn {
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;

          &:hover {
            background-color: #40a9ff;
          }

          &:disabled {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
            color: #999;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}
</style>
