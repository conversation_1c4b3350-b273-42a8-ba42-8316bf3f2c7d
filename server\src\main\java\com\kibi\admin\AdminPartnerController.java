package com.kibi.admin;

import com.kibi.entity.Partner;
import com.kibi.entity.Role;
import com.kibi.entity.User;
import com.kibi.service.PartnerService;
import com.kibi.service.UserService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/admin/partner")
public class AdminPartnerController {

    @Autowired
    private PartnerService partnerService;

    @Autowired
    private JWTUtils jwtUtils;

    /**
     * 获取伴侣列表
     */
    @GetMapping
    public R<list<Partner>> getlist(@RequestHeader("Authorization") String authHeader) {
    }
}