import { ref, reactive, computed } from 'vue'
import type { CrudOperation, QueryParams } from '@/types/global'
import type { PageResult } from '@/types/global'

/**
 * 通用CRUD操作组合式函数
 */
export function useCrud<T = any>(api: CrudOperation<T>) {
  // 响应式状态
  const loading = ref(false)
  const submitting = ref(false)
  const data = ref<T[]>([])
  const total = ref(0)
  const currentItem = ref<T | null>(null)
  
  // 分页状态
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0
  })
  
  // 查询参数
  const queryParams = reactive<QueryParams>({
    page: 1,
    size: 10,
    keyword: '',
    sortBy: '',
    sortOrder: 'asc'
  })
  
  // 错误状态
  const error = ref<string | null>(null)
  
  // 计算属性
  const hasData = computed(() => data.value.length > 0)
  const isEmpty = computed(() => !loading.value && !hasData.value)
  const totalPages = computed(() => Math.ceil(pagination.total / pagination.pageSize))
  
  /**
   * 显示成功提示
   */
  const showSuccess = (message: string) => {
    uni.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    })
  }
  
  /**
   * 显示错误提示
   */
  const showError = (message: string) => {
    uni.showToast({
      title: message,
      icon: 'error',
      duration: 3000
    })
    error.value = message
  }
  
  /**
   * 清除错误状态
   */
  const clearError = () => {
    error.value = null
  }
  
  /**
   * 获取列表数据
   */
  const fetchList = async (params?: Partial<QueryParams>) => {
    try {
      loading.value = true
      clearError()
      
      const mergedParams = { ...queryParams, ...params }
      const result: PageResult<T> = await api.list(mergedParams)
      
      data.value = result.records || []
      pagination.total = result.total || 0
      pagination.current = result.current || 1
      pagination.pageSize = result.size || 10
      
      return result
    } catch (err: any) {
      const message = err.message || '获取数据失败'
      showError(message)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 创建记录
   */
  const create = async (itemData: Partial<T>) => {
    try {
      submitting.value = true
      clearError()
      
      const result = await api.create(itemData)
      
      // 刷新列表数据
      await fetchList()
      
      showSuccess('创建成功')
      return result
    } catch (err: any) {
      const message = err.message || '创建失败'
      showError(message)
      throw err
    } finally {
      submitting.value = false
    }
  }
  
  /**
   * 更新记录
   */
  const update = async (id: number, itemData: Partial<T>) => {
    try {
      submitting.value = true
      clearError()
      
      const result = await api.update(id, itemData)
      
      // 更新本地数据
      const index = data.value.findIndex((item: any) => item.id === id)
      if (index !== -1) {
        data.value[index] = { ...data.value[index], ...result }
      }
      
      showSuccess('更新成功')
      return result
    } catch (err: any) {
      const message = err.message || '更新失败'
      showError(message)
      throw err
    } finally {
      submitting.value = false
    }
  }
  
  /**
   * 删除记录
   */
  const remove = async (id: number) => {
    try {
      loading.value = true
      clearError()
      
      await api.delete(id)
      
      // 从本地数据中移除
      const index = data.value.findIndex((item: any) => item.id === id)
      if (index !== -1) {
        data.value.splice(index, 1)
        pagination.total -= 1
      }
      
      // 如果当前页没有数据且不是第一页，则跳转到上一页
      if (data.value.length === 0 && pagination.current > 1) {
        pagination.current -= 1
        await fetchList()
      }
      
      showSuccess('删除成功')
      return true
    } catch (err: any) {
      const message = err.message || '删除失败'
      showError(message)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 批量删除
   */
  const batchRemove = async (ids: number[]) => {
    try {
      loading.value = true
      clearError()
      
      // 并发删除所有选中的记录
      await Promise.all(ids.map(id => api.delete(id)))
      
      // 刷新列表数据
      await fetchList()
      
      showSuccess(`成功删除 ${ids.length} 条记录`)
      return true
    } catch (err: any) {
      const message = err.message || '批量删除失败'
      showError(message)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取单条记录详情
   */
  const fetchDetail = async (id: number) => {
    try {
      loading.value = true
      clearError()
      
      const result = await api.get(id)
      currentItem.value = result
      
      return result
    } catch (err: any) {
      const message = err.message || '获取详情失败'
      showError(message)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 搜索
   */
  const search = async (keyword: string) => {
    queryParams.keyword = keyword
    queryParams.page = 1
    pagination.current = 1
    await fetchList()
  }
  
  /**
   * 排序
   */
  const sort = async (field: string, order: 'asc' | 'desc') => {
    queryParams.sortBy = field
    queryParams.sortOrder = order
    queryParams.page = 1
    pagination.current = 1
    await fetchList()
  }
  
  /**
   * 分页
   */
  const changePage = async (page: number) => {
    queryParams.page = page
    pagination.current = page
    await fetchList()
  }
  
  /**
   * 改变每页大小
   */
  const changePageSize = async (pageSize: number) => {
    queryParams.size = pageSize
    queryParams.page = 1
    pagination.pageSize = pageSize
    pagination.current = 1
    await fetchList()
  }
  
  /**
   * 刷新数据
   */
  const refresh = async () => {
    await fetchList()
  }
  
  /**
   * 重置查询条件
   */
  const reset = async () => {
    Object.assign(queryParams, {
      page: 1,
      size: 10,
      keyword: '',
      sortBy: '',
      sortOrder: 'asc'
    })
    
    Object.assign(pagination, {
      current: 1,
      pageSize: 10,
      total: 0
    })
    
    await fetchList()
  }
  
  return {
    // 状态
    loading,
    submitting,
    data,
    total,
    currentItem,
    pagination,
    queryParams,
    error,
    
    // 计算属性
    hasData,
    isEmpty,
    totalPages,
    
    // 方法
    fetchList,
    create,
    update,
    remove,
    batchRemove,
    fetchDetail,
    search,
    sort,
    changePage,
    changePageSize,
    refresh,
    reset,
    clearError,
    showSuccess,
    showError
  }
}