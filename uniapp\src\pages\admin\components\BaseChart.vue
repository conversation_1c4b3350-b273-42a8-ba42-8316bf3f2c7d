<template>
  <view class="base-chart">
    <canvas 
      :canvas-id="canvasId"
      :id="canvasId"
      class="chart-canvas"
      :style="{ width: width + 'px', height: height + 'px' }"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    ></canvas>
    
    <!-- 图表标题 -->
    <view v-if="title" class="chart-title">{{ title }}</view>
    
    <!-- 图例 -->
    <view v-if="showLegend && legend.length > 0" class="chart-legend">
      <view 
        v-for="(item, index) in legend" 
        :key="index"
        class="legend-item"
      >
        <view 
          class="legend-color" 
          :style="{ backgroundColor: item.color }"
        ></view>
        <text class="legend-label">{{ item.label }}</text>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="chart-loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
    
    <!-- 空数据状态 -->
    <view v-if="!loading && (!data || data.length === 0)" class="chart-empty">
      <text>暂无数据</text>
    </view>
    
    <!-- 悬停提示框 -->
    <view 
      v-if="tooltip.visible" 
      class="chart-tooltip"
      :style="{ 
        left: tooltip.x + 'px', 
        top: tooltip.y + 'px' 
      }"
    >
      <view class="tooltip-title">{{ tooltip.title }}</view>
      <view class="tooltip-content">{{ tooltip.content }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

interface ChartDataItem {
  label: string
  value: number
  color?: string
}

interface LegendItem {
  label: string
  color: string
}

interface Props {
  /** 图表类型 */
  type: 'line' | 'bar' | 'pie' | 'gauge'
  /** 图表数据 */
  data: ChartDataItem[]
  /** 图表标题 */
  title?: string
  /** 图表宽度 */
  width?: number
  /** 图表高度 */
  height?: number
  /** 是否显示图例 */
  showLegend?: boolean
  /** 加载状态 */
  loading?: boolean
  /** 图表配置 */
  options?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  type: 'line',
  data: () => [],
  width: 400,
  height: 300,
  showLegend: true,
  loading: false,
  options: () => ({})
})

const emit = defineEmits<{
  click: [item: ChartDataItem, index: number]
  hover: [item: ChartDataItem, index: number]
}>()

// 响应式数据
const canvasId = ref(`chart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)
const legend = ref<LegendItem[]>([])
const ctx = ref<any>(null)
const tooltip = ref({
  visible: false,
  x: 0,
  y: 0,
  title: '',
  content: ''
})

// 默认颜色配置
const defaultColors = [
  '#1890ff', '#52c41a', '#faad14', '#ff4d4f', 
  '#722ed1', '#13c2c2', '#eb2f96', '#f5222d'
]

// 方法
const initCanvas = () => {
  ctx.value = uni.createCanvasContext(canvasId.value)
  if (ctx.value) {
    drawChart()
  }
}

const drawChart = () => {
  if (!ctx.value || !props.data || props.data.length === 0) return
  
  // 清空画布
  ctx.value.clearRect(0, 0, props.width, props.height)
  
  // 根据图表类型绘制
  switch (props.type) {
    case 'line':
      drawLineChart()
      break
    case 'bar':
      drawBarChart()
      break
    case 'pie':
      drawPieChart()
      break
    case 'gauge':
      drawGaugeChart()
      break
  }
  
  ctx.value.draw()
  updateLegend()
}

const drawLineChart = () => {
  const padding = 40
  const chartWidth = props.width - padding * 2
  const chartHeight = props.height - padding * 2
  
  if (props.data.length === 0) return
  
  const maxValue = Math.max(...props.data.map(item => item.value))
  const minValue = Math.min(...props.data.map(item => item.value))
  const valueRange = maxValue - minValue || 1
  
  // 绘制坐标轴
  ctx.value.setStrokeStyle('#e8e8e8')
  ctx.value.setLineWidth(1)
  
  // Y轴
  ctx.value.beginPath()
  ctx.value.moveTo(padding, padding)
  ctx.value.lineTo(padding, props.height - padding)
  ctx.value.stroke()
  
  // X轴
  ctx.value.beginPath()
  ctx.value.moveTo(padding, props.height - padding)
  ctx.value.lineTo(props.width - padding, props.height - padding)
  ctx.value.stroke()
  
  // 绘制数据线
  ctx.value.setStrokeStyle(defaultColors[0])
  ctx.value.setLineWidth(2)
  ctx.value.beginPath()
  
  props.data.forEach((item, index) => {
    const x = padding + (index / (props.data.length - 1)) * chartWidth
    const y = props.height - padding - ((item.value - minValue) / valueRange) * chartHeight
    
    if (index === 0) {
      ctx.value.moveTo(x, y)
    } else {
      ctx.value.lineTo(x, y)
    }
  })
  
  ctx.value.stroke()
  
  // 绘制数据点
  ctx.value.setFillStyle(defaultColors[0])
  props.data.forEach((item, index) => {
    const x = padding + (index / (props.data.length - 1)) * chartWidth
    const y = props.height - padding - ((item.value - minValue) / valueRange) * chartHeight
    
    ctx.value.beginPath()
    ctx.value.arc(x, y, 4, 0, 2 * Math.PI)
    ctx.value.fill()
  })
}

const drawBarChart = () => {
  const padding = 40
  const chartWidth = props.width - padding * 2
  const chartHeight = props.height - padding * 2
  
  if (props.data.length === 0) return
  
  const maxValue = Math.max(...props.data.map(item => item.value))
  const barWidth = chartWidth / props.data.length * 0.6
  const barSpacing = chartWidth / props.data.length * 0.4
  
  props.data.forEach((item, index) => {
    const barHeight = (item.value / maxValue) * chartHeight
    const x = padding + index * (barWidth + barSpacing) + barSpacing / 2
    const y = props.height - padding - barHeight
    
    ctx.value.setFillStyle(item.color || defaultColors[index % defaultColors.length])
    ctx.value.fillRect(x, y, barWidth, barHeight)
  })
}

const drawPieChart = () => {
  const centerX = props.width / 2
  const centerY = props.height / 2
  const radius = Math.min(props.width, props.height) / 2 - 40
  
  if (props.data.length === 0) return
  
  const total = props.data.reduce((sum, item) => sum + item.value, 0)
  let currentAngle = -Math.PI / 2
  
  props.data.forEach((item, index) => {
    const sliceAngle = (item.value / total) * 2 * Math.PI
    
    ctx.value.setFillStyle(item.color || defaultColors[index % defaultColors.length])
    ctx.value.beginPath()
    ctx.value.moveTo(centerX, centerY)
    ctx.value.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
    ctx.value.closePath()
    ctx.value.fill()
    
    currentAngle += sliceAngle
  })
}

const drawGaugeChart = () => {
  const centerX = props.width / 2
  const centerY = props.height / 2
  const radius = Math.min(props.width, props.height) / 2 - 40
  
  if (props.data.length === 0) return
  
  const value = props.data[0]?.value || 0
  const maxValue = props.options.max || 100
  const percentage = Math.min(value / maxValue, 1)
  
  // 绘制背景圆弧
  ctx.value.setStrokeStyle('#f0f0f0')
  ctx.value.setLineWidth(20)
  ctx.value.beginPath()
  ctx.value.arc(centerX, centerY, radius, Math.PI, 2 * Math.PI)
  ctx.value.stroke()
  
  // 绘制进度圆弧
  const progressAngle = Math.PI * percentage
  ctx.value.setStrokeStyle(defaultColors[0])
  ctx.value.setLineWidth(20)
  ctx.value.beginPath()
  ctx.value.arc(centerX, centerY, radius, Math.PI, Math.PI + progressAngle)
  ctx.value.stroke()
  
  // 绘制数值文本
  ctx.value.setFillStyle('#333')
  ctx.value.setFontSize(24)
  ctx.value.setTextAlign('center')
  ctx.value.fillText(`${value}%`, centerX, centerY + 10)
}

const updateLegend = () => {
  legend.value = props.data.map((item, index) => ({
    label: item.label,
    color: item.color || defaultColors[index % defaultColors.length]
  }))
}

const handleTouchStart = (e: any) => {
  // 处理触摸开始事件
  e.preventDefault()
}

const handleTouchMove = (e: any) => {
  // 处理触摸移动事件
  e.preventDefault()
}

const handleTouchEnd = (e: any) => {
  // 处理触摸结束事件，实现点击交互
  e.preventDefault()
  
  const touch = e.changedTouches[0]
  if (!touch) return
  
  // 获取canvas的位置信息
  uni.createSelectorQuery().select(`#${canvasId.value}`).boundingClientRect((rect: any) => {
    if (!rect) return
    
    const x = touch.clientX - rect.left
    const y = touch.clientY - rect.top
    
    // 根据图表类型和坐标计算点击的数据项
    const clickedItem = getClickedDataItem(x, y)
    if (clickedItem) {
      // 显示tooltip
      showTooltip(x, y, clickedItem.item, clickedItem.index)
      emit('click', clickedItem.item, clickedItem.index)
    } else {
      hideTooltip()
    }
  }).exec()
}

const showTooltip = (x: number, y: number, item: ChartDataItem, index: number) => {
  tooltip.value = {
    visible: true,
    x: x,
    y: y - 10,
    title: item.label,
    content: `值: ${item.value}`
  }
  
  // 3秒后自动隐藏tooltip
  setTimeout(() => {
    hideTooltip()
  }, 3000)
}

const hideTooltip = () => {
  tooltip.value.visible = false
}

const getClickedDataItem = (x: number, y: number): { item: ChartDataItem, index: number } | null => {
  const padding = 40
  const chartWidth = props.width - padding * 2
  const chartHeight = props.height - padding * 2
  
  switch (props.type) {
    case 'line':
      return getLineChartClickedItem(x, y, padding, chartWidth, chartHeight)
    case 'bar':
      return getBarChartClickedItem(x, y, padding, chartWidth, chartHeight)
    case 'pie':
      return getPieChartClickedItem(x, y)
    case 'gauge':
      return getGaugeChartClickedItem(x, y)
    default:
      return null
  }
}

const getLineChartClickedItem = (x: number, y: number, padding: number, chartWidth: number, chartHeight: number) => {
  if (props.data.length === 0) return null
  
  const minValue = Math.min(...props.data.map(item => item.value))
  const maxValue = Math.max(...props.data.map(item => item.value))
  const valueRange = maxValue - minValue || 1
  
  // 找到最接近的数据点
  let closestIndex = 0
  let minDistance = Infinity
  
  props.data.forEach((item, index) => {
    const pointX = padding + (index / (props.data.length - 1)) * chartWidth
    const pointY = props.height - padding - ((item.value - minValue) / valueRange) * chartHeight
    
    const distance = Math.sqrt(Math.pow(x - pointX, 2) + Math.pow(y - pointY, 2))
    if (distance < minDistance && distance < 20) { // 20px的点击范围
      minDistance = distance
      closestIndex = index
    }
  })
  
  return minDistance < 20 ? { item: props.data[closestIndex], index: closestIndex } : null
}

const getBarChartClickedItem = (x: number, y: number, padding: number, chartWidth: number, chartHeight: number) => {
  if (props.data.length === 0) return null
  
  const barWidth = chartWidth / props.data.length * 0.6
  const barSpacing = chartWidth / props.data.length * 0.4
  const maxValue = Math.max(...props.data.map(item => item.value))
  
  for (let index = 0; index < props.data.length; index++) {
    const barHeight = (props.data[index].value / maxValue) * chartHeight
    const barX = padding + index * (barWidth + barSpacing) + barSpacing / 2
    const barY = props.height - padding - barHeight
    
    if (x >= barX && x <= barX + barWidth && y >= barY && y <= barY + barHeight) {
      return { item: props.data[index], index }
    }
  }
  
  return null
}

const getPieChartClickedItem = (x: number, y: number) => {
  if (props.data.length === 0) return null
  
  const centerX = props.width / 2
  const centerY = props.height / 2
  const radius = Math.min(props.width, props.height) / 2 - 40
  
  // 检查是否在圆形范围内
  const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2))
  if (distance > radius) return null
  
  // 计算角度
  const angle = Math.atan2(y - centerY, x - centerX) + Math.PI / 2
  const normalizedAngle = angle < 0 ? angle + 2 * Math.PI : angle
  
  const total = props.data.reduce((sum, item) => sum + item.value, 0)
  let currentAngle = 0
  
  for (let index = 0; index < props.data.length; index++) {
    const sliceAngle = (props.data[index].value / total) * 2 * Math.PI
    
    if (normalizedAngle >= currentAngle && normalizedAngle <= currentAngle + sliceAngle) {
      return { item: props.data[index], index }
    }
    
    currentAngle += sliceAngle
  }
  
  return null
}

const getGaugeChartClickedItem = (x: number, y: number) => {
  if (props.data.length === 0) return null
  
  const centerX = props.width / 2
  const centerY = props.height / 2
  const radius = Math.min(props.width, props.height) / 2 - 40
  
  // 检查是否在仪表盘范围内
  const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2))
  if (distance <= radius + 20 && distance >= radius - 20) {
    return { item: props.data[0], index: 0 }
  }
  
  return null
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    drawChart()
  })
}, { deep: true })

watch(() => props.type, () => {
  nextTick(() => {
    drawChart()
  })
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initCanvas()
  })
})

// 暴露方法
const refresh = () => {
  drawChart()
}

defineExpose({
  refresh
})
</script>

<style lang="scss" scoped>
.base-chart {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  .chart-canvas {
    border-radius: 4px;
  }

  .chart-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
    text-align: center;
  }

  .chart-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
    margin-top: 12px;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;

      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
      }

      .legend-label {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;

    .loading-spinner {
      width: 24px;
      height: 24px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  .chart-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-size: 14px;
  }

  .chart-tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1000;
    transform: translate(-50%, -100%);
    margin-top: -8px;

    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.8);
    }

    .tooltip-title {
      font-weight: 600;
      margin-bottom: 4px;
    }

    .tooltip-content {
      font-size: 11px;
      opacity: 0.9;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 暗色主题支持
.admin-layout.dark-theme .base-chart {
  .chart-title {
    color: #fff;
  }

  .legend-label {
    color: #ccc;
  }

  .chart-loading,
  .chart-empty {
    color: #999;
  }
}
</style>