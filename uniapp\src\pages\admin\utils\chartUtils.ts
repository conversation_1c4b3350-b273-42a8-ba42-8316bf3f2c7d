/**
 * 图表工具函数
 */

export interface ChartDataPoint {
  label: string
  value: number
  color?: string
}

export interface ChartOptions {
  width?: number
  height?: number
  padding?: number
  colors?: string[]
  showGrid?: boolean
  showLegend?: boolean
  animation?: boolean
}

/**
 * 默认颜色配置
 */
export const DEFAULT_COLORS = [
  '#1890ff', '#52c41a', '#faad14', '#ff4d4f', 
  '#722ed1', '#13c2c2', '#eb2f96', '#f5222d',
  '#fa8c16', '#a0d911', '#2f54eb', '#08979c'
]

/**
 * 格式化数值显示
 */
export const formatValue = (value: number, type: 'number' | 'percentage' | 'bytes' | 'time' = 'number'): string => {
  switch (type) {
    case 'percentage':
      return `${value.toFixed(1)}%`
    case 'bytes':
      if (value >= 1024 * 1024 * 1024) {
        return `${(value / (1024 * 1024 * 1024)).toFixed(1)}GB`
      } else if (value >= 1024 * 1024) {
        return `${(value / (1024 * 1024)).toFixed(1)}MB`
      } else if (value >= 1024) {
        return `${(value / 1024).toFixed(1)}KB`
      }
      return `${value}B`
    case 'time':
      if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}s`
      }
      return `${value}ms`
    default:
      if (value >= 10000) {
        return `${(value / 10000).toFixed(1)}w`
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}k`
      }
      return value.toString()
  }
}

/**
 * 生成渐变色
 */
export const generateGradientColors = (startColor: string, endColor: string, steps: number): string[] => {
  const colors: string[] = []
  
  // 解析颜色值
  const parseColor = (color: string) => {
    const hex = color.replace('#', '')
    return {
      r: parseInt(hex.substring(0, 2), 16),
      g: parseInt(hex.substring(2, 4), 16),
      b: parseInt(hex.substring(4, 6), 16)
    }
  }
  
  const start = parseColor(startColor)
  const end = parseColor(endColor)
  
  for (let i = 0; i < steps; i++) {
    const ratio = i / (steps - 1)
    const r = Math.round(start.r + (end.r - start.r) * ratio)
    const g = Math.round(start.g + (end.g - start.g) * ratio)
    const b = Math.round(start.b + (end.b - start.b) * ratio)
    
    colors.push(`#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`)
  }
  
  return colors
}

/**
 * 计算图表数据的统计信息
 */
export const calculateStats = (data: ChartDataPoint[]) => {
  if (!data || data.length === 0) {
    return {
      min: 0,
      max: 0,
      avg: 0,
      sum: 0,
      count: 0
    }
  }
  
  const values = data.map(item => item.value)
  const sum = values.reduce((acc, val) => acc + val, 0)
  
  return {
    min: Math.min(...values),
    max: Math.max(...values),
    avg: sum / values.length,
    sum,
    count: values.length
  }
}

/**
 * 数据平滑处理
 */
export const smoothData = (data: ChartDataPoint[], windowSize: number = 3): ChartDataPoint[] => {
  if (!data || data.length <= windowSize) {
    return data
  }
  
  const smoothedData: ChartDataPoint[] = []
  
  for (let i = 0; i < data.length; i++) {
    const start = Math.max(0, i - Math.floor(windowSize / 2))
    const end = Math.min(data.length, i + Math.ceil(windowSize / 2))
    
    const windowData = data.slice(start, end)
    const avgValue = windowData.reduce((sum, item) => sum + item.value, 0) / windowData.length
    
    smoothedData.push({
      ...data[i],
      value: avgValue
    })
  }
  
  return smoothedData
}

/**
 * 生成时间序列标签
 */
export const generateTimeLabels = (count: number, interval: 'hour' | 'day' | 'week' | 'month' = 'day'): string[] => {
  const labels: string[] = []
  const now = new Date()
  
  for (let i = count - 1; i >= 0; i--) {
    const date = new Date(now)
    
    switch (interval) {
      case 'hour':
        date.setHours(date.getHours() - i)
        labels.push(`${date.getHours()}:00`)
        break
      case 'day':
        date.setDate(date.getDate() - i)
        labels.push(`${date.getMonth() + 1}/${date.getDate()}`)
        break
      case 'week':
        date.setDate(date.getDate() - i * 7)
        labels.push(`${date.getMonth() + 1}/${date.getDate()}`)
        break
      case 'month':
        date.setMonth(date.getMonth() - i)
        labels.push(`${date.getFullYear()}/${date.getMonth() + 1}`)
        break
    }
  }
  
  return labels
}

/**
 * 检测数据异常值
 */
export const detectOutliers = (data: ChartDataPoint[], threshold: number = 2): ChartDataPoint[] => {
  const stats = calculateStats(data)
  const outliers: ChartDataPoint[] = []
  
  data.forEach(item => {
    const deviation = Math.abs(item.value - stats.avg)
    const standardDeviation = Math.sqrt(
      data.reduce((sum, d) => sum + Math.pow(d.value - stats.avg, 2), 0) / data.length
    )
    
    if (deviation > threshold * standardDeviation) {
      outliers.push(item)
    }
  })
  
  return outliers
}

/**
 * 数据插值处理（填补缺失数据）
 */
export const interpolateData = (data: ChartDataPoint[], targetLength: number): ChartDataPoint[] => {
  if (!data || data.length === 0) {
    return []
  }
  
  if (data.length >= targetLength) {
    return data.slice(0, targetLength)
  }
  
  const interpolated: ChartDataPoint[] = [...data]
  const step = (data.length - 1) / (targetLength - 1)
  
  for (let i = data.length; i < targetLength; i++) {
    const index = i * step
    const lowerIndex = Math.floor(index)
    const upperIndex = Math.ceil(index)
    
    if (lowerIndex === upperIndex || upperIndex >= data.length) {
      interpolated.push({ ...data[lowerIndex] })
    } else {
      const ratio = index - lowerIndex
      const lowerValue = data[lowerIndex].value
      const upperValue = data[upperIndex].value
      const interpolatedValue = lowerValue + (upperValue - lowerValue) * ratio
      
      interpolated.push({
        label: `插值${i}`,
        value: interpolatedValue,
        color: data[lowerIndex].color
      })
    }
  }
  
  return interpolated
}

/**
 * 图表主题配置
 */
export const CHART_THEMES = {
  light: {
    backgroundColor: '#ffffff',
    textColor: '#333333',
    gridColor: '#e8e8e8',
    axisColor: '#d9d9d9'
  },
  dark: {
    backgroundColor: '#1f1f1f',
    textColor: '#ffffff',
    gridColor: '#434343',
    axisColor: '#666666'
  }
}

/**
 * 获取主题配置
 */
export const getThemeConfig = (theme: 'light' | 'dark' = 'light') => {
  return CHART_THEMES[theme]
}

/**
 * 数据聚合函数
 */
export const aggregateData = (
  data: ChartDataPoint[], 
  groupBy: 'hour' | 'day' | 'week' | 'month',
  aggregateType: 'sum' | 'avg' | 'max' | 'min' = 'sum'
): ChartDataPoint[] => {
  const groups: { [key: string]: ChartDataPoint[] } = {}
  
  data.forEach(item => {
    const date = new Date(item.label)
    let groupKey: string
    
    switch (groupBy) {
      case 'hour':
        groupKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`
        break
      case 'day':
        groupKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`
        break
      case 'week':
        const weekStart = new Date(date)
        weekStart.setDate(date.getDate() - date.getDay())
        groupKey = `${weekStart.getFullYear()}-${weekStart.getMonth()}-${weekStart.getDate()}`
        break
      case 'month':
        groupKey = `${date.getFullYear()}-${date.getMonth()}`
        break
      default:
        groupKey = item.label
    }
    
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(item)
  })
  
  return Object.entries(groups).map(([key, groupData]) => {
    let value: number
    const values = groupData.map(d => d.value)
    
    switch (aggregateType) {
      case 'sum':
        value = values.reduce((sum, val) => sum + val, 0)
        break
      case 'avg':
        value = values.reduce((sum, val) => sum + val, 0) / values.length
        break
      case 'max':
        value = Math.max(...values)
        break
      case 'min':
        value = Math.min(...values)
        break
      default:
        value = values.reduce((sum, val) => sum + val, 0)
    }
    
    return {
      label: key,
      value,
      color: groupData[0].color
    }
  })
}

/**
 * 计算移动平均线
 */
export const calculateMovingAverage = (data: ChartDataPoint[], period: number): ChartDataPoint[] => {
  if (!data || data.length < period) {
    return data
  }
  
  const movingAverage: ChartDataPoint[] = []
  
  for (let i = period - 1; i < data.length; i++) {
    const slice = data.slice(i - period + 1, i + 1)
    const average = slice.reduce((sum, item) => sum + item.value, 0) / period
    
    movingAverage.push({
      label: data[i].label,
      value: average,
      color: data[i].color
    })
  }
  
  return movingAverage
}

/**
 * 数据趋势分析
 */
export const analyzeTrend = (data: ChartDataPoint[]): {
  trend: 'up' | 'down' | 'stable'
  slope: number
  correlation: number
  volatility: number
} => {
  if (!data || data.length < 2) {
    return { trend: 'stable', slope: 0, correlation: 0, volatility: 0 }
  }
  
  const values = data.map(item => item.value)
  const n = values.length
  
  // 计算线性回归斜率
  const xSum = (n * (n - 1)) / 2
  const ySum = values.reduce((sum, val) => sum + val, 0)
  const xySum = values.reduce((sum, val, index) => sum + val * index, 0)
  const xxSum = (n * (n - 1) * (2 * n - 1)) / 6
  
  const slope = (n * xySum - xSum * ySum) / (n * xxSum - xSum * xSum)
  
  // 计算相关系数
  const xMean = xSum / n
  const yMean = ySum / n
  
  let numerator = 0
  let xDenominator = 0
  let yDenominator = 0
  
  values.forEach((val, index) => {
    const xDiff = index - xMean
    const yDiff = val - yMean
    numerator += xDiff * yDiff
    xDenominator += xDiff * xDiff
    yDenominator += yDiff * yDiff
  })
  
  const correlation = numerator / Math.sqrt(xDenominator * yDenominator)
  
  // 计算波动率
  const variance = values.reduce((sum, val) => sum + Math.pow(val - yMean, 2), 0) / n
  const volatility = Math.sqrt(variance) / yMean
  
  // 判断趋势
  let trend: 'up' | 'down' | 'stable'
  if (Math.abs(slope) < 0.1) {
    trend = 'stable'
  } else if (slope > 0) {
    trend = 'up'
  } else {
    trend = 'down'
  }
  
  return { trend, slope, correlation, volatility }
}

/**
 * 生成热力图数据
 */
export const generateHeatmapData = (
  data: ChartDataPoint[],
  rows: number,
  cols: number
): { x: number; y: number; value: number; color: string }[] => {
  const heatmapData: { x: number; y: number; value: number; color: string }[] = []
  const maxValue = Math.max(...data.map(d => d.value))
  const minValue = Math.min(...data.map(d => d.value))
  const range = maxValue - minValue
  
  for (let i = 0; i < Math.min(data.length, rows * cols); i++) {
    const x = i % cols
    const y = Math.floor(i / cols)
    const normalizedValue = range > 0 ? (data[i].value - minValue) / range : 0
    
    // 根据数值生成颜色强度
    const intensity = Math.floor(normalizedValue * 255)
    const color = `rgba(24, 144, 255, ${normalizedValue})`
    
    heatmapData.push({
      x,
      y,
      value: data[i].value,
      color
    })
  }
  
  return heatmapData
}

/**
 * 数据预测（简单线性预测）
 */
export const predictData = (data: ChartDataPoint[], periods: number): ChartDataPoint[] => {
  if (!data || data.length < 2) {
    return []
  }
  
  const trend = analyzeTrend(data)
  const lastValue = data[data.length - 1].value
  const predictions: ChartDataPoint[] = []
  
  for (let i = 1; i <= periods; i++) {
    const predictedValue = lastValue + (trend.slope * i)
    predictions.push({
      label: `预测${i}`,
      value: Math.max(0, predictedValue), // 确保预测值不为负
      color: '#ff7875' // 预测数据使用不同颜色
    })
  }
  
  return predictions
}

/**
 * 数据导出功能
 */
export const exportChartData = (
  data: ChartDataPoint[],
  format: 'csv' | 'json' = 'csv'
): string => {
  if (format === 'json') {
    return JSON.stringify(data, null, 2)
  }
  
  // CSV格式
  const headers = ['标签', '数值', '颜色']
  const csvRows = [headers.join(',')]
  
  data.forEach(item => {
    const row = [
      `"${item.label}"`,
      item.value.toString(),
      `"${item.color || ''}"`
    ]
    csvRows.push(row.join(','))
  })
  
  return csvRows.join('\n')
}

/**
 * 图表性能优化 - 数据采样
 */
export const sampleData = (
  data: ChartDataPoint[],
  maxPoints: number = 100,
  method: 'uniform' | 'peak' | 'adaptive' = 'uniform'
): ChartDataPoint[] => {
  if (!data || data.length <= maxPoints) {
    return data
  }
  
  switch (method) {
    case 'uniform':
      // 均匀采样
      const step = data.length / maxPoints
      const sampledData: ChartDataPoint[] = []
      for (let i = 0; i < maxPoints; i++) {
        const index = Math.floor(i * step)
        sampledData.push(data[index])
      }
      return sampledData
      
    case 'peak':
      // 保留峰值点
      const stats = calculateStats(data)
      const threshold = stats.avg + (stats.max - stats.avg) * 0.5
      
      const peaks = data.filter(item => item.value >= threshold)
      const others = data.filter(item => item.value < threshold)
      
      const peakCount = Math.min(peaks.length, Math.floor(maxPoints * 0.3))
      const otherCount = maxPoints - peakCount
      
      const sampledPeaks = peaks.slice(0, peakCount)
      const sampledOthers = sampleData(others, otherCount, 'uniform')
      
      return [...sampledPeaks, ...sampledOthers].sort((a, b) => 
        data.indexOf(a) - data.indexOf(b)
      )
      
    case 'adaptive':
      // 自适应采样 - 在变化大的地方采样更多点
      const adaptiveSampled: ChartDataPoint[] = [data[0]] // 总是包含第一个点
      let lastIndex = 0
      
      for (let i = 1; i < data.length - 1 && adaptiveSampled.length < maxPoints - 1; i++) {
        const current = data[i]
        const prev = data[lastIndex]
        const next = data[i + 1]
        
        // 计算变化率
        const changeRate = Math.abs(current.value - prev.value) + Math.abs(next.value - current.value)
        const avgChange = (Math.abs(data[i + 1].value - data[i - 1].value)) / 2
        
        // 如果变化率大于平均变化率，则采样这个点
        if (changeRate > avgChange * 1.5) {
          adaptiveSampled.push(current)
          lastIndex = i
        }
      }
      
      adaptiveSampled.push(data[data.length - 1]) // 总是包含最后一个点
      return adaptiveSampled
      
    default:
      return data.slice(0, maxPoints)
  }
}