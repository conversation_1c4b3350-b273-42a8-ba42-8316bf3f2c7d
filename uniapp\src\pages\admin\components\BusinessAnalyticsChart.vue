<template>
  <view class="business-analytics-chart">
    <BaseChart
      :type="chartType"
      :data="chartData"
      title="业务分析"
      :width="400"
      :height="300"
      :loading="loading"
      :show-legend="true"
      :options="chartOptions"
      @click="handleChartClick"
      @hover="handleChartHover"
    />
    
    <!-- 图表控制面板 -->
    <view class="chart-controls">
      <!-- 图表类型切换 -->
      <view class="control-group">
        <text class="control-label">图表类型:</text>
        <view class="chart-type-buttons">
          <view 
            v-for="type in chartTypes" 
            :key="type.value"
            :class="['type-btn', { active: chartType === type.value }]"
            @click="selectChartType(type.value)"
          >
            <text class="iconfont" :class="type.icon"></text>
            <text>{{ type.label }}</text>
          </view>
        </view>
      </view>
      
      <!-- 数据维度选择 -->
      <view class="control-group">
        <text class="control-label">数据维度:</text>
        <view class="dimension-selector">
          <view 
            v-for="dimension in dataDimensions" 
            :key="dimension.value"
            :class="['dimension-item', { active: selectedDimension === dimension.value }]"
            @click="selectDimension(dimension.value)"
          >
            {{ dimension.label }}
          </view>
        </view>
      </view>
      
      <!-- 时间范围选择 -->
      <view class="control-group">
        <text class="control-label">时间范围:</text>
        <view class="time-range-selector">
          <view 
            v-for="range in timeRanges" 
            :key="range.value"
            :class="['range-item', { active: selectedRange === range.value }]"
            @click="selectTimeRange(range.value)"
          >
            {{ range.label }}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 数据统计信息 -->
    <view class="data-statistics">
      <view class="stat-item">
        <text class="stat-label">总计</text>
        <text class="stat-value">{{ formatNumber(statistics.total) }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">平均值</text>
        <text class="stat-value">{{ formatNumber(statistics.average) }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">最大值</text>
        <text class="stat-value">{{ formatNumber(statistics.max) }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">增长率</text>
        <text :class="['stat-value', statistics.growth >= 0 ? 'positive' : 'negative']">
          {{ statistics.growth >= 0 ? '+' : '' }}{{ statistics.growth.toFixed(1) }}%
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import BaseChart from './BaseChart.vue'
import { useDashboardStore } from '@/stores/dashboard'
import { formatValue } from '../utils/chartUtils'

interface BusinessData {
  date: string
  revenue: number
  orders: number
  users: number
  conversion: number
}

const dashboardStore = useDashboardStore()

// 响应式数据
const loading = ref(false)
const chartType = ref<'line' | 'bar' | 'pie'>('line')
const selectedDimension = ref('revenue')
const selectedRange = ref('7d')
const businessData = ref<BusinessData[]>([])

// 图表类型选项
const chartTypes = [
  { label: '折线图', value: 'line', icon: 'icon-line-chart' },
  { label: '柱状图', value: 'bar', icon: 'icon-bar-chart' },
  { label: '饼图', value: 'pie', icon: 'icon-pie-chart' }
]

// 数据维度选项
const dataDimensions = [
  { label: '营收', value: 'revenue' },
  { label: '订单数', value: 'orders' },
  { label: '用户数', value: 'users' },
  { label: '转化率', value: 'conversion' }
]

// 时间范围选项
const timeRanges = [
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' },
  { label: '1年', value: '1y' }
]

// 计算属性
const chartData = computed(() => {
  if (chartType.value === 'pie') {
    // 饼图显示各维度占比
    const latest = businessData.value[businessData.value.length - 1]
    if (!latest) return []
    
    return [
      { label: '营收', value: latest.revenue, color: '#1890ff' },
      { label: '订单数', value: latest.orders, color: '#52c41a' },
      { label: '用户数', value: latest.users, color: '#faad14' },
      { label: '转化率', value: latest.conversion, color: '#ff4d4f' }
    ]
  } else {
    // 折线图和柱状图显示时间序列数据
    return businessData.value.map(item => ({
      label: formatDate(item.date),
      value: item[selectedDimension.value as keyof BusinessData] as number,
      color: getDimensionColor(selectedDimension.value)
    }))
  }
})

const chartOptions = computed(() => {
  return {
    animation: true,
    showGrid: true,
    responsive: true
  }
})

const statistics = computed(() => {
  if (businessData.value.length === 0) {
    return { total: 0, average: 0, max: 0, growth: 0 }
  }
  
  const values = businessData.value.map(item => item[selectedDimension.value as keyof BusinessData] as number)
  const total = values.reduce((sum, val) => sum + val, 0)
  const average = total / values.length
  const max = Math.max(...values)
  
  // 计算增长率（最后一个值相对于第一个值）
  const firstValue = values[0] || 0
  const lastValue = values[values.length - 1] || 0
  const growth = firstValue > 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0
  
  return { total, average, max, growth }
})

// 方法
const loadBusinessData = async () => {
  loading.value = true
  
  try {
    const data = await dashboardStore.fetchChartData(`business-analytics?dimension=${selectedDimension.value}&range=${selectedRange.value}`)
    
    if (data && Array.isArray(data)) {
      businessData.value = data
    } else {
      // 使用模拟数据
      businessData.value = generateMockBusinessData()
    }
  } catch (error) {
    console.error('加载业务分析数据失败:', error)
    businessData.value = generateMockBusinessData()
  } finally {
    loading.value = false
  }
}

const generateMockBusinessData = (): BusinessData[] => {
  const data: BusinessData[] = []
  const days = selectedRange.value === '7d' ? 7 : selectedRange.value === '30d' ? 30 : selectedRange.value === '90d' ? 90 : 365
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    
    // 生成有趋势的模拟数据
    const trend = (days - i) / days // 0 到 1 的趋势
    const randomFactor = 0.8 + Math.random() * 0.4 // 0.8 到 1.2 的随机因子
    
    data.push({
      date: date.toISOString().split('T')[0],
      revenue: Math.floor((50000 + trend * 20000) * randomFactor),
      orders: Math.floor((200 + trend * 100) * randomFactor),
      users: Math.floor((1000 + trend * 500) * randomFactor),
      conversion: Math.round((2.5 + trend * 1.5) * randomFactor * 100) / 100
    })
  }
  
  return data
}

const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  if (selectedRange.value === '1y') {
    return `${date.getFullYear()}/${date.getMonth() + 1}`
  }
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const formatNumber = (value: number): string => {
  if (selectedDimension.value === 'conversion') {
    return `${value.toFixed(2)}%`
  }
  return formatValue(value)
}

const getDimensionColor = (dimension: string): string => {
  const colorMap = {
    revenue: '#1890ff',
    orders: '#52c41a',
    users: '#faad14',
    conversion: '#ff4d4f'
  }
  return colorMap[dimension] || '#1890ff'
}

const selectChartType = (type: 'line' | 'bar' | 'pie') => {
  chartType.value = type
}

const selectDimension = (dimension: string) => {
  selectedDimension.value = dimension
  loadBusinessData()
}

const selectTimeRange = (range: string) => {
  selectedRange.value = range
  loadBusinessData()
}

const handleChartClick = (item: any, index: number) => {
  if (chartType.value === 'pie') {
    // 饼图点击切换到对应维度的时间序列
    const dimensionMap = {
      '营收': 'revenue',
      '订单数': 'orders',
      '用户数': 'users',
      '转化率': 'conversion'
    }
    const dimension = dimensionMap[item.label]
    if (dimension) {
      selectedDimension.value = dimension
      chartType.value = 'line'
      loadBusinessData()
    }
  } else {
    // 时间序列图表点击显示详情
    const data = businessData.value[index]
    if (data) {
      const content = [
        `📅 日期: ${data.date}`,
        `💰 营收: ${formatValue(data.revenue)}`,
        `📦 订单: ${data.orders}`,
        `👥 用户: ${data.users}`,
        `📈 转化率: ${data.conversion}%`
      ].join('\n')
      
      uni.showModal({
        title: '业务数据详情',
        content,
        showCancel: true,
        cancelText: '关闭',
        confirmText: '查看趋势',
        success: (res) => {
          if (res.confirm) {
            // 可以跳转到详细趋势分析页面
            uni.showToast({
              title: '趋势分析功能开发中',
              icon: 'none'
            })
          }
        }
      })
    }
  }
}

const handleChartHover = (item: any, index: number) => {
  // 悬停效果处理
  console.log('悬停在业务数据:', item, index)
}

// 监听变化
watch([selectedDimension, selectedRange], () => {
  loadBusinessData()
})

// 生命周期
onMounted(() => {
  loadBusinessData()
})

// 暴露刷新方法
const refresh = () => {
  loadBusinessData()
}

defineExpose({
  refresh
})
</script>

<style lang="scss" scoped>
.business-analytics-chart {
  .chart-controls {
    margin-top: 16px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;

    .control-group {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .control-label {
        display: block;
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
      }
    }

    .chart-type-buttons {
      display: flex;
      gap: 8px;

      .type-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        padding: 8px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 11px;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 60px;

        .iconfont {
          font-size: 16px;
        }

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.active {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;
        }
      }
    }

    .dimension-selector,
    .time-range-selector {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .dimension-item,
      .range-item {
        padding: 4px 8px;
        border: 1px solid #d9d9d9;
        border-radius: 3px;
        font-size: 11px;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.active {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;
        }
      }
    }
  }

  .data-statistics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
    margin-top: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;

    .stat-item {
      text-align: center;

      .stat-label {
        display: block;
        font-size: 11px;
        color: #999;
        margin-bottom: 4px;
      }

      .stat-value {
        display: block;
        font-size: 14px;
        font-weight: 600;
        color: #333;

        &.positive {
          color: #52c41a;
        }

        &.negative {
          color: #ff4d4f;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .business-analytics-chart {
    .chart-controls {
      .chart-type-buttons {
        .type-btn {
          min-width: 50px;
          padding: 6px 8px;
          font-size: 10px;

          .iconfont {
            font-size: 14px;
          }
        }
      }

      .dimension-selector,
      .time-range-selector {
        .dimension-item,
        .range-item {
          font-size: 10px;
          padding: 3px 6px;
        }
      }
    }

    .data-statistics {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      .stat-item {
        .stat-value {
          font-size: 12px;
        }
      }
    }
  }
}

// 暗色主题支持
.admin-layout.dark-theme .business-analytics-chart {
  .chart-controls {
    background-color: #2a2a2a;

    .control-label {
      color: #ccc;
    }

    .type-btn {
      border-color: #434343;
      color: #ccc;
      background-color: #1f1f1f;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.active {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;
      }
    }

    .dimension-item,
    .range-item {
      border-color: #434343;
      color: #ccc;
      background-color: #1f1f1f;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.active {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;
      }
    }
  }

  .data-statistics {
    background-color: #2a2a2a;

    .stat-item {
      .stat-label {
        color: #999;
      }

      .stat-value {
        color: #fff;
      }
    }
  }
}
</style>