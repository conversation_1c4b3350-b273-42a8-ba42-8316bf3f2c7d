/**
 * 性能优化工具
 * 提供防抖、节流、懒加载、缓存等性能优化功能
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: number | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 缓存装饰器
 */
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  getKey?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, ReturnType<T>>()
  
  return ((...args: Parameters<T>) => {
    const key = getKey ? getKey(...args) : JSON.stringify(args)
    
    if (cache.has(key)) {
      return cache.get(key)!
    }
    
    const result = func(...args)
    cache.set(key, result)
    
    return result
  }) as T
}

/**
 * LRU缓存
 */
export class LRUCache<K, V> {
  private capacity: number
  private cache: Map<K, V>
  
  constructor(capacity: number) {
    this.capacity = capacity
    this.cache = new Map()
  }
  
  get(key: K): V | undefined {
    if (this.cache.has(key)) {
      // 移动到最后（最近使用）
      const value = this.cache.get(key)!
      this.cache.delete(key)
      this.cache.set(key, value)
      return value
    }
    return undefined
  }
  
  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      // 更新现有键
      this.cache.delete(key)
    } else if (this.cache.size >= this.capacity) {
      // 删除最久未使用的项
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(key, value)
  }
  
  has(key: K): boolean {
    return this.cache.has(key)
  }
  
  delete(key: K): boolean {
    return this.cache.delete(key)
  }
  
  clear(): void {
    this.cache.clear()
  }
  
  get size(): number {
    return this.cache.size
  }
}

/**
 * 懒加载组合函数
 */
export function useLazyLoad(threshold = 100) {
  const isVisible = ref(false)
  const target = ref<HTMLElement | null>(null)
  
  let observer: IntersectionObserver | null = null
  
  const startObserving = () => {
    if (!target.value || observer) return
    
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            isVisible.value = true
            stopObserving()
          }
        })
      },
      {
        rootMargin: `${threshold}px`
      }
    )
    
    observer.observe(target.value)
  }
  
  const stopObserving = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }
  
  onMounted(() => {
    startObserving()
  })
  
  onUnmounted(() => {
    stopObserving()
  })
  
  return {
    isVisible,
    target,
    startObserving,
    stopObserving
  }
}

/**
 * 虚拟滚动组合函数
 */
export function useVirtualScroll<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  buffer = 5
) {
  const scrollTop = ref(0)
  
  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight) + buffer,
      items.length
    )
    
    return {
      start: Math.max(0, start - buffer),
      end
    }
  })
  
  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value
    return items.slice(start, end).map((item, index) => ({
      item,
      index: start + index
    }))
  })
  
  const totalHeight = computed(() => items.length * itemHeight)
  
  const offsetY = computed(() => visibleRange.value.start * itemHeight)
  
  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }
  
  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll
  }
}

/**
 * 性能监控
 */
export class PerformanceMonitor {
  private marks: Map<string, number> = new Map()
  private measures: Map<string, number> = new Map()
  
  /**
   * 标记时间点
   */
  mark(name: string): void {
    this.marks.set(name, performance.now())
  }
  
  /**
   * 测量时间差
   */
  measure(name: string, startMark: string, endMark?: string): number {
    const startTime = this.marks.get(startMark)
    if (!startTime) {
      console.warn(`Start mark "${startMark}" not found`)
      return 0
    }
    
    const endTime = endMark ? this.marks.get(endMark) : performance.now()
    if (endMark && !endTime) {
      console.warn(`End mark "${endMark}" not found`)
      return 0
    }
    
    const duration = (endTime || performance.now()) - startTime
    this.measures.set(name, duration)
    
    return duration
  }
  
  /**
   * 获取测量结果
   */
  getMeasure(name: string): number | undefined {
    return this.measures.get(name)
  }
  
  /**
   * 获取所有测量结果
   */
  getAllMeasures(): Record<string, number> {
    return Object.fromEntries(this.measures)
  }
  
  /**
   * 清除所有标记和测量
   */
  clear(): void {
    this.marks.clear()
    this.measures.clear()
  }
  
  /**
   * 记录函数执行时间
   */
  time<T extends (...args: any[]) => any>(name: string, func: T): T {
    return ((...args: Parameters<T>) => {
      this.mark(`${name}-start`)
      const result = func(...args)
      
      if (result instanceof Promise) {
        return result.finally(() => {
          this.mark(`${name}-end`)
          this.measure(name, `${name}-start`, `${name}-end`)
        }) as ReturnType<T>
      } else {
        this.mark(`${name}-end`)
        this.measure(name, `${name}-start`, `${name}-end`)
        return result
      }
    }) as T
  }
}

/**
 * 批处理工具
 */
export class BatchProcessor<T> {
  private queue: T[] = []
  private timer: number | null = null
  private processor: (items: T[]) => void
  private batchSize: number
  private delay: number
  
  constructor(
    processor: (items: T[]) => void,
    batchSize = 10,
    delay = 100
  ) {
    this.processor = processor
    this.batchSize = batchSize
    this.delay = delay
  }
  
  /**
   * 添加项目到队列
   */
  add(item: T): void {
    this.queue.push(item)
    
    if (this.queue.length >= this.batchSize) {
      this.flush()
    } else {
      this.scheduleFlush()
    }
  }
  
  /**
   * 立即处理队列中的所有项目
   */
  flush(): void {
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }
    
    if (this.queue.length > 0) {
      const items = this.queue.splice(0)
      this.processor(items)
    }
  }
  
  /**
   * 调度刷新
   */
  private scheduleFlush(): void {
    if (this.timer) return
    
    this.timer = setTimeout(() => {
      this.flush()
    }, this.delay)
  }
  
  /**
   * 获取队列大小
   */
  get queueSize(): number {
    return this.queue.length
  }
}

/**
 * 资源预加载
 */
export function preloadResource(url: string, type: 'image' | 'script' | 'style' = 'image'): Promise<void> {
  return new Promise((resolve, reject) => {
    let element: HTMLElement
    
    switch (type) {
      case 'image':
        element = new Image()
        break
      case 'script':
        element = document.createElement('script')
        ;(element as HTMLScriptElement).src = url
        break
      case 'style':
        element = document.createElement('link')
        ;(element as HTMLLinkElement).rel = 'stylesheet'
        ;(element as HTMLLinkElement).href = url
        break
      default:
        reject(new Error(`Unsupported resource type: ${type}`))
        return
    }
    
    element.onload = () => resolve()
    element.onerror = () => reject(new Error(`Failed to load resource: ${url}`))
    
    if (type === 'image') {
      ;(element as HTMLImageElement).src = url
    } else {
      document.head.appendChild(element)
    }
  })
}

/**
 * 数据缓存管理器
 */
export class CacheManager {
  private cache: LRUCache<string, any>
  private ttlMap: Map<string, number> = new Map()

  constructor(capacity = 100) {
    this.cache = new LRUCache(capacity)
  }

  /**
   * 设置缓存
   */
  set(key: string, value: any, ttl?: number): void {
    this.cache.set(key, value)

    if (ttl) {
      const expireTime = Date.now() + ttl
      this.ttlMap.set(key, expireTime)
    }
  }

  /**
   * 获取缓存
   */
  get(key: string): any {
    // 检查是否过期
    const expireTime = this.ttlMap.get(key)
    if (expireTime && Date.now() > expireTime) {
      this.delete(key)
      return undefined
    }

    return this.cache.get(key)
  }

  /**
   * 删除缓存
   */
  delete(key: string): boolean {
    this.ttlMap.delete(key)
    return this.cache.delete(key)
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
    this.ttlMap.clear()
  }

  /**
   * 检查缓存是否存在
   */
  has(key: string): boolean {
    // 检查是否过期
    const expireTime = this.ttlMap.get(key)
    if (expireTime && Date.now() > expireTime) {
      this.delete(key)
      return false
    }

    return this.cache.has(key)
  }

  /**
   * 获取缓存大小
   */
  get size(): number {
    return this.cache.size
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    this.ttlMap.forEach((expireTime, key) => {
      if (now > expireTime) {
        expiredKeys.push(key)
      }
    })

    expiredKeys.forEach(key => this.delete(key))
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()
export const cacheManager = new CacheManager()
