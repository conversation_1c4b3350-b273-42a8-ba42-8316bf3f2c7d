<template>
  <view 
    v-if="visible" 
    class="loading-indicator-overlay"
    :class="{ 'overlay-transparent': transparent }"
  >
    <view class="loading-indicator" :class="[`loading-${size}`, `loading-${type}`]">
      <!-- 加载动画 -->
      <view class="loading-animation">
        <view v-if="type === 'spinner'" class="spinner">
          <view class="spinner-ring"></view>
        </view>
        
        <view v-else-if="type === 'dots'" class="dots">
          <view class="dot" v-for="i in 3" :key="i"></view>
        </view>
        
        <view v-else-if="type === 'pulse'" class="pulse">
          <view class="pulse-circle"></view>
        </view>
        
        <view v-else-if="type === 'bars'" class="bars">
          <view class="bar" v-for="i in 4" :key="i"></view>
        </view>
        
        <view v-else class="default-spinner">
          <view class="default-ring"></view>
        </view>
      </view>
      
      <!-- 加载文本 -->
      <view v-if="message" class="loading-message">{{ message }}</view>
      
      <!-- 进度条 -->
      <view v-if="showProgress && progress !== undefined" class="loading-progress">
        <view class="progress-bar">
          <view 
            class="progress-fill"
            :style="{ width: `${progress}%` }"
          ></view>
        </view>
        <view class="progress-text">{{ Math.round(progress) }}%</view>
      </view>
      
      <!-- 取消按钮 -->
      <button 
        v-if="cancelable"
        class="loading-cancel"
        @click="handleCancel"
      >
        取消
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

export interface LoadingIndicatorProps {
  visible: boolean
  message?: string
  type?: 'spinner' | 'dots' | 'pulse' | 'bars'
  size?: 'small' | 'medium' | 'large'
  progress?: number
  showProgress?: boolean
  cancelable?: boolean
  transparent?: boolean
}

const props = withDefaults(defineProps<LoadingIndicatorProps>(), {
  visible: false,
  message: '加载中...',
  type: 'spinner',
  size: 'medium',
  showProgress: false,
  cancelable: false,
  transparent: false
})

const emit = defineEmits<{
  'cancel': []
}>()

const handleCancel = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.loading-indicator-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 19999;
  backdrop-filter: blur(2px);
  
  &.overlay-transparent {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: none;
  }
}

.loading-indicator {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 120px;
  
  &.loading-small {
    padding: 16px;
    gap: 12px;
    min-width: 100px;
  }
  
  &.loading-large {
    padding: 32px;
    gap: 20px;
    min-width: 160px;
  }
}

.loading-animation {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Spinner动画
.spinner {
  .spinner-ring {
    width: 32px;
    height: 32px;
    border: 3px solid #f0f0f0;
    border-top: 3px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.loading-small .spinner .spinner-ring {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.loading-large .spinner .spinner-ring {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

// Dots动画
.dots {
  display: flex;
  gap: 4px;
  
  .dot {
    width: 8px;
    height: 8px;
    background: #1890ff;
    border-radius: 50%;
    animation: dotPulse 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

.loading-small .dots .dot {
  width: 6px;
  height: 6px;
}

.loading-large .dots .dot {
  width: 10px;
  height: 10px;
}

// Pulse动画
.pulse {
  .pulse-circle {
    width: 32px;
    height: 32px;
    background: #1890ff;
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
  }
}

.loading-small .pulse .pulse-circle {
  width: 24px;
  height: 24px;
}

.loading-large .pulse .pulse-circle {
  width: 40px;
  height: 40px;
}

// Bars动画
.bars {
  display: flex;
  gap: 3px;
  align-items: end;
  
  .bar {
    width: 4px;
    height: 20px;
    background: #1890ff;
    border-radius: 2px;
    animation: barScale 1.2s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: -0.45s; }
    &:nth-child(2) { animation-delay: -0.3s; }
    &:nth-child(3) { animation-delay: -0.15s; }
    &:nth-child(4) { animation-delay: 0s; }
  }
}

.loading-small .bars .bar {
  width: 3px;
  height: 16px;
}

.loading-large .bars .bar {
  width: 5px;
  height: 24px;
}

// 默认动画
.default-spinner {
  .default-ring {
    width: 32px;
    height: 32px;
    border: 3px solid transparent;
    border-top: 3px solid #1890ff;
    border-right: 3px solid #1890ff;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
  }
}

.loading-message {
  font-size: 14px;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

.loading-small .loading-message {
  font-size: 12px;
}

.loading-large .loading-message {
  font-size: 16px;
}

.loading-progress {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #999;
}

.loading-cancel {
  padding: 6px 16px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #e6e6e6;
    border-color: #bfbfbf;
  }
  
  &:active {
    transform: translateY(1px);
  }
}

// 动画定义
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes barScale {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .loading-indicator {
    margin: 20px;
    max-width: calc(100% - 40px);
  }
  
  .loading-message {
    font-size: 13px;
  }
}
</style>
