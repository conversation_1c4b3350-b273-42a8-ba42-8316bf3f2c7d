<template>
  <view class="chart-showcase">
    <view class="showcase-header">
      <text class="showcase-title">图表数据可视化展示</text>
      <view class="showcase-controls">
        <view class="control-btn" @click="refreshAllCharts">
          <text class="iconfont icon-refresh" :class="{ spinning: globalLoading }"></text>
          <text>刷新全部</text>
        </view>
        <view class="control-btn" @click="toggleAutoRefresh">
          <text class="iconfont" :class="globalAutoRefresh ? 'icon-pause' : 'icon-play'"></text>
          <text>{{ globalAutoRefresh ? '暂停' : '自动刷新' }}</text>
        </view>
      </view>
    </view>

    <!-- 图表网格布局 -->
    <view class="charts-grid">
      <!-- 用户活动图表 -->
      <view class="chart-card">
        <view class="card-header">
          <text class="card-title">用户活动趋势</text>
          <view class="card-actions">
            <view class="action-btn" @click="refreshChart('user-activity')">
              <text class="iconfont icon-refresh"></text>
            </view>
            <view class="action-btn" @click="toggleChartType('userActivity')">
              <text class="iconfont icon-bar-chart"></text>
            </view>
          </view>
        </view>
        <UserActivityChart 
          ref="userActivityChart"
          @click="handleChartClick"
          @hover="handleChartHover"
        />
      </view>

      <!-- 系统性能图表 -->
      <view class="chart-card">
        <view class="card-header">
          <text class="card-title">系统性能监控</text>
          <view class="card-actions">
            <view class="action-btn" @click="refreshChart('system-performance')">
              <text class="iconfont icon-refresh"></text>
            </view>
            <view class="action-btn" @click="toggleChartType('systemPerformance')">
              <text class="iconfont icon-gauge"></text>
            </view>
          </view>
        </view>
        <SystemPerformanceChart 
          ref="systemPerformanceChart"
          @click="handleChartClick"
          @hover="handleChartHover"
        />
      </view>

      <!-- 实时监控图表 -->
      <view class="chart-card full-width">
        <view class="card-header">
          <text class="card-title">实时系统监控</text>
          <view class="card-actions">
            <view class="action-btn" @click="toggleRealTimeMonitor">
              <text class="iconfont" :class="realTimeActive ? 'icon-pause' : 'icon-play'"></text>
            </view>
            <view class="action-btn" @click="clearRealTimeAlerts">
              <text class="iconfont icon-delete"></text>
            </view>
          </view>
        </view>
        <RealTimeMonitorChart 
          ref="realTimeMonitorChart"
          @click="handleChartClick"
          @hover="handleChartHover"
        />
      </view>

      <!-- 业务分析图表 -->
      <view class="chart-card">
        <view class="card-header">
          <text class="card-title">业务数据分析</text>
          <view class="card-actions">
            <view class="action-btn" @click="refreshChart('business-analytics')">
              <text class="iconfont icon-refresh"></text>
            </view>
            <view class="action-btn" @click="exportBusinessData">
              <text class="iconfont icon-download"></text>
            </view>
          </view>
        </view>
        <BusinessAnalyticsChart 
          ref="businessAnalyticsChart"
          @click="handleChartClick"
          @hover="handleChartHover"
        />
      </view>

      <!-- 高级图表展示 -->
      <view class="chart-card">
        <view class="card-header">
          <text class="card-title">高级数据可视化</text>
          <view class="card-actions">
            <view class="action-btn" @click="generateAdvancedData">
              <text class="iconfont icon-magic"></text>
            </view>
            <view class="action-btn" @click="exportAdvancedData">
              <text class="iconfont icon-download"></text>
            </view>
          </view>
        </view>
        <AdvancedChart 
          ref="advancedChart"
          :data-source="advancedChartData"
          title="综合数据分析"
          :width="400"
          :height="300"
          :default-auto-refresh="false"
          @data-refresh="handleAdvancedDataRefresh"
          @data-export="handleAdvancedDataExport"
          @data-point-click="handleChartClick"
        />
      </view>
    </view>

    <!-- 图表交互信息面板 -->
    <view class="interaction-panel">
      <view class="panel-header">
        <text class="panel-title">图表交互信息</text>
        <view class="panel-actions">
          <view class="action-btn" @click="clearInteractionLog">
            <text class="iconfont icon-delete"></text>
            <text>清空</text>
          </view>
        </view>
      </view>
      
      <view class="interaction-log">
        <view 
          v-for="(log, index) in interactionLogs" 
          :key="index"
          class="log-item"
          :class="log.type"
        >
          <view class="log-time">{{ formatTime(log.timestamp) }}</view>
          <view class="log-content">
            <text class="log-action">{{ log.action }}</text>
            <text class="log-details">{{ log.details }}</text>
          </view>
          <view class="log-chart">{{ log.chart }}</view>
        </view>
        
        <view v-if="interactionLogs.length === 0" class="empty-log">
          <text class="iconfont icon-info-circle"></text>
          <text>暂无交互记录</text>
        </view>
      </view>
    </view>

    <!-- 数据统计面板 -->
    <view class="statistics-panel">
      <view class="panel-header">
        <text class="panel-title">数据统计概览</text>
        <view class="panel-actions">
          <view class="action-btn" @click="refreshStatistics">
            <text class="iconfont icon-refresh"></text>
            <text>刷新</text>
          </view>
        </view>
      </view>
      
      <view class="statistics-grid">
        <view class="stat-card">
          <view class="stat-icon">
            <text class="iconfont icon-user"></text>
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ statistics.totalUsers.toLocaleString() }}</text>
            <text class="stat-label">总用户数</text>
          </view>
          <view class="stat-trend" :class="statistics.userGrowth >= 0 ? 'positive' : 'negative'">
            <text class="iconfont" :class="statistics.userGrowth >= 0 ? 'icon-trending-up' : 'icon-trending-down'"></text>
            <text>{{ Math.abs(statistics.userGrowth).toFixed(1) }}%</text>
          </view>
        </view>

        <view class="stat-card">
          <view class="stat-icon">
            <text class="iconfont icon-activity"></text>
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ statistics.activeUsers.toLocaleString() }}</text>
            <text class="stat-label">活跃用户</text>
          </view>
          <view class="stat-trend" :class="statistics.activityGrowth >= 0 ? 'positive' : 'negative'">
            <text class="iconfont" :class="statistics.activityGrowth >= 0 ? 'icon-trending-up' : 'icon-trending-down'"></text>
            <text>{{ Math.abs(statistics.activityGrowth).toFixed(1) }}%</text>
          </view>
        </view>

        <view class="stat-card">
          <view class="stat-icon">
            <text class="iconfont icon-server"></text>
          </view>
          <view class="stat-content">
            <text class="stat-value">{{ statistics.systemLoad.toFixed(1) }}%</text>
            <text class="stat-label">系统负载</text>
          </view>
          <view class="stat-trend" :class="statistics.systemLoad < 80 ? 'positive' : 'negative'">
            <text class="iconfont" :class="statistics.systemLoad < 80 ? 'icon-check-circle' : 'icon-warning'"></text>
            <text>{{ statistics.systemLoad < 80 ? '正常' : '警告' }}</text>
          </view>
        </view>

        <view class="stat-card">
          <view class="stat-icon">
            <text class="iconfont icon-dollar"></text>
          </view>
          <view class="stat-content">
            <text class="stat-value">¥{{ (statistics.revenue / 10000).toFixed(1) }}w</text>
            <text class="stat-label">营收</text>
          </view>
          <view class="stat-trend" :class="statistics.revenueGrowth >= 0 ? 'positive' : 'negative'">
            <text class="iconfont" :class="statistics.revenueGrowth >= 0 ? 'icon-trending-up' : 'icon-trending-down'"></text>
            <text>{{ Math.abs(statistics.revenueGrowth).toFixed(1) }}%</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import UserActivityChart from './UserActivityChart.vue'
import SystemPerformanceChart from './SystemPerformanceChart.vue'
import RealTimeMonitorChart from './RealTimeMonitorChart.vue'
import BusinessAnalyticsChart from './BusinessAnalyticsChart.vue'
import AdvancedChart from './AdvancedChart.vue'
import { formatValue, generateTimeLabels, type ChartDataPoint } from '../utils/chartUtils'

interface InteractionLog {
  timestamp: number
  action: string
  details: string
  chart: string
  type: 'click' | 'hover' | 'refresh' | 'export'
}

interface Statistics {
  totalUsers: number
  activeUsers: number
  systemLoad: number
  revenue: number
  userGrowth: number
  activityGrowth: number
  revenueGrowth: number
}

// 响应式数据
const globalLoading = ref(false)
const globalAutoRefresh = ref(false)
const realTimeActive = ref(true)
const interactionLogs = ref<InteractionLog[]>([])
const advancedChartData = ref<ChartDataPoint[]>([])

// 图表引用
const userActivityChart = ref()
const systemPerformanceChart = ref()
const realTimeMonitorChart = ref()
const businessAnalyticsChart = ref()
const advancedChart = ref()

// 统计数据
const statistics = reactive<Statistics>({
  totalUsers: 125680,
  activeUsers: 89432,
  systemLoad: 67.5,
  revenue: 2456780,
  userGrowth: 12.3,
  activityGrowth: 8.7,
  revenueGrowth: 15.2
})

// 定时器
let globalRefreshTimer: NodeJS.Timeout | null = null

// 方法
const refreshAllCharts = async () => {
  globalLoading.value = true
  
  try {
    // 并行刷新所有图表
    await Promise.all([
      userActivityChart.value?.refresh(),
      systemPerformanceChart.value?.refresh(),
      businessAnalyticsChart.value?.refresh(),
      advancedChart.value?.refresh()
    ])
    
    addInteractionLog('refresh', '刷新所有图表', '全局')
    
    uni.showToast({
      title: '图表已刷新',
      icon: 'success'
    })
  } catch (error) {
    console.error('刷新图表失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  } finally {
    globalLoading.value = false
  }
}

const refreshChart = async (chartType: string) => {
  try {
    switch (chartType) {
      case 'user-activity':
        await userActivityChart.value?.refresh()
        addInteractionLog('refresh', '刷新用户活动图表', '用户活动')
        break
      case 'system-performance':
        await systemPerformanceChart.value?.refresh()
        addInteractionLog('refresh', '刷新系统性能图表', '系统性能')
        break
      case 'business-analytics':
        await businessAnalyticsChart.value?.refresh()
        addInteractionLog('refresh', '刷新业务分析图表', '业务分析')
        break
    }
    
    uni.showToast({
      title: '图表已刷新',
      icon: 'success'
    })
  } catch (error) {
    console.error('刷新图表失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  }
}

const toggleAutoRefresh = () => {
  globalAutoRefresh.value = !globalAutoRefresh.value
  
  if (globalAutoRefresh.value) {
    startGlobalAutoRefresh()
    addInteractionLog('refresh', '开启全局自动刷新', '全局')
  } else {
    stopGlobalAutoRefresh()
    addInteractionLog('refresh', '关闭全局自动刷新', '全局')
  }
}

const startGlobalAutoRefresh = () => {
  if (globalRefreshTimer) {
    clearInterval(globalRefreshTimer)
  }
  
  globalRefreshTimer = setInterval(() => {
    refreshAllCharts()
  }, 30000) // 30秒刷新一次
}

const stopGlobalAutoRefresh = () => {
  if (globalRefreshTimer) {
    clearInterval(globalRefreshTimer)
    globalRefreshTimer = null
  }
}

const toggleChartType = (chartName: string) => {
  // 这里可以实现图表类型切换逻辑
  addInteractionLog('click', `切换${chartName}图表类型`, chartName)
  
  uni.showToast({
    title: '图表类型已切换',
    icon: 'none'
  })
}

const toggleRealTimeMonitor = () => {
  realTimeActive.value = !realTimeActive.value
  realTimeMonitorChart.value?.toggleRealTime()
  
  addInteractionLog('click', `${realTimeActive.value ? '开启' : '暂停'}实时监控`, '实时监控')
}

const clearRealTimeAlerts = () => {
  realTimeMonitorChart.value?.clearAlerts()
  addInteractionLog('click', '清空实时监控告警', '实时监控')
  
  uni.showToast({
    title: '告警已清空',
    icon: 'success'
  })
}

const exportBusinessData = () => {
  addInteractionLog('export', '导出业务分析数据', '业务分析')
  
  uni.showModal({
    title: '数据导出',
    content: '业务分析数据已导出到本地',
    showCancel: false,
    confirmText: '确定'
  })
}

const generateAdvancedData = () => {
  // 生成模拟的高级图表数据
  const labels = generateTimeLabels(30, 'day')
  const newData: ChartDataPoint[] = labels.map((label, index) => ({
    label,
    value: Math.floor(Math.random() * 1000) + 500 + index * 10,
    color: `hsl(${(index * 12) % 360}, 70%, 50%)`
  }))
  
  advancedChartData.value = newData
  addInteractionLog('click', '生成高级图表数据', '高级图表')
  
  uni.showToast({
    title: '数据已生成',
    icon: 'success'
  })
}

const exportAdvancedData = () => {
  addInteractionLog('export', '导出高级图表数据', '高级图表')
  
  uni.showModal({
    title: '数据导出',
    content: `已导出 ${advancedChartData.value.length} 条高级图表数据`,
    showCancel: false,
    confirmText: '确定'
  })
}

const handleAdvancedDataRefresh = () => {
  generateAdvancedData()
  addInteractionLog('refresh', '刷新高级图表数据', '高级图表')
}

const handleAdvancedDataExport = (data: ChartDataPoint[], format: string) => {
  addInteractionLog('export', `导出高级图表数据 (${format})`, '高级图表')
  console.log('导出数据:', data, format)
}

const handleChartClick = (item: any, index: number) => {
  const details = `点击数据点: ${item.label} = ${formatValue(item.value)}`
  addInteractionLog('click', details, '图表')
}

const handleChartHover = (item: any, index: number) => {
  const details = `悬停数据点: ${item.label} = ${formatValue(item.value)}`
  addInteractionLog('hover', details, '图表')
}

const addInteractionLog = (action: string, details: string, chart: string) => {
  const log: InteractionLog = {
    timestamp: Date.now(),
    action,
    details,
    chart,
    type: action as any
  }
  
  interactionLogs.value.unshift(log)
  
  // 保持最多50条记录
  if (interactionLogs.value.length > 50) {
    interactionLogs.value = interactionLogs.value.slice(0, 50)
  }
}

const clearInteractionLog = () => {
  interactionLogs.value = []
  
  uni.showToast({
    title: '交互记录已清空',
    icon: 'success'
  })
}

const refreshStatistics = () => {
  // 模拟统计数据更新
  statistics.totalUsers += Math.floor(Math.random() * 100) - 50
  statistics.activeUsers += Math.floor(Math.random() * 50) - 25
  statistics.systemLoad = Math.random() * 100
  statistics.revenue += Math.floor(Math.random() * 10000) - 5000
  statistics.userGrowth = (Math.random() - 0.5) * 20
  statistics.activityGrowth = (Math.random() - 0.5) * 15
  statistics.revenueGrowth = (Math.random() - 0.5) * 25
  
  addInteractionLog('refresh', '刷新统计数据', '统计面板')
  
  uni.showToast({
    title: '统计数据已更新',
    icon: 'success'
  })
}

const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
}

// 生命周期
onMounted(() => {
  // 初始化高级图表数据
  generateAdvancedData()
  
  // 添加欢迎日志
  addInteractionLog('refresh', '图表展示页面已加载', '全局')
})

onUnmounted(() => {
  stopGlobalAutoRefresh()
})
</script>

<style lang="scss" scoped>
.chart-showcase {
  padding: 16px;

  .showcase-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;

    .showcase-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .showcase-controls {
      display: flex;
      gap: 8px;

      .control-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 12px;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 12px;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;

        .iconfont {
          font-size: 14px;
        }

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }
  }

  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 16px;
    margin-bottom: 20px;

    .chart-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      &.full-width {
        grid-column: 1 / -1;
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;

        .card-title {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .card-actions {
          display: flex;
          gap: 4px;

          .action-btn {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;

            .iconfont {
              font-size: 12px;
              color: #666;
            }

            &:hover {
              border-color: #1890ff;
              background-color: #f0f8ff;

              .iconfont {
                color: #1890ff;
              }
            }
          }
        }
      }
    }
  }

  .interaction-panel,
  .statistics-panel {
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      .panel-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .panel-actions {
        display: flex;
        gap: 8px;

        .action-btn {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border: 1px solid #e8e8e8;
          border-radius: 3px;
          font-size: 11px;
          color: #666;
          cursor: pointer;
          transition: all 0.3s ease;

          .iconfont {
            font-size: 12px;
          }

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }
        }
      }
    }
  }

  .interaction-log {
    max-height: 200px;
    overflow-y: auto;

    .log-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 8px;
      margin-bottom: 4px;
      background-color: #f8f9fa;
      border-radius: 4px;
      font-size: 11px;
      border-left: 3px solid #e8e8e8;

      &.click {
        border-left-color: #1890ff;
      }

      &.hover {
        border-left-color: #52c41a;
      }

      &.refresh {
        border-left-color: #faad14;
      }

      &.export {
        border-left-color: #722ed1;
      }

      .log-time {
        color: #999;
        min-width: 60px;
      }

      .log-content {
        flex: 1;

        .log-action {
          font-weight: 500;
          color: #333;
          margin-right: 4px;
        }

        .log-details {
          color: #666;
        }
      }

      .log-chart {
        color: #999;
        font-size: 10px;
        min-width: 60px;
        text-align: right;
      }
    }

    .empty-log {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 20px;
      color: #999;
      font-size: 12px;

      .iconfont {
        font-size: 24px;
      }
    }
  }

  .statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;

    .stat-card {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background-color: #f8f9fa;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f0f8ff;
      }

      .stat-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #1890ff;
        border-radius: 50%;

        .iconfont {
          font-size: 18px;
          color: #fff;
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          display: block;
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 2px;
        }

        .stat-label {
          font-size: 11px;
          color: #999;
        }
      }

      .stat-trend {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;
        font-size: 10px;

        &.positive {
          color: #52c41a;
        }

        &.negative {
          color: #ff4d4f;
        }

        .iconfont {
          font-size: 12px;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .chart-showcase {
    padding: 8px;

    .showcase-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .showcase-controls {
        justify-content: center;
      }
    }

    .charts-grid {
      grid-template-columns: 1fr;
      gap: 12px;

      .chart-card {
        padding: 12px;

        &.full-width {
          grid-column: 1;
        }
      }
    }

    .statistics-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      .stat-card {
        padding: 8px;

        .stat-icon {
          width: 32px;
          height: 32px;

          .iconfont {
            font-size: 14px;
          }
        }

        .stat-content {
          .stat-value {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// 暗色主题支持
.admin-layout.dark-theme .chart-showcase {
  .showcase-header {
    background-color: #2a2a2a;

    .showcase-title {
      color: #fff;
    }

    .control-btn {
      background-color: #1f1f1f;
      border-color: #434343;
      color: #ccc;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }

  .chart-card,
  .interaction-panel,
  .statistics-panel {
    background-color: #1f1f1f;

    .card-title,
    .panel-title {
      color: #fff;
    }

    .card-header,
    .panel-header {
      border-bottom-color: #434343;
    }

    .action-btn {
      border-color: #434343;
      background-color: #2a2a2a;
      color: #ccc;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
        background-color: #1f2937;
      }
    }
  }

  .interaction-log {
    .log-item {
      background-color: #2a2a2a;

      .log-action {
        color: #fff;
      }

      .log-details {
        color: #ccc;
      }
    }

    .empty-log {
      color: #999;
    }
  }

  .statistics-grid {
    .stat-card {
      background-color: #2a2a2a;

      &:hover {
        background-color: #1f2937;
      }

      .stat-content {
        .stat-value {
          color: #fff;
        }

        .stat-label {
          color: #999;
        }
      }
    }
  }
}

@keyframes spinning {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinning {
  animation: spinning 1s linear infinite;
}
</style>