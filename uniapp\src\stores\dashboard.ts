import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { DashboardMetrics, Activity, DashboardData } from '@/pages/admin/types/admin'
import { http } from '@/utils/http'

export const useDashboardStore = defineStore('dashboard', () => {
  // ==================== 状态 ====================
  
  /** 仪表板数据 */
  const dashboardData = ref<DashboardData | null>(null)
  
  /** 指标数据 */
  const metrics = ref<DashboardMetrics | null>(null)
  
  /** 最近活动 */
  const recentActivities = ref<Activity[]>([])
  
  /** 加载状态 */
  const loading = ref(false)
  
  /** 最后更新时间 */
  const lastUpdateTime = ref<number>(0)

  /** 当前模块 */
  const currentModule = ref<string>('dashboard')

  /** 面包屑导航 */
  const breadcrumb = ref<Array<{ title: string; path: string }>>([
    { title: '首页', path: 'dashboard' }
  ])

  // ==================== 计算属性 ====================
  
  /** 是否有数据 */
  const hasData = computed(() => dashboardData.value !== null)
  
  /** 总用户数 */
  const totalUsers = computed(() => metrics.value?.totalUsers || 0)
  
  /** 活跃用户数 */
  const activeUsers = computed(() => metrics.value?.activeUsers || 0)
  
  /** 系统负载 */
  const systemLoad = computed(() => metrics.value?.systemLoad || 0)
  
  /** 错误率 */
  const errorRate = computed(() => metrics.value?.errorRate || 0)

  // ==================== 方法 ====================
  
  /** 获取仪表板指标 */
  const fetchMetrics = async (): Promise<DashboardMetrics | null> => {
    try {
      const response = await http({
        url: '/admin/dashboard/metrics',
        method: 'GET'
      })
      
      if (response.code === 200 && response.data) {
        metrics.value = response.data
        return response.data
      }
      
      return null
    } catch (error) {
      console.error('获取仪表板指标失败:', error)
      return null
    }
  }
  
  /** 获取最近活动 */
  const fetchRecentActivities = async (): Promise<Activity[]> => {
    try {
      const response = await http({
        url: '/admin/dashboard/activities',
        method: 'GET'
      })
      
      if (response.code === 200 && response.data) {
        recentActivities.value = response.data
        return response.data
      }
      
      return []
    } catch (error) {
      console.error('获取最近活动失败:', error)
      return []
    }
  }
  
  /** 获取图表数据 */
  const fetchChartData = async (type: string) => {
    try {
      const response = await http({
        url: `/admin/dashboard/charts/${type}`,
        method: 'GET'
      })
      
      if (response.code === 200 && response.data) {
        return response.data
      }
      
      return null
    } catch (error) {
      console.error(`获取图表数据失败 (${type}):`, error)
      return null
    }
  }
  
  /** 获取完整仪表板数据 */
  const fetchDashboardData = async (): Promise<void> => {
    loading.value = true
    
    try {
      // 并行获取数据
      const [metricsData, activitiesData] = await Promise.all([
        fetchMetrics(),
        fetchRecentActivities()
      ])
      
      // 构建仪表板数据
      if (metricsData) {
        dashboardData.value = {
          userStats: metricsData,
          systemMetrics: metricsData,
          businessAnalytics: metricsData,
          recentActivities: activitiesData
        }
      }
      
      lastUpdateTime.value = Date.now()
      
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  /** 刷新数据 */
  const refresh = async (): Promise<void> => {
    await fetchDashboardData()
  }
  
  /** 清除缓存 */
  const clearCache = (): void => {
    dashboardData.value = null
    metrics.value = null
    recentActivities.value = []
    lastUpdateTime.value = 0
  }
  
  /** 设置指标数据 */
  const setMetrics = (data: DashboardMetrics): void => {
    metrics.value = data
  }
  
  /** 设置最近活动 */
  const setRecentActivities = (activities: Activity[]): void => {
    recentActivities.value = activities
  }
  
  /** 添加新活动 */
  const addActivity = (activity: Activity): void => {
    recentActivities.value.unshift(activity)
    // 保持最多20条记录
    if (recentActivities.value.length > 20) {
      recentActivities.value = recentActivities.value.slice(0, 20)
    }
  }
  
  /** 检查是否需要刷新 */
  const shouldRefresh = (maxAge: number = 300000): boolean => {
    // 默认5分钟过期
    return Date.now() - lastUpdateTime.value > maxAge
  }

  /** 设置当前模块 */
  const setCurrentModule = (module: string): void => {
    currentModule.value = module
  }

  /** 设置面包屑导航 */
  const setBreadcrumb = (crumbs: Array<{ title: string; path: string }>): void => {
    breadcrumb.value = crumbs
  }

  return {
    // 状态
    dashboardData,
    metrics,
    recentActivities,
    loading,
    lastUpdateTime,
    
    // 计算属性
    hasData,
    totalUsers,
    activeUsers,
    systemLoad,
    errorRate,
    
    // 方法
    fetchMetrics,
    fetchRecentActivities,
    fetchChartData,
    fetchDashboardData,
    refresh,
    clearCache,
    setMetrics,
    setRecentActivities,
    addActivity,
    shouldRefresh,
    setCurrentModule,
    setBreadcrumb,
    currentModule,
    breadcrumb
  }
}, {
  persist: {
    key: 'dashboard-store',
    storage: {
      getItem: (key: string) => uni.getStorageSync(key),
      setItem: (key: string, value: string) => uni.setStorageSync(key, value)
    },
    paths: ['dashboardData', 'metrics', 'recentActivities', 'lastUpdateTime', 'currentModule', 'breadcrumb']
  }
})