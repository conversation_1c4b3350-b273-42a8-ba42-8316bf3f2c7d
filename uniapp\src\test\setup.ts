/**
 * 测试环境设置
 */

import { vi } from 'vitest'

// 模拟uni-app API
const mockUni = {
  // 网络请求
  request: vi.fn(),
  
  // 存储
  setStorageSync: vi.fn(),
  getStorageSync: vi.fn(),
  removeStorageSync: vi.fn(),
  clearStorageSync: vi.fn(),
  
  // 提示
  showToast: vi.fn(),
  hideToast: vi.fn(),
  showLoading: vi.fn(),
  hideLoading: vi.fn(),
  showModal: vi.fn(),
  
  // 导航
  navigateTo: vi.fn(),
  redirectTo: vi.fn(),
  switchTab: vi.fn(),
  navigateBack: vi.fn(),
  
  // 系统信息
  getSystemInfoSync: vi.fn(() => ({
    windowWidth: 375,
    windowHeight: 667,
    pixelRatio: 2,
    platform: 'ios',
    system: 'iOS 14.0',
    safeAreaInsets: {
      top: 44,
      bottom: 34,
      left: 0,
      right: 0
    }
  })),
  
  // 窗口事件
  onWindowResize: vi.fn(),
  offWindowResize: vi.fn(),
  
  // 页面事件
  onShow: vi.fn(),
  onHide: vi.fn(),
  onLoad: vi.fn(),
  onUnload: vi.fn(),
  
  // 下拉刷新
  startPullDownRefresh: vi.fn(),
  stopPullDownRefresh: vi.fn(),
  
  // 上拉加载
  onReachBottom: vi.fn(),
  
  // 分享
  onShareAppMessage: vi.fn(),
  onShareTimeline: vi.fn(),
  
  // 文件
  chooseImage: vi.fn(),
  previewImage: vi.fn(),
  uploadFile: vi.fn(),
  downloadFile: vi.fn(),
  
  // 位置
  getLocation: vi.fn(),
  chooseLocation: vi.fn(),
  openLocation: vi.fn(),
  
  // 设备
  getNetworkType: vi.fn(),
  onNetworkStatusChange: vi.fn(),
  
  // 剪贴板
  setClipboardData: vi.fn(),
  getClipboardData: vi.fn(),
  
  // 扫码
  scanCode: vi.fn(),
  
  // 振动
  vibrateLong: vi.fn(),
  vibrateShort: vi.fn(),
  
  // 屏幕亮度
  setScreenBrightness: vi.fn(),
  getScreenBrightness: vi.fn(),
  setKeepScreenOn: vi.fn()
}

// 设置全局uni对象
globalThis.uni = mockUni

// 模拟console方法（避免测试时输出过多日志）
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
}

// 模拟window对象的一些属性
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: ''
  },
  writable: true
})

// 模拟IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn()
}))

// 模拟ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn()
}))

// 模拟matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
})

// 测试工具函数
export const createMockResponse = (data: any, success = true) => {
  return {
    statusCode: success ? 200 : 400,
    data: success ? { code: 0, data, message: 'success' } : { code: 1, message: 'error' },
    errMsg: success ? 'request:ok' : 'request:fail'
  }
}

export const mockUniRequest = (response: any) => {
  mockUni.request.mockImplementation((options: any) => {
    setTimeout(() => {
      if (response.success) {
        options.success?.(response)
      } else {
        options.fail?.(response)
      }
    }, 0)
  })
}

export const mockUniStorage = (data: Record<string, any> = {}) => {
  const storage = { ...data }
  
  mockUni.setStorageSync.mockImplementation((key: string, value: any) => {
    storage[key] = value
  })
  
  mockUni.getStorageSync.mockImplementation((key: string) => {
    return storage[key]
  })
  
  mockUni.removeStorageSync.mockImplementation((key: string) => {
    delete storage[key]
  })
  
  mockUni.clearStorageSync.mockImplementation(() => {
    Object.keys(storage).forEach(key => delete storage[key])
  })
  
  return storage
}

export const mockUniModal = (confirmResult = true) => {
  mockUni.showModal.mockImplementation((options: any) => {
    setTimeout(() => {
      options.success?.({
        confirm: confirmResult,
        cancel: !confirmResult,
        content: options.editable ? 'test input' : undefined
      })
    }, 0)
  })
}

export const mockUniToast = () => {
  mockUni.showToast.mockImplementation((options: any) => {
    setTimeout(() => {
      options.success?.()
    }, 0)
  })
}

// 重置所有mock
export const resetAllMocks = () => {
  vi.clearAllMocks()
  Object.values(mockUni).forEach(fn => {
    if (typeof fn === 'function') {
      fn.mockClear?.()
    }
  })
}

// 在每个测试前重置
beforeEach(() => {
  resetAllMocks()
})
