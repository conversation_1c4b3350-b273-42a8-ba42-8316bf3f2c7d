// 管理系统样式入口文件

// 导入基础布局样式
@import './admin-layout.scss';
@import './admin-sidebar.scss';
@import './admin-header.scss';
@import './admin-content.scss';

// 导入功能模块样式
@import './dashboard-panel.scss';
@import './system-function-panel.scss';
@import './system-monitor-panel.scss';
@import './system-tools-panel.scss';

// 导入通用组件样式
@import './data-table.scss';
@import './data-form.scss';

// 管理系统全局样式变量
:root {
  // 主色调
  --admin-primary-color: #1890ff;
  --admin-primary-hover: #40a9ff;
  --admin-primary-active: #096dd9;

  // 状态颜色
  --admin-success-color: #52c41a;
  --admin-warning-color: #faad14;
  --admin-error-color: #ff4d4f;
  --admin-info-color: #1890ff;

  // 文字颜色
  --admin-text-primary: #333;
  --admin-text-secondary: #666;
  --admin-text-disabled: #999;
  --admin-text-placeholder: #ccc;

  // 背景颜色
  --admin-bg-primary: #fff;
  --admin-bg-secondary: #f5f5f5;
  --admin-bg-hover: #f0f0f0;
  --admin-bg-active: #e6f7ff;

  // 边框颜色
  --admin-border-color: #e8e8e8;
  --admin-border-light: #f0f0f0;

  // 阴影
  --admin-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --admin-shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);

  // 圆角
  --admin-border-radius: 4px;
  --admin-border-radius-lg: 8px;

  // 间距
  --admin-spacing-xs: 4px;
  --admin-spacing-sm: 8px;
  --admin-spacing-md: 16px;
  --admin-spacing-lg: 24px;
  --admin-spacing-xl: 32px;

  // 字体大小
  --admin-font-size-xs: 11px;
  --admin-font-size-sm: 12px;
  --admin-font-size-base: 14px;
  --admin-font-size-lg: 16px;
  --admin-font-size-xl: 18px;
  --admin-font-size-xxl: 20px;

  // 动画时间
  --admin-transition-fast: 0.2s;
  --admin-transition-base: 0.3s;
  --admin-transition-slow: 0.5s;
}

// 管理系统全局样式重置
.admin-layout {
  * {
    box-sizing: border-box;
  }

  // 滚动条样式
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // 通用工具类
  .text-center { text-align: center; }
  .text-left { text-align: left; }
  .text-right { text-align: right; }

  .text-primary { color: var(--admin-primary-color); }
  .text-success { color: var(--admin-success-color); }
  .text-warning { color: var(--admin-warning-color); }
  .text-error { color: var(--admin-error-color); }
  .text-secondary { color: var(--admin-text-secondary); }
  .text-disabled { color: var(--admin-text-disabled); }

  .bg-primary { background-color: var(--admin-primary-color); }
  .bg-success { background-color: var(--admin-success-color); }
  .bg-warning { background-color: var(--admin-warning-color); }
  .bg-error { background-color: var(--admin-error-color); }
  .bg-secondary { background-color: var(--admin-bg-secondary); }

  .border { border: 1px solid var(--admin-border-color); }
  .border-light { border: 1px solid var(--admin-border-light); }
  .border-top { border-top: 1px solid var(--admin-border-color); }
  .border-bottom { border-bottom: 1px solid var(--admin-border-color); }
  .border-left { border-left: 1px solid var(--admin-border-color); }
  .border-right { border-right: 1px solid var(--admin-border-color); }

  .rounded { border-radius: var(--admin-border-radius); }
  .rounded-lg { border-radius: var(--admin-border-radius-lg); }

  .shadow { box-shadow: var(--admin-shadow-light); }
  .shadow-lg { box-shadow: var(--admin-shadow-medium); }

  .m-0 { margin: 0; }
  .m-1 { margin: var(--admin-spacing-xs); }
  .m-2 { margin: var(--admin-spacing-sm); }
  .m-3 { margin: var(--admin-spacing-md); }
  .m-4 { margin: var(--admin-spacing-lg); }
  .m-5 { margin: var(--admin-spacing-xl); }

  .p-0 { padding: 0; }
  .p-1 { padding: var(--admin-spacing-xs); }
  .p-2 { padding: var(--admin-spacing-sm); }
  .p-3 { padding: var(--admin-spacing-md); }
  .p-4 { padding: var(--admin-spacing-lg); }
  .p-5 { padding: var(--admin-spacing-xl); }

  .flex { display: flex; }
  .flex-column { flex-direction: column; }
  .flex-wrap { flex-wrap: wrap; }
  .justify-center { justify-content: center; }
  .justify-between { justify-content: space-between; }
  .justify-around { justify-content: space-around; }
  .align-center { align-items: center; }
  .align-start { align-items: flex-start; }
  .align-end { align-items: flex-end; }

  .w-full { width: 100%; }
  .h-full { height: 100%; }

  .hidden { display: none; }
  .visible { visibility: visible; }
  .invisible { visibility: hidden; }

  .cursor-pointer { cursor: pointer; }
  .cursor-not-allowed { cursor: not-allowed; }

  .select-none { user-select: none; }
  .select-text { user-select: text; }

  .overflow-hidden { overflow: hidden; }
  .overflow-auto { overflow: auto; }
  .overflow-x-auto { overflow-x: auto; }
  .overflow-y-auto { overflow-y: auto; }

  .transition { transition: all var(--admin-transition-base) ease; }
  .transition-fast { transition: all var(--admin-transition-fast) ease; }
  .transition-slow { transition: all var(--admin-transition-slow) ease; }
}