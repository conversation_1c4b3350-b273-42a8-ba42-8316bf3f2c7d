import { ref, computed } from 'vue'
import type { AdminUser, Permission } from '../types/admin'

/**
 * 权限管理composable
 */
export function usePermission() {
  // 当前用户信息
  const currentUser = ref<AdminUser | null>(null)
  
  // 用户权限列表
  const userPermissions = ref<string[]>([])
  
  // 权限树
  const permissionTree = ref<Permission[]>([])
  
  /**
   * 初始化用户权限
   */
  const initUserPermissions = async () => {
    try {
      // 从本地存储获取用户信息
      const userInfo = uni.getStorageSync('admin_user')
      if (userInfo) {
        currentUser.value = userInfo
        userPermissions.value = userInfo.permissions || []
      }
      
      // 如果没有用户信息，尝试从token验证获取
      const token = uni.getStorageSync('admin_token')
      if (token && !currentUser.value) {
        // 这里应该调用API验证token并获取用户信息
        // const userInfo = await validateToken(token)
        // currentUser.value = userInfo
        // userPermissions.value = userInfo.permissions || []
      }
    } catch (error) {
      console.error('Init user permissions error:', error)
    }
  }
  
  /**
   * 检查是否有指定权限
   */
  const hasPermission = (permission: string | string[]): boolean => {
    if (!currentUser.value) return false
    
    // 超级管理员拥有所有权限
    if (currentUser.value.role === 'super_admin') return true
    
    if (Array.isArray(permission)) {
      // 检查是否拥有数组中的任一权限
      return permission.some(p => userPermissions.value.includes(p))
    } else {
      // 检查是否拥有指定权限
      return userPermissions.value.includes(permission)
    }
  }
  
  /**
   * 检查是否有指定角色
   */
  const hasRole = (role: string | string[]): boolean => {
    if (!currentUser.value) return false
    
    if (Array.isArray(role)) {
      return role.includes(currentUser.value.role)
    } else {
      return currentUser.value.role === role
    }
  }
  
  /**
   * 检查是否有所有指定权限
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    if (!currentUser.value) return false
    
    // 超级管理员拥有所有权限
    if (currentUser.value.role === 'super_admin') return true
    
    return permissions.every(permission => userPermissions.value.includes(permission))
  }
  
  /**
   * 获取用户可访问的菜单
   */
  const getAccessibleMenus = (menus: any[]): any[] => {
    return menus.filter(menu => {
      // 检查菜单权限
      if (menu.permission && !hasPermission(menu.permission)) {
        return false
      }
      
      // 递归过滤子菜单
      if (menu.children && menu.children.length > 0) {
        menu.children = getAccessibleMenus(menu.children)
      }
      
      return true
    })
  }
  
  /**
   * 权限守卫 - 检查页面访问权限
   */
  const checkPagePermission = (requiredPermissions: string[]): boolean => {
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true
    }
    
    return hasPermission(requiredPermissions)
  }
  
  /**
   * 操作权限检查
   */
  const checkActionPermission = (action: string, resource: string): boolean => {
    const permission = `${resource}:${action}`
    return hasPermission(permission)
  }
  
  /**
   * 批量权限检查
   */
  const checkBatchPermissions = (permissions: Record<string, string[]>): Record<string, boolean> => {
    const result: Record<string, boolean> = {}
    
    Object.entries(permissions).forEach(([key, perms]) => {
      result[key] = hasPermission(perms)
    })
    
    return result
  }
  
  /**
   * 权限错误处理
   */
  const handlePermissionError = (message: string = '没有权限执行此操作') => {
    uni.showToast({
      title: message,
      icon: 'error',
      duration: 2000
    })
  }
  
  /**
   * 跳转到无权限页面
   */
  const redirectToNoPermission = () => {
    uni.navigateTo({
      url: '/pages/error/no-permission'
    })
  }
  
  /**
   * 权限装饰器 - 用于方法权限检查
   */
  const withPermission = (permissions: string[], fn: Function) => {
    return (...args: any[]) => {
      if (hasPermission(permissions)) {
        return fn(...args)
      } else {
        handlePermissionError()
        return Promise.reject(new Error('Permission denied'))
      }
    }
  }
  
  /**
   * 更新用户权限
   */
  const updateUserPermissions = (newPermissions: string[]) => {
    userPermissions.value = newPermissions
    
    // 更新本地存储
    if (currentUser.value) {
      currentUser.value.permissions = newPermissions
      uni.setStorageSync('admin_user', currentUser.value)
    }
  }
  
  /**
   * 清除用户权限
   */
  const clearUserPermissions = () => {
    currentUser.value = null
    userPermissions.value = []
    uni.removeStorageSync('admin_user')
    uni.removeStorageSync('admin_token')
  }
  
  // 计算属性
  const isLoggedIn = computed(() => !!currentUser.value)
  const isAdmin = computed(() => hasRole(['admin', 'super_admin']))
  const isSuperAdmin = computed(() => hasRole('super_admin'))
  
  return {
    // 状态
    currentUser,
    userPermissions,
    permissionTree,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    isSuperAdmin,
    
    // 方法
    initUserPermissions,
    hasPermission,
    hasRole,
    hasAllPermissions,
    getAccessibleMenus,
    checkPagePermission,
    checkActionPermission,
    checkBatchPermissions,
    handlePermissionError,
    redirectToNoPermission,
    withPermission,
    updateUserPermissions,
    clearUserPermissions
  }
}

/**
 * 权限指令 - 用于模板中的权限控制
 */
export const vPermission = {
  mounted(el: HTMLElement, binding: any) {
    const { hasPermission } = usePermission()
    const permissions = binding.value
    
    if (!hasPermission(permissions)) {
      el.style.display = 'none'
    }
  },
  
  updated(el: HTMLElement, binding: any) {
    const { hasPermission } = usePermission()
    const permissions = binding.value
    
    if (!hasPermission(permissions)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

/**
 * 权限路由守卫
 */
export const createPermissionGuard = () => {
  const { checkPagePermission, redirectToNoPermission } = usePermission()
  
  return (to: any, from: any, next: Function) => {
    const requiredPermissions = to.meta?.permissions || []
    
    if (checkPagePermission(requiredPermissions)) {
      next()
    } else {
      redirectToNoPermission()
    }
  }
}

/**
 * 权限常量
 */
export const PERMISSIONS = {
  // 用户管理
  USER_VIEW: 'user:view',
  USER_CREATE: 'user:create',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  USER_MANAGE: 'user:manage',
  
  // 角色管理
  ROLE_VIEW: 'role:view',
  ROLE_CREATE: 'role:create',
  ROLE_UPDATE: 'role:update',
  ROLE_DELETE: 'role:delete',
  ROLE_MANAGE: 'role:manage',
  
  // 权限管理
  PERMISSION_VIEW: 'permission:view',
  PERMISSION_CREATE: 'permission:create',
  PERMISSION_UPDATE: 'permission:update',
  PERMISSION_DELETE: 'permission:delete',
  PERMISSION_MANAGE: 'permission:manage',
  
  // 系统管理
  SYSTEM_MONITOR: 'system:monitor',
  SYSTEM_TOOLS: 'system:tools',
  SYSTEM_SETTINGS: 'system:settings',
  
  // 数据管理
  DATA_EXPORT: 'data:export',
  DATA_IMPORT: 'data:import',
  DATA_BACKUP: 'data:backup'
} as const
