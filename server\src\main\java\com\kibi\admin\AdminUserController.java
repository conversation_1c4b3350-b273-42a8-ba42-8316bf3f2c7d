package com.kibi.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kibi.entity.User;
import com.kibi.service.UserService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/user")
public class AdminUserController {

    @Autowired
    private UserService userService;

    @Autowired
    private JWTUtils jwtUtils;

    /**
     * 检查token是否有效 - 需要token验证
     */
    @GetMapping("/validateToken")
    public R validateToken(@RequestHeader("Authorization") String authHeader) {
        try {
            System.out.println("开始验证token...");

            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                System.out.println("Token无效或已过期");
                return R.error("Token无效或已过期");
            }

            // 从token中获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            System.out.println("Token验证成功，用户ID: " + userId);

            if (userId == null) {
                System.out.println("无法从token中获取用户ID");
                return R.error("Token格式错误");
            }

            // 验证用户是否存在且状态正常
            User user = userService.getById(userId);
            if (user == null) {
                System.out.println("用户不存在: " + userId);
                return R.error("用户不存在");
            }

            if (user.getStatus() != 1) {
                System.out.println("用户账户已被禁用: " + userId);
                return R.error("账户已被禁用");
            }

            if (userId == 88888) {
//                User user1 = new User();
//                user1.setId(userId);
//                user1.setNickName(user.getNickName());
//                user1.setHeadImgUrl(user.getHeadImgUrl());
                return R.success(true);
            }
            return R.error("无权限");
        } catch (Exception e) {
            System.err.println("Token验证异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("Token验证失败");
        }
    }

    /**
     * 获取用户列表 - 分页查询
     */
    @GetMapping("/list")
    public R<Map<String, Object>> getUserList(
            @RequestHeader("Authorization") String authHeader,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 构建查询条件
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.like("nick_name", keyword)
                           .or().like("username", keyword)
                           .or().like("phone", keyword);
            }
            queryWrapper.orderByDesc("create_time");

            // 分页查询
            Page<User> pageParam = new Page<>(page, pageSize);
            IPage<User> userPage = userService.page(pageParam, queryWrapper);

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("items", userPage.getRecords());
            result.put("total", userPage.getTotal());
            result.put("page", userPage.getCurrent());
            result.put("pageSize", userPage.getSize());
            result.put("totalPages", userPage.getPages());

            return R.success(result);
        } catch (Exception e) {
            System.err.println("获取用户列表异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("获取用户列表失败");
        }
    }

    /**
     * 创建用户
     */
    @PostMapping
    public R<User> createUser(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody User user) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 检查用户名是否已存在
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", user.getUsername());
            if (userService.count(queryWrapper) > 0) {
                return R.error("用户名已存在");
            }

            // 设置默认值
            if (user.getStatus() == null) {
                user.setStatus(1);
            }

            // 保存用户
            boolean success = userService.save(user);
            if (success) {
                return R.success(user);
            } else {
                return R.error("创建用户失败");
            }
        } catch (Exception e) {
            System.err.println("创建用户异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("创建用户失败");
        }
    }

    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    public R<User> updateUser(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id,
            @RequestBody User user) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 检查用户是否存在
            User existingUser = userService.getById(id);
            if (existingUser == null) {
                return R.error("用户不存在");
            }

            // 如果修改了用户名，检查是否重复
            if (!existingUser.getUsername().equals(user.getUsername())) {
                QueryWrapper<User> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("username", user.getUsername())
                           .ne("id", id);
                if (userService.count(queryWrapper) > 0) {
                    return R.error("用户名已存在");
                }
            }

            // 更新用户信息
            user.setId(id);
            boolean success = userService.updateById(user);
            if (success) {
                return R.success(userService.getById(id));
            } else {
                return R.error("更新用户失败");
            }
        } catch (Exception e) {
            System.err.println("更新用户异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("更新用户失败");
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public R<Boolean> deleteUser(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 检查用户是否存在
            User user = userService.getById(id);
            if (user == null) {
                return R.error("用户不存在");
            }

            // 不能删除管理员账户
            if (id == 88888L) {
                return R.error("不能删除管理员账户");
            }

            // 删除用户
            boolean success = userService.removeById(id);
            return success ? R.success(true) : R.error("删除用户失败");
        } catch (Exception e) {
            System.err.println("删除用户异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("删除用户失败");
        }
    }

    /**
     * 获取单个用户信息
     */
    @GetMapping("/{id}")
    public R<User> getUser(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            User user = userService.getById(id);
            if (user == null) {
                return R.error("用户不存在");
            }

            return R.success(user);
        } catch (Exception e) {
            System.err.println("获取用户信息异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("获取用户信息失败");
        }
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    public R<Boolean> batchDeleteUsers(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody List<Long> ids) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 过滤掉管理员账户
            ids.removeIf(id -> id == 88888L);

            if (ids.isEmpty()) {
                return R.error("没有可删除的用户");
            }

            // 批量删除
            boolean success = userService.removeByIds(ids);
            return success ? R.success(true) : R.error("批量删除失败");
        } catch (Exception e) {
            System.err.println("批量删除用户异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("批量删除失败");
        }
    }

    /**
     * 验证管理员Token
     */
    private boolean validateAdminToken(String authHeader) {
        try {
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return false;
            }

            Long userId = jwtUtils.getUserIdFromToken(token);
            if (userId == null) {
                return false;
            }

            // 验证用户是否存在且状态正常
            User user = userService.getById(userId);
            if (user == null || user.getStatus() != 1) {
                return false;
            }

            // 只有管理员账户才有权限
            return userId == 88888L;
        } catch (Exception e) {
            return false;
        }
    }
}