package com.kibi.admin;

import com.kibi.entity.User;
import com.kibi.service.PartnerService;
import com.kibi.service.RoleService;
import com.kibi.service.UserService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/admin/user")
public class AdminUserController {

    private final UserService userService;
    private final JWTUtils jwtUtils;

    public AdminUserController(UserService userService, JWTUtils jwtUtils) {
        this.userService = userService;
        this.jwtUtils = jwtUtils;
    }

    /**
     * 检查token是否有效 - 需要token验证
     */
    @GetMapping("/validateToken")
    public R validateToken(@RequestHeader("Authorization") String authHeader) {
        try {
            System.out.println("开始验证token...");

            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                System.out.println("Token无效或已过期");
                return R.error("Token无效或已过期");
            }

            // 从token中获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            System.out.println("Token验证成功，用户ID: " + userId);

            if (userId == null) {
                System.out.println("无法从token中获取用户ID");
                return R.error("Token格式错误");
            }

            // 验证用户是否存在且状态正常
            User user = userService.getById(userId);
            if (user == null) {
                System.out.println("用户不存在: " + userId);
                return R.error("用户不存在");
            }

            if (user.getStatus() != 1) {
                System.out.println("用户账户已被禁用: " + userId);
                return R.error("账户已被禁用");
            }

            if (userId == 88888) {
//                User user1 = new User();
//                user1.setId(userId);
//                user1.setNickName(user.getNickName());
//                user1.setHeadImgUrl(user.getHeadImgUrl());
                return R.success(true);
            }
            return R.error("无权限");
        } catch (Exception e) {
            System.err.println("Token验证异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("Token验证失败");
        }
    }
}