import { ref, computed } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'
import { useUserStore } from '@/stores'

// 路由权限守卫
export function useRouteGuard() {
  const dashboardStore = useDashboardStore()
  const userStore = useUserStore()
  
  // 当前路由路径
  const currentPath = ref('dashboard')
  
  // 路由历史记录
  const routeHistory = ref<string[]>(['dashboard'])
  
  // 菜单配置（与AdminSidebar保持一致）
  const menuItems = ref([
    {
      id: 'dashboard',
      title: '首页',
      icon: '📊',
      path: 'dashboard',
      permission: 'dashboard:view'
    },
    {
      id: 'system-function',
      title: '系统功能',
      icon: '⚙️',
      path: 'system-function',
      permission: 'system:manage',
      children: [
        {
          id: 'user-management',
          title: '用户管理',
          icon: '👥',
          path: 'system-function/user-management',
          permission: 'user:manage'
        },
        {
          id: 'role-management',
          title: '角色管理',
          icon: '🔐',
          path: 'system-function/role-management',
          permission: 'role:manage'
        },
        {
          id: 'partner-management',
          title: '伴侣管理',
          icon: '🔐',
          path: 'system-function/partner-management',
          permission: 'partner:manage'
        },
        {
          id: 'createChat-management',
          title: '初始聊天管理',
          icon: '🔐',
          path: 'system-function/createChat-management',
          permission: 'createChat:manage'
        },
        {
          id: 'permission-management',
          title: '权限管理',
          icon: '🛡️',
          path: 'system-function/permission-management',
          permission: 'permission:manage'
        }
      ]
    },
    {
      id: 'system-monitor',
      title: '系统监控',
      icon: '📈',
      path: 'system-monitor',
      permission: 'monitor:view',
      children: [
        {
          id: 'server-monitor',
          title: '服务器监控',
          icon: '🖥️',
          path: 'system-monitor/server-monitor',
          permission: 'monitor:server'
        },
        {
          id: 'database-monitor',
          title: '数据库监控',
          icon: '🗄️',
          path: 'system-monitor/database-monitor',
          permission: 'monitor:database'
        },
        {
          id: 'application-monitor',
          title: '应用监控',
          icon: '📱',
          path: 'system-monitor/application-monitor',
          permission: 'monitor:application'
        }
      ]
    },
    {
      id: 'system-tools',
      title: '系统工具',
      icon: '🔧',
      path: 'system-tools',
      permission: 'tools:use',
      children: [
        {
          id: 'database-backup',
          title: '数据库备份',
          icon: '💾',
          path: 'system-tools/database-backup',
          permission: 'tools:backup'
        },
        {
          id: 'log-viewer',
          title: '日志查看器',
          icon: '📋',
          path: 'system-tools/log-viewer',
          permission: 'tools:logs'
        },
        {
          id: 'cache-management',
          title: '缓存管理',
          icon: '🗂️',
          path: 'system-tools/cache-management',
          permission: 'tools:cache'
        },
        {
          id: 'system-cleanup',
          title: '系统清理',
          icon: '🧹',
          path: 'system-tools/system-cleanup',
          permission: 'tools:cleanup'
        }
      ]
    }
  ])
  
  // 权限检查
  const hasPermission = (permission?: string): boolean => {
    if (!permission) return true
    
    // 简化权限检查，实际项目中应该从用户权限store中获取
    // const userPermissions = userStore.permissions || []
    // return userPermissions.includes(permission)
    
    // 目前返回true，允许所有访问
    return true
  }
  
  // 查找菜单项
  const findMenuItem = (path: string) => {
    for (const menu of menuItems.value) {
      if (menu.path === path) {
        return menu
      }
      if (menu.children) {
        for (const child of menu.children) {
          if (child.path === path) {
            return child
          }
        }
      }
    }
    return null
  }
  
  // 查找父菜单
  const findParentMenu = (path: string) => {
    for (const menu of menuItems.value) {
      if (menu.children) {
        for (const child of menu.children) {
          if (child.path === path) {
            return menu
          }
        }
      }
    }
    return null
  }
  
  // 生成面包屑导航
  const generateBreadcrumb = (path: string) => {
    const breadcrumb = []
    const menuItem = findMenuItem(path)
    
    if (!menuItem) {
      return [{ title: '首页', path: 'dashboard' }]
    }
    
    // 查找父菜单
    const parentMenu = findParentMenu(path)
    if (parentMenu) {
      breadcrumb.push({
        title: parentMenu.title,
        path: parentMenu.path
      })
    }
    
    // 添加当前菜单
    breadcrumb.push({
      title: menuItem.title,
      path: menuItem.path
    })
    
    return breadcrumb
  }
  
  // 路由守卫
  const canNavigate = (path: string): boolean => {
    const menuItem = findMenuItem(path)
    
    if (!menuItem) {
      console.warn(`路由 ${path} 不存在`)
      return false
    }
    
    if (!hasPermission(menuItem.permission)) {
      console.warn(`没有访问 ${path} 的权限`)
      uni.showToast({
        title: '没有访问权限',
        icon: 'error'
      })
      return false
    }
    
    return true
  }
  
  // 导航到指定路径
  const navigateTo = (path: string): boolean => {
    if (!canNavigate(path)) {
      return false
    }
    
    // 更新当前路径
    currentPath.value = path
    
    // 添加到历史记录
    if (routeHistory.value[routeHistory.value.length - 1] !== path) {
      routeHistory.value.push(path)
      
      // 限制历史记录长度
      if (routeHistory.value.length > 20) {
        routeHistory.value = routeHistory.value.slice(-20)
      }
    }
    
    // 更新面包屑
    const breadcrumb = generateBreadcrumb(path)
    dashboardStore.setBreadcrumb(breadcrumb)
    
    // 更新当前模块
    dashboardStore.setCurrentModule(path)
    
    // 触发路由变化事件
    uni.$emit('admin-route-change', path)
    
    return true
  }
  
  // 返回上一页
  const goBack = (): boolean => {
    if (routeHistory.value.length <= 1) {
      return false
    }
    
    // 移除当前页面
    routeHistory.value.pop()
    
    // 获取上一页路径
    const previousPath = routeHistory.value[routeHistory.value.length - 1]
    
    // 导航到上一页（不添加到历史记录）
    currentPath.value = previousPath
    
    const breadcrumb = generateBreadcrumb(previousPath)
    dashboardStore.setBreadcrumb(breadcrumb)
    dashboardStore.setCurrentModule(previousPath)
    
    uni.$emit('admin-route-change', previousPath)
    
    return true
  }
  
  // 获取当前路径的菜单信息
  const getCurrentMenuItem = computed(() => {
    return findMenuItem(currentPath.value)
  })
  
  // 获取面包屑导航
  const breadcrumb = computed(() => {
    return dashboardStore.breadcrumb
  })
  
  // 检查是否可以返回
  const canGoBack = computed(() => {
    return routeHistory.value.length > 1
  })
  
  // 初始化路由
  const initRoute = (initialPath = 'dashboard') => {
    if (canNavigate(initialPath)) {
      navigateTo(initialPath)
    } else {
      // 如果初始路径无权限，导航到首页
      navigateTo('dashboard')
    }
  }
  
  return {
    // 状态
    currentPath,
    routeHistory,
    menuItems,
    
    // 计算属性
    getCurrentMenuItem,
    breadcrumb,
    canGoBack,
    
    // 方法
    hasPermission,
    findMenuItem,
    findParentMenu,
    generateBreadcrumb,
    canNavigate,
    navigateTo,
    goBack,
    initRoute
  }
}
