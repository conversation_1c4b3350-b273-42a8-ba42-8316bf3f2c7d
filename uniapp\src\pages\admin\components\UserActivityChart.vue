<template>
  <view class="user-activity-chart">
    <BaseChart
      type="line"
      :data="chartData"
      title="用户活动趋势"
      :width="400"
      :height="300"
      :loading="loading"
      :show-legend="true"
      @click="handleChartClick"
      @hover="handleChartHover"
    />
    
    <!-- 时间范围选择器 -->
    <view class="time-range-selector">
      <view 
        v-for="range in timeRanges" 
        :key="range.value"
        :class="['range-item', { active: selectedRange === range.value }]"
        @click="selectTimeRange(range.value)"
      >
        {{ range.label }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import BaseChart from './BaseChart.vue'
import { useDashboardStore } from '@/stores/dashboard'

interface UserActivityData {
  date: string
  activeUsers: number
  newUsers: number
  totalSessions: number
}

const dashboardStore = useDashboardStore()

// 响应式数据
const loading = ref(false)
const selectedRange = ref('7d')
const activityData = ref<UserActivityData[]>([])

// 时间范围选项
const timeRanges = [
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' }
]

// 计算属性
const chartData = computed(() => {
  return activityData.value.map(item => ({
    label: formatDate(item.date),
    value: item.activeUsers,
    color: '#1890ff'
  }))
})

// 方法
const loadActivityData = async () => {
  loading.value = true
  
  try {
    const data = await dashboardStore.fetchChartData(`user-activity?range=${selectedRange.value}`)
    
    if (data && Array.isArray(data)) {
      activityData.value = data
    } else {
      // 使用模拟数据
      activityData.value = generateMockData()
    }
  } catch (error) {
    console.error('加载用户活动数据失败:', error)
    activityData.value = generateMockData()
  } finally {
    loading.value = false
  }
}

const generateMockData = (): UserActivityData[] => {
  const data: UserActivityData[] = []
  const days = selectedRange.value === '7d' ? 7 : selectedRange.value === '30d' ? 30 : 90
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    
    data.push({
      date: date.toISOString().split('T')[0],
      activeUsers: Math.floor(Math.random() * 1000) + 500,
      newUsers: Math.floor(Math.random() * 100) + 20,
      totalSessions: Math.floor(Math.random() * 2000) + 800
    })
  }
  
  return data
}

const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const selectTimeRange = (range: string) => {
  selectedRange.value = range
  loadActivityData()
}

const handleChartClick = (item: any, index: number) => {
  const data = activityData.value[index]
  if (data) {
    // 显示详细的用户活动信息
    const content = [
      `日期: ${data.date}`,
      `活跃用户: ${data.activeUsers.toLocaleString()}`,
      `新用户: ${data.newUsers.toLocaleString()}`,
      `总会话: ${data.totalSessions.toLocaleString()}`,
      `用户增长率: ${((data.newUsers / data.activeUsers) * 100).toFixed(1)}%`
    ].join('\n')
    
    uni.showModal({
      title: '用户活动详情',
      content,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '查看更多',
      success: (res) => {
        if (res.confirm) {
          // 可以跳转到详细的用户分析页面
          console.log('跳转到用户详情页面:', data)
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  }
}

const handleChartHover = (item: any, index: number) => {
  const data = activityData.value[index]
  if (data) {
    // 可以在这里添加悬停效果，比如高亮显示
    console.log('悬停在数据点:', data)
  }
}

// 监听时间范围变化
watch(selectedRange, () => {
  loadActivityData()
})

// 生命周期
onMounted(() => {
  loadActivityData()
})

// 暴露刷新方法
const refresh = () => {
  loadActivityData()
}

defineExpose({
  refresh
})
</script>

<style lang="scss" scoped>
.user-activity-chart {
  .time-range-selector {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 16px;

    .range-item {
      padding: 6px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 12px;
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.active {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;
      }
    }
  }
}

// 暗色主题支持
.admin-layout.dark-theme .user-activity-chart {
  .time-range-selector {
    .range-item {
      border-color: #434343;
      color: #ccc;
      background-color: #1f1f1f;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.active {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;
      }
    }
  }
}
</style>