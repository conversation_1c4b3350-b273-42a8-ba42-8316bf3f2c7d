<template>
  <view class="data-form" :class="{ 'form-loading': loading }">
    <!-- 表单头部 -->
    <view v-if="showHeader" class="form-header">
      <view class="form-title">{{ getFormTitle() }}</view>
      <view v-if="mode !== 'view' && !readonly" class="form-actions">
        <button 
          class="action-btn"
          @click="handleReset"
          :disabled="loading"
        >
          重置
        </button>
        <button 
          class="action-btn primary"
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          :class="{ loading: submitting }"
        >
          {{ mode === 'create' ? '创建' : '保存' }}
        </button>
      </view>
    </view>

    <!-- 表单主体 -->
    <view class="form-body">
      <!-- 分组表单 -->
      <template v-if="hasSections">
        <view 
          v-for="(sectionFields, sectionName) in fieldSections" 
          :key="sectionName"
          class="form-section"
        >
          <view v-if="sectionName !== 'default'" class="section-title">
            {{ sectionName }}
          </view>
          <view class="section-content">
            <view class="form-grid" :class="gridClass">
              <view 
                v-for="field in sectionFields" 
                :key="field.key"
                class="form-field"
                :class="{ readonly: mode === 'view' || field.disabled || readonly }"
              >
                <!-- 字段内容将在这里渲染 -->
                <template v-if="true">
                  <!-- 字段标签 -->
                  <label class="field-label" :for="field.key">
                    {{ field.label }}
                    <span v-if="field.required && mode !== 'view'" class="required-mark">*</span>
                  </label>

                  <!-- 字段输入控件 -->
                  <template v-if="field.type === 'text' || field.type === 'email' || field.type === 'password'">
                    <input 
                      :id="field.key"
                      v-model="formData[field.key]"
                      class="field-input"
                      :class="{ error: fieldErrors[field.key] }"
                      :type="field.type"
                      :placeholder="field.placeholder"
                      :disabled="mode === 'view' || field.disabled || readonly"
                      @blur="validateField(field)"
                      @input="clearFieldError(field.key)"
                    />
                  </template>

                  <template v-else-if="field.type === 'number'">
                    <input 
                      :id="field.key"
                      v-model.number="formData[field.key]"
                      class="field-input"
                      :class="{ error: fieldErrors[field.key] }"
                      type="number"
                      :placeholder="field.placeholder"
                      :min="field.min"
                      :max="field.max"
                      :step="field.step"
                      :disabled="mode === 'view' || field.disabled || readonly"
                      @blur="validateField(field)"
                      @input="clearFieldError(field.key)"
                    />
                  </template>

                  <template v-else-if="field.type === 'textarea'">
                    <textarea 
                      :id="field.key"
                      v-model="formData[field.key]"
                      class="field-input textarea"
                      :class="{ error: fieldErrors[field.key] }"
                      :placeholder="field.placeholder"
                      :disabled="mode === 'view' || field.disabled || readonly"
                      @blur="validateField(field)"
                      @input="clearFieldError(field.key)"
                    ></textarea>
                  </template>

                  <template v-else-if="field.type === 'select'">
                    <select 
                      :id="field.key"
                      v-model="formData[field.key]"
                      class="field-select"
                      :class="{ error: fieldErrors[field.key] }"
                      :disabled="mode === 'view' || field.disabled || readonly"
                      @change="validateField(field)"
                    >
                      <option value="">请选择{{ field.label }}</option>
                      <option 
                        v-for="option in field.options" 
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label }}
                      </option>
                    </select>
                  </template>

                  <template v-else-if="field.type === 'radio'">
                    <view class="field-radio-group">
                      <label 
                        v-for="option in field.options" 
                        :key="option.value"
                        class="radio-item"
                      >
                        <input 
                          v-model="formData[field.key]"
                          class="radio-input"
                          type="radio"
                          :value="option.value"
                          :disabled="mode === 'view' || field.disabled || readonly"
                          @change="validateField(field)"
                        />
                        <span class="radio-label">{{ option.label }}</span>
                      </label>
                    </view>
                  </template>

                  <template v-else-if="field.type === 'checkbox'">
                    <view class="field-checkbox-group">
                      <label 
                        v-for="option in field.options" 
                        :key="option.value"
                        class="checkbox-item"
                      >
                        <input 
                          v-model="formData[field.key]"
                          class="checkbox-input"
                          type="checkbox"
                          :value="option.value"
                          :disabled="mode === 'view' || field.disabled || readonly"
                          @change="validateField(field)"
                        />
                        <span class="checkbox-label">{{ option.label }}</span>
                      </label>
                    </view>
                  </template>

                  <template v-else-if="field.type === 'date'">
                    <input 
                      :id="field.key"
                      v-model="formData[field.key]"
                      class="field-input"
                      :class="{ error: fieldErrors[field.key] }"
                      type="date"
                      :disabled="mode === 'view' || field.disabled || readonly"
                      @blur="validateField(field)"
                      @input="clearFieldError(field.key)"
                    />
                  </template>

                  <template v-else-if="field.type === 'datetime'">
                    <input 
                      :id="field.key"
                      v-model="formData[field.key]"
                      class="field-input"
                      :class="{ error: fieldErrors[field.key] }"
                      type="datetime-local"
                      :disabled="mode === 'view' || field.disabled || readonly"
                      @blur="validateField(field)"
                      @input="clearFieldError(field.key)"
                    />
                  </template>

                  <template v-else-if="field.type === 'switch'">
                    <view class="field-switch">
                      <label class="switch-container">
                        <input 
                          v-model="formData[field.key]"
                          class="switch-input"
                          type="checkbox"
                          :disabled="mode === 'view' || field.disabled || readonly"
                          @change="validateField(field)"
                        />
                        <span class="switch-slider"></span>
                        <span class="switch-text">{{ formData[field.key] ? '开启' : '关闭' }}</span>
                      </label>
                    </view>
                  </template>

                  <template v-else-if="field.type === 'file'">
                    <view class="field-file">
                      <input 
                        :id="field.key"
                        class="file-input"
                        type="file"
                        :accept="field.accept"
                        :multiple="field.multiple"
                        :disabled="mode === 'view' || field.disabled || readonly"
                        @change="handleFileChange(field, $event)"
                      />
                      <view v-if="formData[field.key]" class="file-preview">
                        <view v-if="Array.isArray(formData[field.key])">
                          <view 
                            v-for="(file, index) in formData[field.key]" 
                            :key="index"
                            class="file-item"
                          >
                            {{ file.name || file }}
                            <button 
                              v-if="mode !== 'view' && !readonly"
                              class="file-remove"
                              @click="removeFile(field.key, index)"
                            >
                              ×
                            </button>
                          </view>
                        </view>
                        <view v-else class="file-item">
                          {{ formData[field.key].name || formData[field.key] }}
                          <button 
                            v-if="mode !== 'view' && !readonly"
                            class="file-remove"
                            @click="removeFile(field.key)"
                          >
                            ×
                          </button>
                        </view>
                      </view>
                    </view>
                  </template>

                  <template v-else-if="field.type === 'color'">
                    <input 
                      :id="field.key"
                      v-model="formData[field.key]"
                      class="field-input color-input"
                      :class="{ error: fieldErrors[field.key] }"
                      type="color"
                      :disabled="mode === 'view' || field.disabled || readonly"
                      @blur="validateField(field)"
                      @input="clearFieldError(field.key)"
                    />
                  </template>

                  <template v-else-if="field.type === 'range'">
                    <view class="field-range">
                      <input 
                        :id="field.key"
                        v-model.number="formData[field.key]"
                        class="range-input"
                        type="range"
                        :min="field.min || 0"
                        :max="field.max || 100"
                        :step="field.step || 1"
                        :disabled="mode === 'view' || field.disabled || readonly"
                        @input="validateField(field)"
                      />
                      <span class="range-value">{{ formData[field.key] || 0 }}</span>
                    </view>
                  </template>

                  <!-- 字段错误信息 -->
                  <view v-if="fieldErrors[field.key]" class="field-error">
                    <span class="error-icon">⚠</span>
                    {{ fieldErrors[field.key] }}
                  </view>

                  <!-- 字段帮助信息 -->
                  <view v-if="field.help" class="field-help">
                    {{ field.help }}
                  </view>
                </template>
              </view>
            </view>
          </view>
        </view>
      </template>

      <!-- 非分组表单 -->
      <template v-else>
        <view class="form-grid" :class="gridClass">
          <view 
            v-for="field in visibleFields" 
            :key="field.key"
            class="form-field"
            :class="{ readonly: mode === 'view' || field.disabled || readonly }"
          >
          <!-- 字段标签 -->
          <label class="field-label" :for="field.key">
            {{ field.label }}
            <span v-if="field.required && mode !== 'view'" class="required-mark">*</span>
          </label>

          <!-- 文本输入框 -->
          <input 
            v-if="field.type === 'text' || field.type === 'email' || field.type === 'password'"
            :id="field.key"
            v-model="formData[field.key]"
            class="field-input"
            :class="{ error: fieldErrors[field.key] }"
            :type="field.type"
            :placeholder="field.placeholder"
            :disabled="mode === 'view' || field.disabled"
            @blur="validateField(field)"
            @input="clearFieldError(field.key)"
          />

          <!-- 数字输入框 -->
          <input 
            v-else-if="field.type === 'number'"
            :id="field.key"
            v-model.number="formData[field.key]"
            class="field-input"
            :class="{ error: fieldErrors[field.key] }"
            type="number"
            :placeholder="field.placeholder"
            :disabled="mode === 'view' || field.disabled"
            @blur="validateField(field)"
            @input="clearFieldError(field.key)"
          />

          <!-- 文本域 -->
          <textarea 
            v-else-if="field.type === 'textarea'"
            :id="field.key"
            v-model="formData[field.key]"
            class="field-input textarea"
            :class="{ error: fieldErrors[field.key] }"
            :placeholder="field.placeholder"
            :disabled="mode === 'view' || field.disabled"
            @blur="validateField(field)"
            @input="clearFieldError(field.key)"
          ></textarea>

          <!-- 下拉选择 -->
          <select 
            v-else-if="field.type === 'select'"
            :id="field.key"
            v-model="formData[field.key]"
            class="field-select"
            :class="{ error: fieldErrors[field.key] }"
            :disabled="mode === 'view' || field.disabled"
            @change="validateField(field)"
          >
            <option value="">请选择{{ field.label }}</option>
            <option 
              v-for="option in field.options" 
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>

          <!-- 单选框组 -->
          <view 
            v-else-if="field.type === 'radio'"
            class="field-radio-group"
          >
            <label 
              v-for="option in field.options" 
              :key="option.value"
              class="radio-item"
            >
              <input 
                v-model="formData[field.key]"
                class="radio-input"
                type="radio"
                :value="option.value"
                :disabled="mode === 'view' || field.disabled"
                @change="validateField(field)"
              />
              <span class="radio-label">{{ option.label }}</span>
            </label>
          </view>

          <!-- 复选框组 -->
          <view 
            v-else-if="field.type === 'checkbox'"
            class="field-checkbox-group"
          >
            <label 
              v-for="option in field.options" 
              :key="option.value"
              class="checkbox-item"
            >
              <input 
                v-model="formData[field.key]"
                class="checkbox-input"
                type="checkbox"
                :value="option.value"
                :disabled="mode === 'view' || field.disabled"
                @change="validateField(field)"
              />
              <span class="checkbox-label">{{ option.label }}</span>
            </label>
          </view>

          <!-- 日期输入 -->
          <input 
            v-else-if="field.type === 'date'"
            :id="field.key"
            v-model="formData[field.key]"
            class="field-input"
            :class="{ error: fieldErrors[field.key] }"
            type="date"
            :disabled="mode === 'view' || field.disabled"
            @blur="validateField(field)"
            @input="clearFieldError(field.key)"
          />

          <!-- 日期时间输入 -->
          <input 
            v-else-if="field.type === 'datetime'"
            :id="field.key"
            v-model="formData[field.key]"
            class="field-input"
            :class="{ error: fieldErrors[field.key] }"
            type="datetime-local"
            :disabled="mode === 'view' || field.disabled"
            @blur="validateField(field)"
            @input="clearFieldError(field.key)"
          />

          <!-- 开关切换 -->
          <view 
            v-else-if="field.type === 'switch'"
            class="field-switch"
          >
            <label class="switch-container">
              <input 
                v-model="formData[field.key]"
                class="switch-input"
                type="checkbox"
                :disabled="mode === 'view' || field.disabled || readonly"
                @change="validateField(field)"
              />
              <span class="switch-slider"></span>
              <span class="switch-text">{{ formData[field.key] ? '开启' : '关闭' }}</span>
            </label>
          </view>

          <!-- 文件上传 -->
          <view 
            v-else-if="field.type === 'file'"
            class="field-file"
          >
            <input 
              :id="field.key"
              class="file-input"
              type="file"
              :accept="field.accept"
              :multiple="field.multiple"
              :disabled="mode === 'view' || field.disabled || readonly"
              @change="handleFileChange(field, $event)"
            />
            <view v-if="formData[field.key]" class="file-preview">
              <view v-if="Array.isArray(formData[field.key])">
                <view 
                  v-for="(file, index) in formData[field.key]" 
                  :key="index"
                  class="file-item"
                >
                  {{ file.name || file }}
                  <button 
                    v-if="mode !== 'view' && !readonly"
                    class="file-remove"
                    @click="removeFile(field.key, index)"
                  >
                    ×
                  </button>
                </view>
              </view>
              <view v-else class="file-item">
                {{ formData[field.key].name || formData[field.key] }}
                <button 
                  v-if="mode !== 'view' && !readonly"
                  class="file-remove"
                  @click="removeFile(field.key)"
                >
                  ×
                </button>
              </view>
            </view>
          </view>

          <!-- 颜色选择器 -->
          <input 
            v-else-if="field.type === 'color'"
            :id="field.key"
            v-model="formData[field.key]"
            class="field-input color-input"
            :class="{ error: fieldErrors[field.key] }"
            type="color"
            :disabled="mode === 'view' || field.disabled || readonly"
            @blur="validateField(field)"
            @input="clearFieldError(field.key)"
          />

          <!-- 范围滑块 -->
          <view 
            v-else-if="field.type === 'range'"
            class="field-range"
          >
            <input 
              :id="field.key"
              v-model.number="formData[field.key]"
              class="range-input"
              type="range"
              :min="field.min || 0"
              :max="field.max || 100"
              :step="field.step || 1"
              :disabled="mode === 'view' || field.disabled || readonly"
              @input="validateField(field)"
            />
            <span class="range-value">{{ formData[field.key] || 0 }}</span>
          </view>

          <!-- 字段错误信息 -->
          <view v-if="fieldErrors[field.key]" class="field-error">
            <span class="error-icon">⚠</span>
            {{ fieldErrors[field.key] }}
          </view>

          <!-- 字段帮助信息 -->
          <view v-if="field.help" class="field-help">
            {{ field.help }}
          </view>
        </view>
      </view>
      </template>
    </view>

    <!-- 表单底部 -->
    <view v-if="showFooter" class="form-footer">
      <button 
        class="footer-btn"
        @click="handleCancel"
        :disabled="loading"
      >
        取消
      </button>
      <button 
        v-if="mode !== 'view'"
        class="footer-btn"
        @click="handleReset"
        :disabled="loading"
      >
        重置
      </button>
      <button 
        v-if="mode !== 'view'"
        class="footer-btn primary"
        @click="handleSubmit"
        :disabled="loading || !isFormValid"
        :class="{ loading: submitting }"
      >
        {{ mode === 'create' ? '创建' : '保存' }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { DataFormProps, FormField, ValidationRule } from '../../types/admin'

// Props定义
interface Props extends DataFormProps {
  title?: string
  loading?: boolean
  submitting?: boolean
  showFooter?: boolean
  columns?: number
  showHeader?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  loading: false,
  submitting: false,
  showFooter: true,
  columns: 1,
  showHeader: true,
  readonly: false
})

// Emits定义
const emit = defineEmits<{
  submit: [data: Record<string, any>]
  cancel: []
  reset: []
  change: [key: string, value: any]
}>()

// 响应式数据
const formData = ref<Record<string, any>>({ ...props.data })
const fieldErrors = ref<Record<string, string>>({})
const originalData = ref<Record<string, any>>({ ...props.data })

// 计算属性
const visibleFields = computed(() => {
  return props.fields.filter(field => {
    // 根据条件函数过滤字段
    if (field.condition && typeof field.condition === 'function') {
      return field.condition(formData.value)
    }
    return true
  })
})

const fieldSections = computed(() => {
  const sections: Record<string, FormField[]> = {}
  const defaultSection = 'default'
  
  visibleFields.value.forEach(field => {
    const sectionName = field.section || defaultSection
    if (!sections[sectionName]) {
      sections[sectionName] = []
    }
    sections[sectionName].push(field)
  })
  
  return sections
})

const hasSections = computed(() => {
  return Object.keys(fieldSections.value).length > 1 || 
         (Object.keys(fieldSections.value).length === 1 && !fieldSections.value.default)
})

const gridClass = computed(() => {
  switch (props.columns) {
    case 1:
      return 'single-column'
    case 2:
      return 'two-columns'
    default:
      return ''
  }
})

const isFormValid = computed(() => {
  // 检查必填字段
  const requiredFields = visibleFields.value.filter(field => field.required)
  for (const field of requiredFields) {
    const value = formData.value[field.key]
    if (value === undefined || value === null || value === '') {
      return false
    }
    // 数组类型的空值检查
    if (Array.isArray(value) && value.length === 0) {
      return false
    }
  }

  // 检查是否有验证错误
  return Object.keys(fieldErrors.value).length === 0
})

// 方法
const getFormTitle = (): string => {
  if (props.title) return props.title
  
  switch (props.mode) {
    case 'create':
      return '创建记录'
    case 'edit':
      return '编辑记录'
    case 'view':
      return '查看记录'
    default:
      return '表单'
  }
}

const validateField = (field: FormField): boolean => {
  const value = formData.value[field.key]
  const rules = field.rules || []

  // 清除之前的错误
  delete fieldErrors.value[field.key]

  // 验证规则
  for (const rule of rules) {
    const error = validateRule(value, rule, field.label)
    if (error) {
      fieldErrors.value[field.key] = error
      return false
    }
  }

  return true
}

const validateRule = (value: any, rule: ValidationRule, fieldLabel: string): string | null => {
  switch (rule.type) {
    case 'required':
      // 处理不同类型的空值检查
      if (value === undefined || value === null || value === '') {
        return rule.message || `${fieldLabel}不能为空`
      }
      // 数组类型的空值检查
      if (Array.isArray(value) && value.length === 0) {
        return rule.message || `${fieldLabel}不能为空`
      }
      break
    
    case 'email':
      if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return rule.message || `${fieldLabel}格式不正确`
      }
      break
    
    case 'min':
      if (typeof value === 'string' && value.length < rule.value) {
        return rule.message || `${fieldLabel}长度不能少于${rule.value}个字符`
      }
      if (typeof value === 'number' && value < rule.value) {
        return rule.message || `${fieldLabel}不能小于${rule.value}`
      }
      // 数组长度检查
      if (Array.isArray(value) && value.length < rule.value) {
        return rule.message || `${fieldLabel}至少选择${rule.value}项`
      }
      break
    
    case 'max':
      if (typeof value === 'string' && value.length > rule.value) {
        return rule.message || `${fieldLabel}长度不能超过${rule.value}个字符`
      }
      if (typeof value === 'number' && value > rule.value) {
        return rule.message || `${fieldLabel}不能大于${rule.value}`
      }
      // 数组长度检查
      if (Array.isArray(value) && value.length > rule.value) {
        return rule.message || `${fieldLabel}最多选择${rule.value}项`
      }
      break
    
    case 'pattern':
      if (value && !new RegExp(rule.value).test(value)) {
        return rule.message || `${fieldLabel}格式不正确`
      }
      break
  }

  return null
}

const validateForm = (): boolean => {
  let isValid = true
  fieldErrors.value = {}

  for (const field of props.fields) {
    if (!validateField(field)) {
      isValid = false
    }
  }

  return isValid
}

const clearFieldError = (key: string) => {
  if (fieldErrors.value[key]) {
    delete fieldErrors.value[key]
  }
}

const handleSubmit = async () => {
  if (validateForm()) {
    try {
      emit('submit', { ...formData.value })
    } catch (error) {
      console.error('Form submission error:', error)
      // 可以在这里添加错误处理逻辑
    }
  } else {
    // 滚动到第一个错误字段
    scrollToFirstError()
  }
}

const scrollToFirstError = () => {
  const firstErrorKey = Object.keys(fieldErrors.value)[0]
  if (firstErrorKey) {
    const errorElement = document.getElementById(firstErrorKey)
    if (errorElement) {
      errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
      errorElement.focus()
    }
  }
}

const handleReset = () => {
  formData.value = { ...originalData.value }
  fieldErrors.value = {}
  emit('reset')
}

const handleCancel = () => {
  emit('cancel')
}

const handleFieldChange = (key: string, value: any) => {
  formData.value[key] = value
  emit('change', key, value)
}

const handleFileChange = (field: FormField, event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (!files || files.length === 0) {
    formData.value[field.key] = field.multiple ? [] : null
    return
  }
  
  if (field.multiple) {
    formData.value[field.key] = Array.from(files)
  } else {
    formData.value[field.key] = files[0]
  }
  
  validateField(field)
  handleFieldChange(field.key, formData.value[field.key])
}

const removeFile = (fieldKey: string, index?: number) => {
  if (index !== undefined) {
    // 移除数组中的特定文件
    const files = formData.value[fieldKey] as File[]
    files.splice(index, 1)
    formData.value[fieldKey] = [...files]
  } else {
    // 移除单个文件
    formData.value[fieldKey] = null
  }
  
  handleFieldChange(fieldKey, formData.value[fieldKey])
}

// 监听器
watch(() => props.data, (newData) => {
  formData.value = { ...newData }
  originalData.value = { ...newData }
  fieldErrors.value = {}
}, { deep: true, immediate: true })

watch(formData, (newData, oldData) => {
  // 监听表单数据变化，可以进行实时验证
  for (const key in newData) {
    if (newData[key] !== oldData?.[key]) {
      handleFieldChange(key, newData[key])
    }
  }
}, { deep: true })

// 监听模式变化，清除错误状态
watch(() => props.mode, () => {
  fieldErrors.value = {}
})

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化逻辑
  originalData.value = { ...props.data }
})

// 获取表单变更的字段
const getChangedFields = (): Record<string, any> => {
  const changed: Record<string, any> = {}
  for (const key in formData.value) {
    if (formData.value[key] !== originalData.value[key]) {
      changed[key] = formData.value[key]
    }
  }
  return changed
}

// 检查表单是否有变更
const hasChanges = computed(() => {
  return Object.keys(getChangedFields()).length > 0
})

// 设置字段值
const setFieldValue = (key: string, value: any) => {
  formData.value[key] = value
  // 清除该字段的错误
  clearFieldError(key)
  // 触发变更事件
  handleFieldChange(key, value)
}

// 获取字段值
const getFieldValue = (key: string) => {
  return formData.value[key]
}

// 设置字段错误
const setFieldError = (key: string, error: string) => {
  fieldErrors.value[key] = error
}

// 清除所有错误
const clearAllErrors = () => {
  fieldErrors.value = {}
}

// 暴露方法给父组件
defineExpose({
  validate: validateForm,
  reset: handleReset,
  getFormData: () => formData.value,
  setFormData: (data: Record<string, any>) => {
    formData.value = { ...data }
    originalData.value = { ...data }
    clearAllErrors()
  },
  getChangedFields,
  hasChanges,
  setFieldValue,
  getFieldValue,
  setFieldError,
  clearFieldError,
  clearAllErrors,
  scrollToFirstError
})
</script>

<style lang="scss" scoped>
@import '../../styles/data-form.scss';
</style>