/**
 * CRUD组合函数测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useCrud } from '../useCrud'
import { mockUniRequest, createMockResponse, mockUniModal } from '../../../test/setup'

// 模拟API
const mockCrudApi = {
  list: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
  get: vi.fn()
}

// 模拟UI管理器
vi.mock('../useUIManager', () => ({
  globalUIManager: {
    showSuccess: vi.fn(),
    showError: vi.fn(),
    showLoading: vi.fn().mockReturnValue(1),
    hideLoading: vi.fn(),
    showDeleteConfirm: vi.fn().mockResolvedValue(true),
    showBatchConfirm: vi.fn().mockResolvedValue(true)
  }
}))

describe('useCrud', () => {
  let crud: ReturnType<typeof useCrud>

  beforeEach(() => {
    crud = useCrud(mockCrudApi, {
      pageSize: 10,
      showLoadingToast: true
    })
    vi.clearAllMocks()
  })

  describe('数据加载', () => {
    it('应该成功加载数据', async () => {
      const mockData = {
        items: [
          { id: 1, name: '项目1' },
          { id: 2, name: '项目2' }
        ],
        total: 2
      }

      mockCrudApi.list.mockResolvedValue(mockData)

      await crud.loadData()

      expect(crud.data.value).toEqual(mockData.items)
      expect(crud.total.value).toBe(2)
      expect(crud.loading.value).toBe(false)
      expect(crud.error.value).toBe('')
    })

    it('应该处理加载错误', async () => {
      const error = new Error('加载失败')
      mockCrudApi.list.mockRejectedValue(error)

      await crud.loadData()

      expect(crud.data.value).toEqual([])
      expect(crud.total.value).toBe(0)
      expect(crud.loading.value).toBe(false)
      expect(crud.error.value).toBe('加载失败')
    })

    it('应该支持分页加载', async () => {
      const mockData = {
        items: [{ id: 3, name: '项目3' }],
        total: 10
      }

      mockCrudApi.list.mockResolvedValue(mockData)

      await crud.changePage(2)

      expect(mockCrudApi.list).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 2,
          pageSize: 10
        })
      )
      expect(crud.pagination.current).toBe(2)
    })

    it('应该支持搜索', async () => {
      const mockData = { items: [], total: 0 }
      mockCrudApi.list.mockResolvedValue(mockData)

      await crud.search('测试关键词')

      expect(mockCrudApi.list).toHaveBeenCalledWith(
        expect.objectContaining({
          keyword: '测试关键词'
        })
      )
    })

    it('应该支持排序', async () => {
      const mockData = { items: [], total: 0 }
      mockCrudApi.list.mockResolvedValue(mockData)

      await crud.sort('name', 'desc')

      expect(mockCrudApi.list).toHaveBeenCalledWith(
        expect.objectContaining({
          sortBy: 'name',
          sortOrder: 'desc'
        })
      )
    })
  })

  describe('数据创建', () => {
    it('应该成功创建数据', async () => {
      const newItem = { name: '新项目' }
      const createdItem = { id: 3, name: '新项目' }

      mockCrudApi.create.mockResolvedValue(createdItem)
      mockCrudApi.list.mockResolvedValue({ items: [createdItem], total: 1 })

      const result = await crud.create(newItem)

      expect(result).toEqual(createdItem)
      expect(mockCrudApi.create).toHaveBeenCalledWith(newItem)
      expect(crud.submitting.value).toBe(false)
    })

    it('应该处理创建错误', async () => {
      const newItem = { name: '新项目' }
      const error = new Error('创建失败')

      mockCrudApi.create.mockRejectedValue(error)

      const result = await crud.create(newItem)

      expect(result).toBeNull()
      expect(crud.submitting.value).toBe(false)
      expect(crud.error.value).toBe('创建失败')
    })
  })

  describe('数据更新', () => {
    it('应该成功更新数据', async () => {
      const updateData = { name: '更新后的项目' }
      const updatedItem = { id: 1, name: '更新后的项目' }

      // 设置初始数据
      crud.data.value = [{ id: 1, name: '原项目' }]
      mockCrudApi.update.mockResolvedValue(updatedItem)

      const result = await crud.update(1, updateData)

      expect(result).toEqual(updatedItem)
      expect(mockCrudApi.update).toHaveBeenCalledWith(1, updateData)
      expect(crud.data.value[0]).toEqual(expect.objectContaining(updatedItem))
      expect(crud.submitting.value).toBe(false)
    })

    it('应该处理更新错误', async () => {
      const updateData = { name: '更新后的项目' }
      const error = new Error('更新失败')

      mockCrudApi.update.mockRejectedValue(error)

      const result = await crud.update(1, updateData)

      expect(result).toBeNull()
      expect(crud.submitting.value).toBe(false)
      expect(crud.error.value).toBe('更新失败')
    })
  })

  describe('数据删除', () => {
    it('应该成功删除数据', async () => {
      // 设置初始数据
      crud.data.value = [
        { id: 1, name: '项目1' },
        { id: 2, name: '项目2' }
      ]

      mockCrudApi.delete.mockResolvedValue(true)

      const result = await crud.remove(1, '项目1')

      expect(result).toBe(true)
      expect(mockCrudApi.delete).toHaveBeenCalledWith(1)
      expect(crud.data.value).toHaveLength(1)
      expect(crud.data.value[0].id).toBe(2)
      expect(crud.submitting.value).toBe(false)
    })

    it('应该处理删除错误', async () => {
      const error = new Error('删除失败')
      mockCrudApi.delete.mockRejectedValue(error)

      const result = await crud.remove(1)

      expect(result).toBe(false)
      expect(crud.submitting.value).toBe(false)
      expect(crud.error.value).toBe('删除失败')
    })

    it('应该支持批量删除', async () => {
      // 设置初始数据和选中项
      crud.data.value = [
        { id: 1, name: '项目1' },
        { id: 2, name: '项目2' },
        { id: 3, name: '项目3' }
      ]
      crud.selectedItems.value = [1, 2]

      mockCrudApi.delete.mockResolvedValue(true)
      mockCrudApi.list.mockResolvedValue({
        items: [{ id: 3, name: '项目3' }],
        total: 1
      })

      const result = await crud.batchDelete([1, 2])

      expect(result).toBe(true)
      expect(mockCrudApi.delete).toHaveBeenCalledTimes(2)
      expect(crud.selectedItems.value).toEqual([])
    })
  })

  describe('选择管理', () => {
    beforeEach(() => {
      crud.data.value = [
        { id: 1, name: '项目1' },
        { id: 2, name: '项目2' },
        { id: 3, name: '项目3' }
      ]
    })

    it('应该切换单项选择', () => {
      crud.toggleSelection(1)
      expect(crud.selectedItems.value).toContain(1)

      crud.toggleSelection(1)
      expect(crud.selectedItems.value).not.toContain(1)
    })

    it('应该全选', () => {
      crud.selectAll()
      expect(crud.selectedItems.value).toEqual([1, 2, 3])
    })

    it('应该清空选择', () => {
      crud.selectedItems.value = [1, 2]
      crud.clearSelection()
      expect(crud.selectedItems.value).toEqual([])
    })

    it('应该正确计算选择状态', () => {
      expect(crud.hasSelection.value).toBe(false)

      crud.selectedItems.value = [1]
      expect(crud.hasSelection.value).toBe(true)
    })
  })

  describe('工具方法', () => {
    it('应该刷新数据', async () => {
      const mockData = { items: [{ id: 1, name: '刷新后的数据' }], total: 1 }
      mockCrudApi.list.mockResolvedValue(mockData)

      await crud.refresh()

      expect(mockCrudApi.list).toHaveBeenCalled()
      expect(crud.data.value).toEqual(mockData.items)
    })

    it('应该重置查询参数', async () => {
      // 设置一些查询参数
      crud.queryParams.keyword = '搜索词'
      crud.queryParams.sortBy = 'name'
      crud.pagination.current = 3

      const mockData = { items: [], total: 0 }
      mockCrudApi.list.mockResolvedValue(mockData)

      await crud.reset()

      expect(crud.queryParams.keyword).toBe('')
      expect(crud.queryParams.sortBy).toBe('')
      expect(crud.pagination.current).toBe(1)
    })

    it('应该清除错误状态', () => {
      crud.error.value = '测试错误'
      crud.clearError()
      expect(crud.error.value).toBe('')
    })
  })

  describe('计算属性', () => {
    it('应该正确计算hasData', () => {
      expect(crud.hasData.value).toBe(false)

      crud.data.value = [{ id: 1, name: '项目1' }]
      expect(crud.hasData.value).toBe(true)
    })

    it('应该正确计算isEmpty', () => {
      crud.loading.value = false
      crud.data.value = []
      expect(crud.isEmpty.value).toBe(true)

      crud.data.value = [{ id: 1, name: '项目1' }]
      expect(crud.isEmpty.value).toBe(false)

      crud.loading.value = true
      crud.data.value = []
      expect(crud.isEmpty.value).toBe(false)
    })
  })
})
