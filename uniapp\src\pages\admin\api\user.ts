import { create<PERSON>rud<PERSON><PERSON>, createMock<PERSON>rud<PERSON><PERSON>, httpClient } from './base'
import type { User, Role, Permission, UserFormData, RoleFormData, PermissionFormData } from '../types/admin'
import type { CrudOperation } from '../types/admin'

// ==================== 用户管理API ====================

/**
 * 用户CRUD API
 */
export const userApi: CrudOperation<User> = createCrudApi<User>('users')

/**
 * 用户相关扩展API
 */
export const userExtendedApi = {
  /**
   * 重置用户密码
   */
  async resetPassword(userId: number, newPassword: string): Promise<boolean> {
    await httpClient.post(`/users/${userId}/reset-password`, { password: newPassword })
    return true
  },
  
  /**
   * 切换用户状态
   */
  async toggleStatus(userId: number, status: 0 | 1): Promise<boolean> {
    await httpClient.put(`/users/${userId}/status`, { status })
    return true
  },
  
  /**
   * 分配用户角色
   */
  async assignRoles(userId: number, roleIds: number[]): Promise<boolean> {
    await httpClient.post(`/users/${userId}/roles`, { roleIds })
    return true
  },
  
  /**
   * 获取用户角色
   */
  async getUserRoles(userId: number): Promise<Role[]> {
    return httpClient.get<Role[]>(`/users/${userId}/roles`)
  },
  
  /**
   * 检查用户名是否可用
   */
  async checkUsername(username: string, excludeId?: number): Promise<boolean> {
    const params = excludeId ? { username, excludeId } : { username }
    const result = await httpClient.get<{ available: boolean }>('/users/check-username', params)
    return result.available
  }
}

// ==================== 角色管理API ====================

/**
 * 角色CRUD API
 */
export const roleApi: CrudOperation<Role> = createCrudApi<Role>('roles')

/**
 * 角色相关扩展API
 */
export const roleExtendedApi = {
  /**
   * 分配角色权限
   */
  async assignPermissions(roleId: number, permissionIds: number[]): Promise<boolean> {
    await httpClient.post(`/roles/${roleId}/permissions`, { permissionIds })
    return true
  },
  
  /**
   * 获取角色权限
   */
  async getRolePermissions(roleId: number): Promise<Permission[]> {
    return httpClient.get<Permission[]>(`/roles/${roleId}/permissions`)
  },
  
  /**
   * 获取所有角色（用于下拉选择）
   */
  async getAllRoles(): Promise<Role[]> {
    return httpClient.get<Role[]>('/roles/all')
  },
  
  /**
   * 检查角色代码是否可用
   */
  async checkRoleCode(code: string, excludeId?: number): Promise<boolean> {
    const params = excludeId ? { code, excludeId } : { code }
    const result = await httpClient.get<{ available: boolean }>('/roles/check-code', params)
    return result.available
  }
}

// ==================== 权限管理API ====================

/**
 * 权限CRUD API
 */
export const permissionApi: CrudOperation<Permission> = createCrudApi<Permission>('permissions')

/**
 * 权限相关扩展API
 */
export const permissionExtendedApi = {
  /**
   * 获取权限树
   */
  async getPermissionTree(): Promise<Permission[]> {
    return httpClient.get<Permission[]>('/permissions/tree')
  },
  
  /**
   * 获取所有权限（用于权限分配）
   */
  async getAllPermissions(): Promise<Permission[]> {
    return httpClient.get<Permission[]>('/permissions/all')
  },
  
  /**
   * 检查权限代码是否可用
   */
  async checkPermissionCode(code: string, excludeId?: number): Promise<boolean> {
    const params = excludeId ? { code, excludeId } : { code }
    const result = await httpClient.get<{ available: boolean }>('/permissions/check-code', params)
    return result.available
  }
}

// ==================== 模拟数据（用于开发测试） ====================

/**
 * 模拟用户数据
 */
const mockUsers: User[] = [
  {
    id: 1,
    username: 'admin',
    nickname: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    avatar: '/static/images/avatar1.png',
    status: 1,
    roleIds: [1],
    roleNames: ['超级管理员'],
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    lastLoginTime: '2024-01-15 09:30:00'
  },
  {
    id: 2,
    username: 'user1',
    nickname: '普通用户1',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: '/static/images/avatar2.png',
    status: 1,
    roleIds: [2],
    roleNames: ['普通用户'],
    createTime: '2024-01-02 10:00:00',
    updateTime: '2024-01-02 10:00:00',
    lastLoginTime: '2024-01-14 15:20:00'
  },
  {
    id: 3,
    username: 'user2',
    nickname: '普通用户2',
    email: '<EMAIL>',
    phone: '13800138002',
    avatar: '/static/images/avatar3.png',
    status: 0,
    roleIds: [2],
    roleNames: ['普通用户'],
    createTime: '2024-01-03 10:00:00',
    updateTime: '2024-01-03 10:00:00',
    lastLoginTime: '2024-01-10 11:45:00'
  }
]

/**
 * 模拟角色数据
 */
const mockRoles: Role[] = [
  {
    id: 1,
    name: '超级管理员',
    code: 'super_admin',
    description: '拥有系统所有权限',
    permissionIds: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    permissionNames: ['用户管理', '角色管理', '权限管理', '系统监控', '系统工具'],
    status: 1,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    name: '普通用户',
    code: 'user',
    description: '普通用户权限',
    permissionIds: [1, 2],
    permissionNames: ['基础查看'],
    status: 1,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00'
  }
]

/**
 * 模拟权限数据
 */
const mockPermissions: Permission[] = [
  {
    id: 1,
    name: '系统管理',
    code: 'system',
    type: 'menu',
    path: '/admin/system',
    description: '系统管理模块',
    sort: 1,
    status: 1,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    children: [
      {
        id: 2,
        name: '用户管理',
        code: 'system:user',
        type: 'menu',
        parentId: 1,
        path: '/admin/system/user',
        description: '用户管理页面',
        sort: 1,
        status: 1,
        createTime: '2024-01-01 10:00:00',
        updateTime: '2024-01-01 10:00:00'
      },
      {
        id: 3,
        name: '角色管理',
        code: 'system:role',
        type: 'menu',
        parentId: 1,
        path: '/admin/system/role',
        description: '角色管理页面',
        sort: 2,
        status: 1,
        createTime: '2024-01-01 10:00:00',
        updateTime: '2024-01-01 10:00:00'
      },
      {
        id: 4,
        name: '权限管理',
        code: 'system:permission',
        type: 'menu',
        parentId: 1,
        path: '/admin/system/permission',
        description: '权限管理页面',
        sort: 3,
        status: 1,
        createTime: '2024-01-01 10:00:00',
        updateTime: '2024-01-01 10:00:00'
      }
    ]
  }
]

/**
 * 模拟用户API（用于开发测试）
 */
export const mockUserApi = createMockCrudApi<User>(mockUsers)

/**
 * 模拟角色API（用于开发测试）
 */
export const mockRoleApi = createMockCrudApi<Role>(mockRoles)

/**
 * 模拟权限API（用于开发测试）
 */
export const mockPermissionApi = createMockCrudApi<Permission>(mockPermissions)
