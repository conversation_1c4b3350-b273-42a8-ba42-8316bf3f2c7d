import type { AdminUser, MenuItem, BreadcrumbItem, DashboardData, SystemMetrics } from './admin'

// ==================== 管理员状态类型 ====================

/** 管理员状态 */
export interface AdminState {
  /** 当前用户 */
  currentUser: AdminUser | null
  /** 用户权限 */
  permissions: string[]
  /** 菜单是否折叠 */
  menuCollapsed: boolean
  /** 当前模块 */
  currentModule: string
  /** 面包屑导航 */
  breadcrumb: BreadcrumbItem[]
}

// ==================== UI状态类型 ====================

/** UI状态 */
export interface UIState {
  /** 全局加载状态 */
  loading: boolean
  /** 侧边栏是否折叠 */
  sidebarCollapsed: boolean
  /** 主题 */
  theme: 'light' | 'dark'
  /** 语言 */
  language: 'zh' | 'en'
}

// ==================== 数据缓存状态类型 ====================

/** 数据状态 */
export interface DataState {
  /** 仪表板数据 */
  dashboardData: DashboardData | null
  /** 用户列表 */
  userList: any[]
  /** 系统指标 */
  systemMetrics: SystemMetrics | null
  /** 最后更新时间 */
  lastUpdateTime: number
}

// ==================== Store Actions 类型 ====================

/** 管理员Store Actions */
export interface AdminActions {
  /** 登录 */
  login: (credentials: { username: string; password: string }) => Promise<void>
  /** 退出登录 */
  logout: () => Promise<void>
  /** 验证令牌 */
  validateToken: () => Promise<boolean>
  /** 刷新令牌 */
  refreshToken: () => Promise<void>
  /** 设置当前模块 */
  setCurrentModule: (module: string) => void
  /** 设置面包屑 */
  setBreadcrumb: (breadcrumb: BreadcrumbItem[]) => void
  /** 切换菜单折叠状态 */
  toggleMenuCollapsed: () => void
}

/** UI Store Actions */
export interface UIActions {
  /** 设置加载状态 */
  setLoading: (loading: boolean) => void
  /** 切换侧边栏 */
  toggleSidebar: () => void
  /** 设置主题 */
  setTheme: (theme: 'light' | 'dark') => void
  /** 设置语言 */
  setLanguage: (language: 'zh' | 'en') => void
}

/** 数据Store Actions */
export interface DataActions {
  /** 获取仪表板数据 */
  fetchDashboardData: () => Promise<void>
  /** 获取用户列表 */
  fetchUserList: (params?: any) => Promise<void>
  /** 获取系统指标 */
  fetchSystemMetrics: () => Promise<void>
  /** 清除缓存 */
  clearCache: () => void
}

// ==================== Store Getters 类型 ====================

/** 管理员Store Getters */
export interface AdminGetters {
  /** 是否已登录 */
  isLoggedIn: boolean
  /** 是否有权限 */
  hasPermission: (permission: string) => boolean
  /** 是否有角色 */
  hasRole: (role: string) => boolean
  /** 当前用户角色 */
  currentUserRole: string | null
}

/** UI Store Getters */
export interface UIGetters {
  /** 是否为暗色主题 */
  isDarkTheme: boolean
  /** 是否为中文 */
  isChineseLanguage: boolean
}

/** 数据Store Getters */
export interface DataGetters {
  /** 是否有仪表板数据 */
  hasDashboardData: boolean
  /** 用户总数 */
  totalUsers: number
  /** 活跃用户数 */
  activeUsers: number
}