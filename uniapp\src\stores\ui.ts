import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UIState, UIActions, UIGetters } from '@/pages/admin/types/store'

export const useUIStore = defineStore('ui', () => {
  // ==================== 状态 ====================
  
  /** 全局加载状态 */
  const loading = ref(false)
  
  /** 侧边栏是否折叠 */
  const sidebarCollapsed = ref(false)
  
  /** 主题 */
  const theme = ref<'light' | 'dark'>('light')
  
  /** 语言 */
  const language = ref<'zh' | 'en'>('zh')

  // ==================== 计算属性 ====================
  
  /** 是否为暗色主题 */
  const isDarkTheme = computed(() => theme.value === 'dark')
  
  /** 是否为中文 */
  const isChineseLanguage = computed(() => language.value === 'zh')

  // ==================== 方法 ====================
  
  /** 设置加载状态 */
  const setLoading = (value: boolean) => {
    loading.value = value
  }
  
  /** 切换侧边栏 */
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
  
  /** 设置侧边栏状态 */
  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
  }
  
  /** 设置主题 */
  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
  }
  
  /** 切换主题 */
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
  }
  
  /** 设置语言 */
  const setLanguage = (newLanguage: 'zh' | 'en') => {
    language.value = newLanguage
  }
  
  /** 切换语言 */
  const toggleLanguage = () => {
    language.value = language.value === 'zh' ? 'en' : 'zh'
  }

  return {
    // 状态
    loading,
    sidebarCollapsed,
    theme,
    language,
    
    // 计算属性
    isDarkTheme,
    isChineseLanguage,
    
    // 方法
    setLoading,
    toggleSidebar,
    setSidebarCollapsed,
    setTheme,
    toggleTheme,
    setLanguage,
    toggleLanguage
  }
}, {
  persist: {
    key: 'ui-store',
    storage: {
      getItem: (key: string) => uni.getStorageSync(key),
      setItem: (key: string, value: string) => uni.setStorageSync(key, value),
      removeItem: (key: string) => uni.removeStorageSync(key)
    },
    paths: ['sidebarCollapsed', 'theme', 'language']
  }
})