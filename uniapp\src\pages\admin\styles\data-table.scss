// 通用数据表格样式
.data-table {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .table-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .table-actions {
      display: flex;
      gap: 8px;

      .search-input {
        width: 200px;
        padding: 6px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 12px;

        &:focus {
          outline: none;
          border-color: #1890ff;
        }
      }

      .action-btn {
        padding: 6px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        color: #333;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;

          &:hover {
            background-color: #40a9ff;
          }
        }
      }
    }
  }

  .table-container {
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .table {
      width: 100%;
      border-collapse: collapse;

      .table-head {
        background-color: #fafafa;

        .table-row {
          .table-cell {
            padding: 12px 16px;
            text-align: left;
            font-size: 12px;
            font-weight: 500;
            color: #666;
            border-bottom: 1px solid #e8e8e8;

            &.sortable {
              cursor: pointer;
              user-select: none;

              &:hover {
                background-color: #f0f0f0;
              }

              .sort-icon {
                margin-left: 4px;
                font-size: 10px;
                color: #999;

                &.active {
                  color: #1890ff;
                }
              }
            }
          }
        }
      }

      .table-body {
        .table-row {
          transition: background-color 0.3s ease;

          &:hover {
            background-color: #f5f5f5;
          }

          &:nth-child(even) {
            background-color: #fafafa;
          }

          .table-cell {
            padding: 12px 16px;
            font-size: 12px;
            color: #333;
            border-bottom: 1px solid #e8e8e8;

            &.actions {
              .action-btn {
                padding: 4px 8px;
                margin-right: 4px;
                border: 1px solid #d9d9d9;
                border-radius: 2px;
                background-color: #fff;
                color: #666;
                font-size: 11px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                  border-color: #1890ff;
                  color: #1890ff;
                }

                &.danger {
                  border-color: #ff4d4f;
                  color: #ff4d4f;

                  &:hover {
                    background-color: #ff4d4f;
                    color: #fff;
                  }
                }
              }
            }

            .status-badge {
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 11px;
              font-weight: 500;

              &.active {
                background-color: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
              }

              &.inactive {
                background-color: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
              }

              &.pending {
                background-color: #fff7e6;
                color: #faad14;
                border: 1px solid #ffd591;
              }
            }
          }
        }
      }
    }

    .empty-state {
      padding: 40px;
      text-align: center;
      color: #999;
      font-size: 14px;

      .empty-icon {
        font-size: 48px;
        color: #d9d9d9;
        margin-bottom: 16px;
      }

      .empty-text {
        margin-bottom: 16px;
      }

      .empty-action {
        padding: 8px 16px;
        border: 1px solid #1890ff;
        border-radius: 4px;
        background-color: #fff;
        color: #1890ff;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background-color: #1890ff;
          color: #fff;
        }
      }
    }

    .loading-state {
      padding: 40px;
      text-align: center;
      color: #999;
      font-size: 14px;

      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid #f0f0f0;
        border-top: 3px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px;
      }
    }
  }

  .table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #fafafa;
    border-top: 1px solid #e8e8e8;

    .pagination-info {
      font-size: 12px;
      color: #666;
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: 8px;

      .page-size-selector {
        padding: 4px 8px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 12px;

        &:focus {
          outline: none;
          border-color: #1890ff;
        }
      }

      .pagination-btn {
        padding: 4px 8px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        color: #333;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          border-color: #1890ff;
          color: #1890ff;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &.active {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;
        }
      }

      .page-jump {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #666;

        .jump-input {
          width: 40px;
          padding: 4px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          text-align: center;
          font-size: 12px;

          &:focus {
            outline: none;
            border-color: #1890ff;
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 移动端响应式设计
@media (max-width: 480px) {
  .data-table {
    .table-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      padding: 12px;

      .table-title {
        font-size: 16px;
      }

      .table-actions {
        width: 100%;
        flex-direction: column;
        gap: 8px;

        .search-input {
          width: 100%;
          margin: 0;
        }

        .action-group {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          width: 100%;

          .action-btn {
            flex: 1;
            min-width: 80px;
            padding: 8px 12px;
            font-size: 12px;
          }
        }
      }
    }

    .table-container {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;

      .table {
        min-width: 500px;

        .table-head,
        .table-body {
          .table-row {
            .table-cell {
              padding: 6px 8px;
              font-size: 11px;
              min-width: 80px;

              &.actions {
                min-width: 100px;

                .action-btn {
                  padding: 4px 6px;
                  font-size: 10px;
                  margin: 1px;
                }
              }
            }
          }
        }
      }
    }

    .table-footer {
      flex-direction: column;
      gap: 8px;
      padding: 12px;

      .table-info {
        text-align: center;
        font-size: 12px;
      }

      .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
        gap: 4px;

        .pagination-btn {
          min-width: 32px;
          padding: 6px 8px;
          font-size: 11px;
        }

        .page-size-selector,
        .page-jump {
          margin: 4px 0;
        }
      }
    }
  }
}

// 平板响应式设计
@media (min-width: 481px) and (max-width: 768px) {
  .data-table {
    .table-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .table-actions {
        width: 100%;
        justify-content: space-between;

        .search-input {
          flex: 1;
          margin-right: 8px;
        }
      }
    }

    .table-container {
      overflow-x: auto;

      .table {
        min-width: 600px;

        .table-head,
        .table-body {
          .table-row {
            .table-cell {
              padding: 8px 12px;
              font-size: 12px;

              &.actions {
                .action-btn {
                  padding: 4px 8px;
                  font-size: 11px;
                }
              }
            }
          }
        }
      }
    }

    .table-footer {
      flex-direction: column;
      gap: 12px;

      .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
      }
    }
  }
}