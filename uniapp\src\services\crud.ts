import type { CrudOperation, QueryParams } from '@/types/global'
import type { PageResult } from '@/types/global'
import { request } from '@/utils/request'

/**
 * 创建通用CRUD API服务
 */
export function createCrudApi<T = any>(baseUrl: string): CrudOperation<T> {
  return {
    /**
     * 获取列表数据
     */
    async list(params: QueryParams): Promise<PageResult<T>> {
      const response = await request({
        url: baseUrl,
        method: 'GET',
        data: params
      })
      
      return {
        records: response.data?.records || response.data || [],
        total: response.data?.total || 0,
        current: response.data?.current || params.page || 1,
        size: response.data?.size || params.size || 10
      }
    },
    
    /**
     * 创建记录
     */
    async create(data: Partial<T>): Promise<T> {
      const response = await request({
        url: baseUrl,
        method: 'POST',
        data
      })
      
      return response.data
    },
    
    /**
     * 更新记录
     */
    async update(id: number, data: Partial<T>): Promise<T> {
      const response = await request({
        url: `${baseUrl}/${id}`,
        method: 'PUT',
        data
      })
      
      return response.data
    },
    
    /**
     * 删除记录
     */
    async delete(id: number): Promise<boolean> {
      await request({
        url: `${baseUrl}/${id}`,
        method: 'DELETE'
      })
      
      return true
    },
    
    /**
     * 获取单条记录
     */
    async get(id: number): Promise<T> {
      const response = await request({
        url: `${baseUrl}/${id}`,
        method: 'GET'
      })
      
      return response.data
    }
  }
}

/**
 * 管理员用户API
 */
export const adminUserApi = createCrudApi('/admin/users')

/**
 * 角色管理API
 */
export const roleApi = createCrudApi('/admin/roles')

/**
 * 权限管理API
 */
export const permissionApi = createCrudApi('/admin/permissions')

/**
 * 系统日志API
 */
export const systemLogApi = createCrudApi('/admin/logs')

/**
 * 系统配置API
 */
export const systemConfigApi = createCrudApi('/admin/configs')