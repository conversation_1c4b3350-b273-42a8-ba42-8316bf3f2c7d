import type { PageResult, PageParams } from '@/types/global'

// ==================== 用户认证相关类型 ====================

/** 管理员用户信息 */
export interface AdminUser {
  /** 用户ID */
  id: number
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname: string
  /** 角色 */
  role: string
  /** 权限列表 */
  permissions: string[]
  /** 最后登录时间 */
  lastLoginTime: string
  /** 用户状态 */
  status: number
  /** JWT令牌 */
  token: string
}

/** 登录凭证 */
export interface LoginCredentials {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
}

// ==================== 导航菜单相关类型 ====================

/** 菜单项 */
export interface MenuItem {
  /** 菜单ID */
  id: string
  /** 菜单标题 */
  title: string
  /** 菜单图标 */
  icon: string
  /** 路由路径 */
  path: string
  /** 子菜单 */
  children?: MenuItem[]
  /** 所需权限 */
  permission?: string
  /** 徽章数量 */
  badge?: number
}

/** 面包屑项 */
export interface BreadcrumbItem {
  /** 标题 */
  title: string
  /** 路径 */
  path?: string
}

// ==================== BI仪表板相关类型 ====================

/** 仪表板指标 */
export interface DashboardMetrics {
  /** 总用户数 */
  totalUsers: number
  /** 活跃用户数 */
  activeUsers: number
  /** 系统负载 */
  systemLoad: number
  /** 内存使用率 */
  memoryUsage: number
  /** 磁盘使用率 */
  diskUsage: number
  /** 错误率 */
  errorRate: number
  /** 响应时间 */
  responseTime: number
}

/** 图表选项 */
export interface ChartOptions {
  /** 图表标题 */
  title?: string
  /** X轴配置 */
  xAxis?: any
  /** Y轴配置 */
  yAxis?: any
  /** 其他配置 */
  [key: string]: any
}

/** 图表数据 */
export interface ChartData {
  /** 图表类型 */
  type: 'line' | 'bar' | 'pie' | 'gauge'
  /** 图表标题 */
  title: string
  /** 图表数据 */
  data: any[]
  /** 图表选项 */
  options: ChartOptions
}

/** 活动记录 */
export interface Activity {
  /** 活动ID */
  id: number
  /** 活动类型 */
  type: string
  /** 活动描述 */
  description: string
  /** 用户信息 */
  user: string
  /** 创建时间 */
  createTime: string
}

/** 仪表板数据 */
export interface DashboardData {
  /** 用户统计 */
  userStats: DashboardMetrics
  /** 系统指标 */
  systemMetrics: DashboardMetrics
  /** 业务分析 */
  businessAnalytics: DashboardMetrics
  /** 最近活动 */
  recentActivities: Activity[]
}

// ==================== 数据表格相关类型 ====================

/** 表格列配置 */
export interface TableColumn {
  /** 列键 */
  key: string
  /** 列标题 */
  title: string
  /** 列宽度 */
  width?: string | number
  /** 是否可排序 */
  sortable?: boolean
  /** 自定义渲染 */
  render?: (value: any, record: any) => string
}

/** 表格操作 */
export interface TableAction {
  /** 操作类型 */
  type: 'add' | 'edit' | 'delete' | 'view'
  /** 操作标签 */
  label: string
  /** 操作处理函数 */
  handler: (item?: any) => void
  /** 所需权限 */
  permission?: string
}

/** 分页配置 */
export interface PaginationConfig {
  /** 当前页 */
  current: number
  /** 每页大小 */
  pageSize: number
  /** 总数 */
  total: number
  /** 显示快速跳转 */
  showQuickJumper?: boolean
  /** 显示每页大小选择器 */
  showSizeChanger?: boolean
}

/** 数据表格属性 */
export interface DataTableProps<T = any> {
  /** 表格数据 */
  data: T[]
  /** 列配置 */
  columns: TableColumn[]
  /** 加载状态 */
  loading: boolean
  /** 分页配置 */
  pagination: PaginationConfig
  /** 是否可搜索 */
  searchable: boolean
  /** 是否可排序 */
  sortable: boolean
  /** 操作按钮 */
  actions: TableAction[]
}

// ==================== 数据表单相关类型 ====================

/** 表单字段类型 */
export type FormFieldType = 'text' | 'number' | 'email' | 'password' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'datetime' | 'switch' | 'file' | 'color' | 'range'

/** 表单字段选项 */
export interface FormFieldOption {
  /** 选项标签 */
  label: string
  /** 选项值 */
  value: any
}

/** 表单字段 */
export interface FormField {
  /** 字段键 */
  key: string
  /** 字段标签 */
  label: string
  /** 字段类型 */
  type: FormFieldType
  /** 是否必填 */
  required?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 占位符 */
  placeholder?: string
  /** 字段选项（用于select、radio、checkbox） */
  options?: FormFieldOption[]
  /** 验证规则 */
  rules?: ValidationRule[]
  /** 帮助文本 */
  help?: string
  /** 文件接受类型（用于file类型） */
  accept?: string
  /** 是否支持多选（用于file类型） */
  multiple?: boolean
  /** 最小值（用于number、range类型） */
  min?: number
  /** 最大值（用于number、range类型） */
  max?: number
  /** 步长（用于number、range类型） */
  step?: number
  /** 条件显示函数 */
  condition?: (formData: Record<string, any>) => boolean
  /** 字段分组 */
  section?: string
}

/** 验证规则 */
export interface ValidationRule {
  /** 规则类型 */
  type: 'required' | 'email' | 'min' | 'max' | 'pattern'
  /** 规则值 */
  value?: any
  /** 错误消息 */
  message: string
}

/** 验证规则集合 */
export interface ValidationRules {
  [key: string]: ValidationRule[]
}

/** 数据表单属性 */
export interface DataFormProps {
  /** 表单字段 */
  fields: FormField[]
  /** 表单数据 */
  data: Record<string, any>
  /** 表单模式 */
  mode: 'create' | 'edit' | 'view'
  /** 验证规则 */
  validation: ValidationRules
}

// ==================== 系统监控相关类型 ====================

/** 服务器状态 */
export interface ServerStatus {
  /** CPU使用率 */
  cpuUsage: number
  /** 内存使用率 */
  memoryUsage: number
  /** 磁盘使用率 */
  diskUsage: number
  /** 网络状态 */
  networkStatus: 'online' | 'offline'
  /** 运行时间 */
  uptime: number
}

/** 数据库指标 */
export interface DatabaseMetrics {
  /** 连接数 */
  connections: number
  /** 查询性能 */
  queryPerformance: number
  /** 存储使用 */
  storageUsage: number
  /** 响应时间 */
  responseTime: number
}

/** 健康状态 */
export interface HealthStatus {
  /** 状态 */
  status: 'healthy' | 'warning' | 'error'
  /** 消息 */
  message: string
  /** 检查时间 */
  checkTime: string
}

/** 系统告警 */
export interface Alert {
  /** 告警ID */
  id: number
  /** 告警级别 */
  level: 'info' | 'warning' | 'error' | 'critical'
  /** 告警标题 */
  title: string
  /** 告警描述 */
  description: string
  /** 创建时间 */
  createTime: string
  /** 是否已读 */
  isRead: boolean
}

/** 监控数据 */
export interface MonitorData {
  /** 服务器状态 */
  serverStatus: ServerStatus
  /** 数据库指标 */
  databaseMetrics: DatabaseMetrics
  /** 应用健康状况 */
  applicationHealth: HealthStatus
  /** 告警列表 */
  alerts: Alert[]
}

// ==================== CRUD操作相关类型 ====================

/** 查询参数 */
export interface QueryParams extends PageParams {
  /** 搜索关键词 */
  keyword?: string
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
  /** 其他过滤条件 */
  [key: string]: any
}

/** CRUD操作接口 */
export interface CrudOperation<T> {
  /** 列表查询 */
  list: (params: QueryParams) => Promise<PageResult<T>>
  /** 创建记录 */
  create: (data: Partial<T>) => Promise<T>
  /** 更新记录 */
  update: (id: number, data: Partial<T>) => Promise<T>
  /** 删除记录 */
  delete: (id: number) => Promise<boolean>
  /** 获取单条记录 */
  get: (id: number) => Promise<T>
}

// ==================== 路由权限相关类型 ====================

/** 路由权限 */
export interface RoutePermission {
  /** 路由路径 */
  path: string
  /** 所需权限 */
  requiredPermissions: string[]
  /** 所需角色 */
  roles: string[]
}

// ==================== 用户管理相关类型 ====================

/** 用户信息 */
export interface User {
  /** 用户ID */
  id: number
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname: string
  /** 邮箱 */
  email?: string
  /** 手机号 */
  phone?: string
  /** 头像 */
  avatar?: string
  /** 用户状态 */
  status: 0 | 1 // 0-禁用 1-启用
  /** 角色ID列表 */
  roleIds: number[]
  /** 角色名称列表 */
  roleNames: string[]
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 最后登录时间 */
  lastLoginTime?: string
}

/** 角色信息 */
export interface Role {
  /** 角色ID */
  id: number
  /** 角色名称 */
  name: string
  /** 角色代码 */
  code: string
  /** 角色描述 */
  description?: string
  /** 权限ID列表 */
  permissionIds: number[]
  /** 权限名称列表 */
  permissionNames: string[]
  /** 角色状态 */
  status: 0 | 1 // 0-禁用 1-启用
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
}

/** 权限信息 */
export interface Permission {
  /** 权限ID */
  id: number
  /** 权限名称 */
  name: string
  /** 权限代码 */
  code: string
  /** 权限类型 */
  type: 'menu' | 'button' | 'api'
  /** 父权限ID */
  parentId?: number
  /** 权限路径 */
  path?: string
  /** 权限描述 */
  description?: string
  /** 排序 */
  sort: number
  /** 权限状态 */
  status: 0 | 1 // 0-禁用 1-启用
  /** 子权限 */
  children?: Permission[]
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
}

/** 用户表单数据 */
export interface UserFormData {
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname: string
  /** 邮箱 */
  email?: string
  /** 手机号 */
  phone?: string
  /** 密码（创建时必填） */
  password?: string
  /** 确认密码 */
  confirmPassword?: string
  /** 头像 */
  avatar?: string
  /** 用户状态 */
  status: 0 | 1
  /** 角色ID列表 */
  roleIds: number[]
}

/** 角色表单数据 */
export interface RoleFormData {
  /** 角色名称 */
  name: string
  /** 角色代码 */
  code: string
  /** 角色描述 */
  description?: string
  /** 权限ID列表 */
  permissionIds: number[]
  /** 角色状态 */
  status: 0 | 1
}

/** 权限表单数据 */
export interface PermissionFormData {
  /** 权限名称 */
  name: string
  /** 权限代码 */
  code: string
  /** 权限类型 */
  type: 'menu' | 'button' | 'api'
  /** 父权限ID */
  parentId?: number
  /** 权限路径 */
  path?: string
  /** 权限描述 */
  description?: string
  /** 排序 */
  sort: number
  /** 权限状态 */
  status: 0 | 1
}

// ==================== 系统工具相关类型 ====================

/** 备份工具 */
export interface BackupTool {
  /** 工具名称 */
  name: string
  /** 工具描述 */
  description: string
  /** 执行函数 */
  execute: () => Promise<boolean>
}

/** 日志查看器工具 */
export interface LogViewerTool {
  /** 工具名称 */
  name: string
  /** 工具描述 */
  description: string
  /** 获取日志 */
  getLogs: (params: LogQueryParams) => Promise<LogEntry[]>
}

/** 缓存管理工具 */
export interface CacheManagementTool {
  /** 工具名称 */
  name: string
  /** 工具描述 */
  description: string
  /** 清理缓存 */
  clearCache: (type?: string) => Promise<boolean>
}

/** 清理工具 */
export interface CleanupTool {
  /** 工具名称 */
  name: string
  /** 工具描述 */
  description: string
  /** 执行清理 */
  cleanup: () => Promise<boolean>
}

/** 系统工具集合 */
export interface SystemTools {
  /** 数据库备份 */
  databaseBackup: BackupTool
  /** 日志查看器 */
  logViewer: LogViewerTool
  /** 缓存管理 */
  cacheManagement: CacheManagementTool
  /** 系统清理 */
  systemCleanup: CleanupTool
}

/** 日志查询参数 */
export interface LogQueryParams extends PageParams {
  /** 日志级别 */
  level?: 'debug' | 'info' | 'warn' | 'error'
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
  /** 关键词 */
  keyword?: string
}

/** 日志条目 */
export interface LogEntry {
  /** 日志ID */
  id: number
  /** 日志级别 */
  level: 'debug' | 'info' | 'warn' | 'error'
  /** 日志消息 */
  message: string
  /** 时间戳 */
  timestamp: string
  /** 来源 */
  source: string
}