<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import UserManagement from '../components/system/UserManagement.vue'
import RoleManagement from '../components/system/RoleManagement.vue'
import PermissionManagement from '../components/system/PermissionManagement.vue'
import PartnerManagement from '../components/system/PartnerManagement.vue'
import CreateChatManagement from '../components/system/CreateChatManagement.vue'

// 响应式数据
const activeTab = ref('user')

// 快速操作配置
const quickActions = ref([
  {
    key: 'addUser',
    label: '添加用户',
    icon: '➕',
    action: () => switchTab('user')
  },
  {
    key: 'addRole',
    label: '添加角色',
    icon: '🔑',
    action: () => switchTab('role')
  },
  {
    key: 'systemSettings',
    label: '系统设置',
    icon: '⚙️',
    action: () => handleSystemSettings()
  },
  {
    key: 'dataExport',
    label: '数据导出',
    icon: '📤',
    action: () => handleDataExport()
  }
])

// 计算属性
const visibleTabs = computed(() => {
  return functionTabs.value.filter(tab => hasPermission(tab.permission))
})

// 方法
const hasPermission = (permission?: string): boolean => {
  if (!permission) return true
  // 简化权限检查，实际项目中应该从用户权限store中获取
  return true
}

const switchTab = (tabKey: string) => {
  if (hasPermission(functionTabs.value.find(tab => tab.key === tabKey)?.permission)) {
    activeTab.value = tabKey
  }
}

const handleQuickAction = (action: any) => {
  if (action.action && typeof action.action === 'function') {
    action.action()
  }
}

const handleSystemSettings = () => {
  uni.showToast({
    title: '系统设置功能开发中',
    icon: 'none'
  })
}

const handleDataExport = () => {
  uni.showToast({
    title: '数据导出功能开发中',
    icon: 'none'
  })
}

// 生命周期
onMounted(() => {
  // 检查URL参数，设置默认标签页
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.tab && visibleTabs.value.some(t => t.key === options.tab)) {
    activeTab.value = options.tab
  }
})
</script>

<template>
  <view class="system-function-panel">

    <!-- 功能内容区域 -->
    <view class="function-content">
      <!-- 用户管理 -->
      <view v-if="activeTab === 'user'" class="content-section">
        <UserManagement />
      </view>

      <!-- 角色管理 -->
      <view v-if="activeTab === 'role'" class="content-section">
        <RoleManagement />
      </view>

      <!-- 权限管理 -->
      <view v-if="activeTab === 'permission'" class="content-section">
        <PermissionManagement />
      </view>

      <!-- 伴侣管理 -->
      <view v-if="activeTab === 'partner'" class="content-section">
        <PartnerManagement />
      </view>

      <!-- 初始聊天管理 -->
      <view v-if="activeTab === 'createChat'" class="content-section">
        <CreateChatManagement />
      </view>
    </view>

    <!-- 快速操作面板 -->
    <view class="quick-actions">
      <view class="actions-title">快速操作</view>
      <view class="actions-grid">
        <view
          v-for="action in quickActions"
          :key="action.key"
          class="action-item"
          @click="handleQuickAction(action)"
        >
          <view class="action-icon">{{ action.icon }}</view>
          <view class="action-label">{{ action.label }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import '../styles/system-function-panel.scss';
</style>