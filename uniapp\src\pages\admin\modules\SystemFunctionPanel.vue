<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'
import UserManagement from '../components/system/UserManagement.vue'
import RoleManagement from '../components/system/RoleManagement.vue'
import PermissionManagement from '../components/system/PermissionManagement.vue'
import PartnerManagement from '../components/system/PartnerManagement.vue'
import CreateChatManagement from '../components/system/CreateChatManagement.vue'

// Store
const dashboardStore = useDashboardStore()

// 响应式数据
const activeTab = ref('user')

// 功能标签页配置
const functionTabs = ref([
  {
    key: 'user',
    label: '用户管理',
    icon: '👥',
    permission: 'user:manage'
  },
  {
    key: 'role',
    label: '角色管理',
    icon: '🔐',
    permission: 'role:manage'
  },
  {
    key: 'partner',
    label: '伴侣管理',
    icon: '💝',
    permission: 'partner:manage'
  },
  {
    key: 'createChat',
    label: '初始聊天管理',
    icon: '💬',
    permission: 'createChat:manage'
  },
  {
    key: 'permission',
    label: '权限管理',
    icon: '🛡️',
    permission: 'permission:manage'
  }
])

// 快速操作配置
const quickActions = ref([
  {
    key: 'addUser',
    label: '添加用户',
    icon: '➕',
    action: () => switchTab('user')
  },
  {
    key: 'addRole',
    label: '添加角色',
    icon: '🔑',
    action: () => switchTab('role')
  },
  {
    key: 'systemSettings',
    label: '系统设置',
    icon: '⚙️',
    action: () => handleSystemSettings()
  },
  {
    key: 'dataExport',
    label: '数据导出',
    icon: '📤',
    action: () => handleDataExport()
  }
])

// 计算属性
const visibleTabs = computed(() => {
  return functionTabs.value.filter(tab => hasPermission(tab.permission))
})

// 方法
const hasPermission = (permission?: string): boolean => {
  if (!permission) return true
  // 简化权限检查，实际项目中应该从用户权限store中获取
  return true
}

const switchTab = (tabKey: string) => {
  if (hasPermission(functionTabs.value.find(tab => tab.key === tabKey)?.permission)) {
    activeTab.value = tabKey
  }
}

const handleQuickAction = (action: any) => {
  if (action.action && typeof action.action === 'function') {
    action.action()
  }
}

const handleSystemSettings = () => {
  uni.showToast({
    title: '系统设置功能开发中',
    icon: 'none'
  })
}

const handleDataExport = () => {
  uni.showToast({
    title: '数据导出功能开发中',
    icon: 'none'
  })
}

// 根据路由路径设置活动标签页
const setActiveTabFromRoute = (routePath: string) => {
  // 从路由路径中提取子模块名称
  const pathParts = routePath.split('/')
  if (pathParts.length >= 2 && pathParts[0] === 'system-function') {
    const subModule = pathParts[1]

    // 映射路由到标签页
    const routeToTabMap: Record<string, string> = {
      'user-management': 'user',
      'role-management': 'role',
      'partner-management': 'partner',
      'createChat-management': 'createChat',
      'permission-management': 'permission'
    }

    const tabKey = routeToTabMap[subModule]
    if (tabKey && visibleTabs.value.some(t => t.key === tabKey)) {
      activeTab.value = tabKey
    }
  }
}

// 监听路由变化
const handleRouteChange = (routePath: string) => {
  console.log('SystemFunctionPanel: 收到路由变化', routePath)
  setActiveTabFromRoute(routePath)
}

// 监听DashboardStore的currentModule变化
watch(() => dashboardStore.currentModule, (newModule) => {
  console.log('SystemFunctionPanel: DashboardStore模块变化', newModule)
  if (newModule && newModule.startsWith('system-function/')) {
    setActiveTabFromRoute(newModule)
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  // 监听路由变化事件
  uni.$on('admin-route-change', handleRouteChange)

  // 检查当前路由，设置默认标签页
  const currentModule = dashboardStore.currentModule
  if (currentModule && currentModule.startsWith('system-function/')) {
    setActiveTabFromRoute(currentModule)
  } else {
    // 设置默认标签页
    activeTab.value = 'user'
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  uni.$off('admin-route-change', handleRouteChange)
})
</script>

<template>
  <view class="system-function-panel">
    <!-- 标签页导航 -->
    <view class="tab-navigation">
      <view class="tab-header">
        <view class="tab-title">系统功能管理</view>
        <view class="tab-description">管理系统的用户、角色、权限等功能模块</view>
      </view>

      <view class="tab-list">
        <view
          v-for="tab in visibleTabs"
          :key="tab.key"
          class="tab-item"
          :class="{ active: activeTab === tab.key }"
          @click="switchTab(tab.key)"
        >
          <view class="tab-icon">{{ tab.icon }}</view>
          <view class="tab-label">{{ tab.label }}</view>
        </view>
      </view>
    </view>

    <!-- 功能内容区域 -->
    <view class="function-content">
      <!-- 用户管理 -->
      <view v-if="activeTab === 'user'" class="content-section">
        <UserManagement />
      </view>

      <!-- 角色管理 -->
      <view v-if="activeTab === 'role'" class="content-section">
        <RoleManagement />
      </view>

      <!-- 权限管理 -->
      <view v-if="activeTab === 'permission'" class="content-section">
        <PermissionManagement />
      </view>

      <!-- 伴侣管理 -->
      <view v-if="activeTab === 'partner'" class="content-section">
        <PartnerManagement />
      </view>

      <!-- 初始聊天管理 -->
      <view v-if="activeTab === 'createChat'" class="content-section">
        <CreateChatManagement />
      </view>
    </view>

    <!-- 快速操作面板 -->
    <view class="quick-actions">
      <view class="actions-title">快速操作</view>
      <view class="actions-grid">
        <view
          v-for="action in quickActions"
          :key="action.key"
          class="action-item"
          @click="handleQuickAction(action)"
        >
          <view class="action-icon">{{ action.icon }}</view>
          <view class="action-label">{{ action.label }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import '../styles/system-function-panel.scss';

.system-function-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;

  .tab-navigation {
    border-bottom: 1px solid #e8e8e8;
    background-color: #fafafa;

    .tab-header {
      padding: 16px 24px;
      border-bottom: 1px solid #e8e8e8;

      .tab-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .tab-description {
        font-size: 14px;
        color: #666;
      }
    }

    .tab-list {
      display: flex;
      padding: 0 24px;
      overflow-x: auto;

      .tab-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        margin-right: 8px;
        border-radius: 6px 6px 0 0;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
        position: relative;

        &:hover {
          background-color: #f0f0f0;
        }

        &.active {
          background-color: #fff;
          color: #1890ff;
          border-bottom: 2px solid #1890ff;

          .tab-icon {
            transform: scale(1.1);
          }
        }

        .tab-icon {
          font-size: 16px;
          transition: transform 0.3s ease;
        }

        .tab-label {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }

  .function-content {
    flex: 1;
    overflow: hidden;

    .content-section {
      height: 100%;
      padding: 16px;
    }
  }

  .quick-actions {
    padding: 16px 24px;
    border-top: 1px solid #e8e8e8;
    background-color: #fafafa;

    .actions-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 12px;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 6px;
        padding: 12px;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        background-color: #fff;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
        }

        .action-icon {
          font-size: 20px;
        }

        .action-label {
          font-size: 12px;
          color: #666;
          text-align: center;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .system-function-panel {
    .tab-navigation {
      .tab-header {
        padding: 12px 16px;

        .tab-title {
          font-size: 16px;
        }

        .tab-description {
          font-size: 12px;
        }
      }

      .tab-list {
        padding: 0 16px;

        .tab-item {
          padding: 10px 12px;
          margin-right: 4px;

          .tab-icon {
            font-size: 14px;
          }

          .tab-label {
            font-size: 12px;
          }
        }
      }
    }

    .function-content {
      .content-section {
        padding: 12px;
      }
    }

    .quick-actions {
      padding: 12px 16px;

      .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 8px;

        .action-item {
          padding: 8px;

          .action-icon {
            font-size: 16px;
          }

          .action-label {
            font-size: 11px;
          }
        }
      }
    }
  }
}
</style>