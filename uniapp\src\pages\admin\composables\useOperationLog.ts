import { ref } from 'vue'
import { httpClient } from '../api/base'
import { usePermission } from './usePermission'

/**
 * 操作日志类型
 */
export interface OperationLog {
  /** 日志ID */
  id: number
  /** 操作用户ID */
  userId: number
  /** 操作用户名 */
  username: string
  /** 操作类型 */
  operation: string
  /** 操作模块 */
  module: string
  /** 操作描述 */
  description: string
  /** 操作对象ID */
  targetId?: number
  /** 操作对象类型 */
  targetType?: string
  /** 操作前数据 */
  beforeData?: string
  /** 操作后数据 */
  afterData?: string
  /** IP地址 */
  ipAddress: string
  /** 用户代理 */
  userAgent: string
  /** 操作结果 */
  result: 'success' | 'failure'
  /** 错误信息 */
  errorMessage?: string
  /** 操作时间 */
  createTime: string
}

/**
 * 操作日志参数
 */
export interface LogOperationParams {
  /** 操作类型 */
  operation: string
  /** 操作模块 */
  module: string
  /** 操作描述 */
  description: string
  /** 操作对象ID */
  targetId?: number
  /** 操作对象类型 */
  targetType?: string
  /** 操作前数据 */
  beforeData?: any
  /** 操作后数据 */
  afterData?: any
  /** 操作结果 */
  result?: 'success' | 'failure'
  /** 错误信息 */
  errorMessage?: string
}

/**
 * 操作日志管理composable
 */
export function useOperationLog() {
  const { currentUser } = usePermission()
  
  // 日志队列（用于批量提交）
  const logQueue = ref<LogOperationParams[]>([])
  
  // 是否启用日志记录
  const loggingEnabled = ref(true)
  
  /**
   * 记录操作日志
   */
  const logOperation = async (params: LogOperationParams): Promise<void> => {
    if (!loggingEnabled.value || !currentUser.value) {
      return
    }
    
    try {
      const logData = {
        ...params,
        userId: currentUser.value.id,
        username: currentUser.value.username,
        beforeData: params.beforeData ? JSON.stringify(params.beforeData) : undefined,
        afterData: params.afterData ? JSON.stringify(params.afterData) : undefined,
        ipAddress: await getClientIP(),
        userAgent: getUserAgent(),
        result: params.result || 'success',
        createTime: new Date().toISOString()
      }
      
      // 发送日志到服务器
      await httpClient.post('/operation-logs', logData)
    } catch (error) {
      console.error('Log operation error:', error)
      // 日志记录失败不应该影响主要业务流程
    }
  }
  
  /**
   * 批量记录操作日志
   */
  const logBatchOperations = async (operations: LogOperationParams[]): Promise<void> => {
    if (!loggingEnabled.value || !currentUser.value) {
      return
    }
    
    try {
      const logDataList = operations.map(params => ({
        ...params,
        userId: currentUser.value!.id,
        username: currentUser.value!.username,
        beforeData: params.beforeData ? JSON.stringify(params.beforeData) : undefined,
        afterData: params.afterData ? JSON.stringify(params.afterData) : undefined,
        ipAddress: '', // 批量操作时可能无法获取准确IP
        userAgent: getUserAgent(),
        result: params.result || 'success',
        createTime: new Date().toISOString()
      }))
      
      await httpClient.post('/operation-logs/batch', { logs: logDataList })
    } catch (error) {
      console.error('Log batch operations error:', error)
    }
  }
  
  /**
   * 添加日志到队列
   */
  const queueLog = (params: LogOperationParams): void => {
    logQueue.value.push(params)
  }
  
  /**
   * 提交队列中的日志
   */
  const flushLogQueue = async (): Promise<void> => {
    if (logQueue.value.length === 0) {
      return
    }
    
    const logs = [...logQueue.value]
    logQueue.value = []
    
    await logBatchOperations(logs)
  }
  
  /**
   * 记录用户操作日志
   */
  const logUserOperation = async (
    operation: string,
    targetId?: number,
    beforeData?: any,
    afterData?: any
  ): Promise<void> => {
    await logOperation({
      operation,
      module: 'user',
      description: getUserOperationDescription(operation, targetId),
      targetId,
      targetType: 'user',
      beforeData,
      afterData
    })
  }
  
  /**
   * 记录角色操作日志
   */
  const logRoleOperation = async (
    operation: string,
    targetId?: number,
    beforeData?: any,
    afterData?: any
  ): Promise<void> => {
    await logOperation({
      operation,
      module: 'role',
      description: getRoleOperationDescription(operation, targetId),
      targetId,
      targetType: 'role',
      beforeData,
      afterData
    })
  }
  
  /**
   * 记录权限操作日志
   */
  const logPermissionOperation = async (
    operation: string,
    targetId?: number,
    beforeData?: any,
    afterData?: any
  ): Promise<void> => {
    await logOperation({
      operation,
      module: 'permission',
      description: getPermissionOperationDescription(operation, targetId),
      targetId,
      targetType: 'permission',
      beforeData,
      afterData
    })
  }
  
  /**
   * 记录登录日志
   */
  const logLoginOperation = async (result: 'success' | 'failure', errorMessage?: string): Promise<void> => {
    await logOperation({
      operation: 'login',
      module: 'auth',
      description: result === 'success' ? '用户登录成功' : '用户登录失败',
      result,
      errorMessage
    })
  }
  
  /**
   * 记录登出日志
   */
  const logLogoutOperation = async (): Promise<void> => {
    await logOperation({
      operation: 'logout',
      module: 'auth',
      description: '用户登出'
    })
  }
  
  /**
   * 获取操作日志列表
   */
  const getOperationLogs = async (params: {
    page?: number
    pageSize?: number
    module?: string
    operation?: string
    userId?: number
    startTime?: string
    endTime?: string
  } = {}): Promise<{ items: OperationLog[], total: number }> => {
    try {
      const response = await httpClient.get('/operation-logs', params)
      return response
    } catch (error) {
      console.error('Get operation logs error:', error)
      return { items: [], total: 0 }
    }
  }
  
  /**
   * 导出操作日志
   */
  const exportOperationLogs = async (params: {
    module?: string
    operation?: string
    userId?: number
    startTime?: string
    endTime?: string
  } = {}): Promise<void> => {
    try {
      // 这里应该调用导出API
      await httpClient.post('/operation-logs/export', params)
      
      uni.showToast({
        title: '导出任务已提交',
        icon: 'success'
      })
    } catch (error) {
      console.error('Export operation logs error:', error)
      uni.showToast({
        title: '导出失败',
        icon: 'error'
      })
    }
  }
  
  /**
   * 清理过期日志
   */
  const cleanupExpiredLogs = async (days: number = 90): Promise<void> => {
    try {
      await httpClient.delete('/operation-logs/cleanup', { days })
      
      uni.showToast({
        title: '清理任务已提交',
        icon: 'success'
      })
    } catch (error) {
      console.error('Cleanup expired logs error:', error)
      uni.showToast({
        title: '清理失败',
        icon: 'error'
      })
    }
  }
  
  // 辅助函数
  function getUserOperationDescription(operation: string, targetId?: number): string {
    const descriptions: Record<string, string> = {
      create: `创建用户${targetId ? ` (ID: ${targetId})` : ''}`,
      update: `更新用户${targetId ? ` (ID: ${targetId})` : ''}`,
      delete: `删除用户${targetId ? ` (ID: ${targetId})` : ''}`,
      'reset-password': `重置用户密码${targetId ? ` (ID: ${targetId})` : ''}`,
      'toggle-status': `切换用户状态${targetId ? ` (ID: ${targetId})` : ''}`,
      'assign-roles': `分配用户角色${targetId ? ` (ID: ${targetId})` : ''}`
    }
    return descriptions[operation] || `用户操作: ${operation}`
  }
  
  function getRoleOperationDescription(operation: string, targetId?: number): string {
    const descriptions: Record<string, string> = {
      create: `创建角色${targetId ? ` (ID: ${targetId})` : ''}`,
      update: `更新角色${targetId ? ` (ID: ${targetId})` : ''}`,
      delete: `删除角色${targetId ? ` (ID: ${targetId})` : ''}`,
      'assign-permissions': `分配角色权限${targetId ? ` (ID: ${targetId})` : ''}`
    }
    return descriptions[operation] || `角色操作: ${operation}`
  }
  
  function getPermissionOperationDescription(operation: string, targetId?: number): string {
    const descriptions: Record<string, string> = {
      create: `创建权限${targetId ? ` (ID: ${targetId})` : ''}`,
      update: `更新权限${targetId ? ` (ID: ${targetId})` : ''}`,
      delete: `删除权限${targetId ? ` (ID: ${targetId})` : ''}`
    }
    return descriptions[operation] || `权限操作: ${operation}`
  }
  
  async function getClientIP(): Promise<string> {
    try {
      // 在实际应用中，可能需要调用API获取客户端IP
      // 这里返回一个占位符
      return '127.0.0.1'
    } catch (error) {
      return 'unknown'
    }
  }
  
  function getUserAgent(): string {
    try {
      // 在uni-app中获取用户代理信息
      const systemInfo = uni.getSystemInfoSync()
      return `${systemInfo.platform} ${systemInfo.system} ${systemInfo.version}`
    } catch (error) {
      return 'unknown'
    }
  }
  
  return {
    // 状态
    logQueue,
    loggingEnabled,
    
    // 方法
    logOperation,
    logBatchOperations,
    queueLog,
    flushLogQueue,
    logUserOperation,
    logRoleOperation,
    logPermissionOperation,
    logLoginOperation,
    logLogoutOperation,
    getOperationLogs,
    exportOperationLogs,
    cleanupExpiredLogs
  }
}

/**
 * 操作日志装饰器
 */
export function withOperationLog(
  operation: string,
  module: string,
  description?: string
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const { logOperation } = useOperationLog()
      
      try {
        const result = await originalMethod.apply(this, args)
        
        // 记录成功日志
        await logOperation({
          operation,
          module,
          description: description || `${module} ${operation} 操作`,
          result: 'success'
        })
        
        return result
      } catch (error) {
        // 记录失败日志
        await logOperation({
          operation,
          module,
          description: description || `${module} ${operation} 操作`,
          result: 'failure',
          errorMessage: error instanceof Error ? error.message : String(error)
        })
        
        throw error
      }
    }
    
    return descriptor
  }
}
