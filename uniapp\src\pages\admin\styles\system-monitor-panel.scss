// 系统监控面板样式
.system-monitor-panel {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  // 页面标题
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    background-color: #fff;
    padding: 16px 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      display: flex;
      align-items: center;
      gap: 12px;

      .title-icon {
        font-size: 24px;
      }

      .title-text {
        font-size: 20px;
        font-weight: 600;
        color: #333;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .refresh-btn, .auto-refresh-toggle {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background-color: #fff;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.loading {
          .refresh-icon {
            animation: spin 1s linear infinite;
          }
        }
      }
    }
  }

  // 系统概览卡片
  .overview-cards {
    margin-bottom: 24px;

    .card-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
    }

    .metric-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        .card-icon {
          font-size: 20px;
        }

        .card-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }

      .card-content {
        .metric-value {
          font-size: 28px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }

        .status-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;

          .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #52c41a;
          }

          .status-text {
            font-size: 14px;
            color: #666;
          }
        }

        .uptime, .memory-details, .disk-details {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #999;
          margin-bottom: 8px;
        }

        .metric-chart {
          .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;

            .progress-fill {
              height: 100%;
              transition: width 0.3s ease;

              &.normal { background-color: #52c41a; }
              &.warning { background-color: #faad14; }
              &.critical { background-color: #ff4d4f; }
            }
          }
        }
      }

      // 服务器状态卡片
      &.server-status {
        &.healthy {
          border-left: 4px solid #52c41a;

          .status-dot {
            background-color: #52c41a;
          }
        }

        &.warning {
          border-left: 4px solid #faad14;

          .status-dot {
            background-color: #faad14;
          }
        }

        &.error {
          border-left: 4px solid #ff4d4f;

          .status-dot {
            background-color: #ff4d4f;
          }
        }
      }
    }
  }

  // 详细监控面板
  .monitor-panels {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 24px;

    .monitor-panel {
      background-color: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .panel-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        .panel-icon {
          font-size: 18px;
        }

        .panel-text {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }

      .panel-content {
        .db-metrics {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 16px;
          margin-bottom: 16px;

          .db-metric {
            text-align: center;
            padding: 12px;
            background-color: #f8f9fa;
            border-radius: 6px;

            .metric-label {
              display: block;
              font-size: 12px;
              color: #666;
              margin-bottom: 4px;
            }

            .metric-value {
              font-size: 18px;
              font-weight: 600;
              color: #333;
            }
          }
        }

        .db-status {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          border-radius: 6px;

          &.healthy {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;

            .status-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #52c41a;
            }

            .status-text {
              color: #389e0d;
            }
          }

          &.warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;

            .status-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #faad14;
            }

            .status-text {
              color: #d48806;
            }
          }

          &.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;

            .status-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #ff4d4f;
            }

            .status-text {
              color: #cf1322;
            }
          }
        }

        .health-checks {
          .health-check {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid #f0f0f0;

            &.healthy {
              background-color: #f6ffed;
              border-color: #b7eb8f;
            }

            &.warning {
              background-color: #fffbe6;
              border-color: #ffe58f;
            }

            &.error {
              background-color: #fff2f0;
              border-color: #ffccc7;
            }

            .check-info {
              flex: 1;

              .check-name {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: #333;
                margin-bottom: 2px;
              }

              .check-description {
                font-size: 12px;
                color: #666;
              }
            }

            .check-status {
              display: flex;
              align-items: center;
              gap: 4px;

              .status-icon {
                font-size: 16px;
              }

              .status-text {
                font-size: 12px;
                font-weight: 500;
              }
            }

            .check-time {
              font-size: 10px;
              color: #999;
              margin-top: 4px;
            }
          }
        }
      }
    }
  }

  // 系统告警和问题提示
  .alerts-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;

      .title-icon {
        font-size: 18px;
      }

      .title-text {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .alerts-list {
      .alert-item {
        border-radius: 6px;
        margin-bottom: 12px;
        border: 1px solid;
        overflow: hidden;

        &.info {
          background-color: #e6f7ff;
          border-color: #91d5ff;
        }

        &.warning {
          background-color: #fffbe6;
          border-color: #ffe58f;
        }

        &.error {
          background-color: #fff2f0;
          border-color: #ffccc7;
        }

        &.critical {
          background-color: #fff0f6;
          border-color: #ffadd2;
        }

        .alert-header {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 16px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.06);

          .alert-icon {
            font-size: 16px;
          }

          .alert-title {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
            color: #333;
          }

          .alert-time {
            font-size: 12px;
            color: #999;
          }
        }

        .alert-message {
          padding: 8px 16px;
          font-size: 13px;
          color: #666;
          line-height: 1.5;
        }

        .alert-actions {
          display: flex;
          gap: 8px;
          padding: 8px 16px;
          border-top: 1px solid rgba(0, 0, 0, 0.06);

          .alert-action {
            padding: 4px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background-color: #fff;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              border-color: #1890ff;
              color: #1890ff;
            }
          }
        }
      }
    }
  }

  // 实时日志流
  .logs-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .title-icon {
        font-size: 18px;
        margin-right: 8px;
      }

      .title-text {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .logs-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .log-level-filter {
          display: flex;
          gap: 4px;

          .level-tag {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background-color: #fff;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;

            &.active {
              background-color: #1890ff;
              border-color: #1890ff;
              color: #fff;
            }

            &:hover:not(.active) {
              border-color: #1890ff;
              color: #1890ff;
            }
          }
        }

        .clear-logs-btn {
          padding: 4px 12px;
          border: 1px solid #ff4d4f;
          border-radius: 4px;
          background-color: #fff;
          color: #ff4d4f;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: #ff4d4f;
            color: #fff;
          }
        }
      }
    }

    .logs-container {
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      background-color: #fafafa;

      .log-entry {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f0;
        font-family: 'Courier New', monospace;
        font-size: 12px;

        &:last-child {
          border-bottom: none;
        }

        &.debug {
          background-color: #f6f6f6;

          .log-level {
            color: #999;
          }
        }

        &.info {
          background-color: #e6f7ff;

          .log-level {
            color: #1890ff;
          }
        }

        &.warn {
          background-color: #fffbe6;

          .log-level {
            color: #faad14;
          }
        }

        &.error {
          background-color: #fff2f0;

          .log-level {
            color: #ff4d4f;
          }
        }

        .log-time {
          color: #999;
          min-width: 80px;
        }

        .log-level {
          font-weight: 600;
          min-width: 50px;
        }

        .log-message {
          flex: 1;
          color: #333;
          word-break: break-all;
        }
      }

      .no-logs {
        text-align: center;
        padding: 40px;
        color: #999;
        font-size: 14px;
      }
    }
  }

  // 动画效果
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .system-monitor-panel {
    padding: 12px;

    .panel-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;

      .header-actions {
        width: 100%;
        justify-content: space-between;

        .refresh-btn, .auto-refresh-toggle {
          flex: 1;
          justify-content: center;
        }
      }
    }

    .overview-cards {
      .card-row {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .metric-card {
        padding: 16px;
      }
    }

    .monitor-panels {
      grid-template-columns: 1fr;
      gap: 16px;

      .monitor-panel {
        padding: 16px;

        .panel-content {
          .db-metrics {
            grid-template-columns: 1fr;
            gap: 12px;
          }

          .health-checks {
            .health-check {
              flex-direction: column;
              align-items: flex-start;
              gap: 8px;

              .check-status {
                align-self: flex-end;
              }
            }
          }
        }
      }
    }

    .alerts-section {
      padding: 16px;

      .section-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .logs-controls {
          width: 100%;
          justify-content: space-between;

          .log-level-filter {
            flex-wrap: wrap;
          }
        }
      }

      .alerts-list {
        .alert-item {
          .alert-header {
            flex-wrap: wrap;
            gap: 8px;

            .alert-time {
              width: 100%;
              text-align: right;
            }
          }

          .alert-actions {
            flex-wrap: wrap;
          }
        }
      }
    }

    .logs-section {
      padding: 16px;

      .section-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .logs-controls {
          width: 100%;
          justify-content: space-between;

          .log-level-filter {
            flex-wrap: wrap;
          }
        }
      }

      .logs-container {
        max-height: 300px;

        .log-entry {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;

          .log-time, .log-level {
            min-width: auto;
          }

          .log-message {
            width: 100%;
            padding-left: 0;
          }
        }
      }
    }
  }
}

// 小屏幕适配
@media (max-width: 480px) {
  .system-monitor-panel {
    padding: 8px;

    .panel-header {
      padding: 12px;

      .header-title {
        .title-text {
          font-size: 18px;
        }
      }

      .header-actions {
        flex-direction: column;
        gap: 8px;

        .refresh-btn, .auto-refresh-toggle {
          width: 100%;
        }
      }
    }

    .overview-cards {
      .metric-card {
        padding: 12px;

        .card-content {
          .metric-value {
            font-size: 24px;
          }
        }
      }
    }

    .monitor-panels {
      .monitor-panel {
        padding: 12px;
      }
    }

    .alerts-section, .logs-section {
      padding: 12px;
    }
  }
}