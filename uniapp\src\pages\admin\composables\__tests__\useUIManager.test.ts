/**
 * UI管理器测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useUIManager, globalUIManager } from '../useUIManager'
import { mockUniModal, mockUniToast } from '../../../test/setup'

describe('useUIManager', () => {
  let uiManager: ReturnType<typeof useUIManager>

  beforeEach(() => {
    uiManager = useUIManager()
    vi.clearAllMocks()
  })

  describe('Toast管理', () => {
    it('应该设置Toast管理器引用', () => {
      const mockToastManager = {
        success: vi.fn(),
        error: vi.fn(),
        warning: vi.fn(),
        info: vi.fn(),
        loading: vi.fn(),
        removeToast: vi.fn(),
        clearAllToasts: vi.fn()
      }

      uiManager.setToastManager(mockToastManager)

      expect(uiManager.uiState.toastManager).toBe(mockToastManager)
    })

    it('应该显示成功提示', () => {
      const mockToastManager = {
        success: vi.fn().mockReturnValue(1)
      }
      uiManager.setToastManager(mockToastManager)

      const result = uiManager.showSuccess('操作成功')

      expect(mockToastManager.success).toHaveBeenCalledWith('操作成功')
      expect(result).toBe(1)
    })

    it('应该在没有Toast管理器时降级到uni.showToast', () => {
      const showToastSpy = vi.spyOn(uni, 'showToast')

      uiManager.showSuccess('操作成功')

      expect(showToastSpy).toHaveBeenCalledWith({
        title: '操作成功',
        icon: 'success',
        duration: 2000
      })
    })

    it('应该显示错误提示', () => {
      const mockToastManager = {
        error: vi.fn().mockReturnValue(2)
      }
      uiManager.setToastManager(mockToastManager)

      const result = uiManager.showError('操作失败')

      expect(mockToastManager.error).toHaveBeenCalledWith('操作失败')
      expect(result).toBe(2)
    })

    it('应该显示警告提示', () => {
      const mockToastManager = {
        warning: vi.fn().mockReturnValue(3)
      }
      uiManager.setToastManager(mockToastManager)

      const result = uiManager.showWarning('警告信息')

      expect(mockToastManager.warning).toHaveBeenCalledWith('警告信息')
      expect(result).toBe(3)
    })

    it('应该显示信息提示', () => {
      const mockToastManager = {
        info: vi.fn().mockReturnValue(4)
      }
      uiManager.setToastManager(mockToastManager)

      const result = uiManager.showInfo('提示信息')

      expect(mockToastManager.info).toHaveBeenCalledWith('提示信息')
      expect(result).toBe(4)
    })

    it('应该显示加载提示', () => {
      const mockToastManager = {
        loading: vi.fn().mockReturnValue(5)
      }
      uiManager.setToastManager(mockToastManager)

      const result = uiManager.showLoading('加载中...')

      expect(mockToastManager.loading).toHaveBeenCalledWith('加载中...')
      expect(result).toBe(5)
    })

    it('应该隐藏加载提示', () => {
      const mockToastManager = {
        removeToast: vi.fn()
      }
      uiManager.setToastManager(mockToastManager)

      uiManager.hideLoading(5)

      expect(mockToastManager.removeToast).toHaveBeenCalledWith(5)
    })
  })

  describe('确认对话框', () => {
    it('应该显示确认对话框', async () => {
      const promise = uiManager.showConfirm({
        message: '确定要删除吗？',
        onConfirm: vi.fn()
      })

      expect(uiManager.uiState.confirmDialog.visible).toBe(true)
      expect(uiManager.uiState.confirmDialog.options.message).toBe('确定要删除吗？')

      // 模拟确认
      uiManager.uiState.confirmDialog.options.onConfirm?.()
      
      const result = await promise
      expect(result).toBe(true)
    })

    it('应该显示删除确认对话框', async () => {
      const promise = uiManager.showDeleteConfirm('测试项目')

      expect(uiManager.uiState.confirmDialog.visible).toBe(true)
      expect(uiManager.uiState.confirmDialog.options.message).toContain('测试项目')
      expect(uiManager.uiState.confirmDialog.options.type).toBe('error')

      // 模拟确认
      uiManager.uiState.confirmDialog.options.onConfirm?.()
      
      const result = await promise
      expect(result).toBe(true)
    })

    it('应该显示输入对话框', async () => {
      const promise = uiManager.showPrompt('请输入名称', '默认名称')

      expect(uiManager.uiState.confirmDialog.visible).toBe(true)
      expect(uiManager.uiState.confirmDialog.options.showInput).toBe(true)
      expect(uiManager.uiState.confirmDialog.options.inputPlaceholder).toBe('默认名称')

      // 模拟输入确认
      uiManager.uiState.confirmDialog.options.onConfirm?.('用户输入')
      
      const result = await promise
      expect(result).toBe('用户输入')
    })

    it('应该显示批量确认对话框', async () => {
      const promise = uiManager.showBatchConfirm('删除', 5, '用户')

      expect(uiManager.uiState.confirmDialog.visible).toBe(true)
      expect(uiManager.uiState.confirmDialog.options.message).toContain('删除 5 个用户')
      expect(uiManager.uiState.confirmDialog.options.type).toBe('warning')

      // 模拟确认
      uiManager.uiState.confirmDialog.options.onConfirm?.()
      
      const result = await promise
      expect(result).toBe(true)
    })
  })

  describe('全局加载状态', () => {
    it('应该设置全局加载状态', () => {
      uiManager.setGlobalLoading(true, '处理中...', 50)

      expect(uiManager.uiState.loading.visible).toBe(true)
      expect(uiManager.uiState.loading.message).toBe('处理中...')
      expect(uiManager.uiState.loading.progress).toBe(50)
    })

    it('应该隐藏全局加载状态', () => {
      uiManager.setGlobalLoading(true, '加载中...')
      uiManager.setGlobalLoading(false)

      expect(uiManager.uiState.loading.visible).toBe(false)
    })
  })

  describe('便捷方法', () => {
    it('应该显示操作结果 - 成功', () => {
      const mockToastManager = {
        success: vi.fn()
      }
      uiManager.setToastManager(mockToastManager)

      uiManager.showOperationResult(true, '保存成功', '保存失败')

      expect(mockToastManager.success).toHaveBeenCalledWith('保存成功')
    })

    it('应该显示操作结果 - 失败', () => {
      const mockToastManager = {
        error: vi.fn()
      }
      uiManager.setToastManager(mockToastManager)

      uiManager.showOperationResult(false, '保存成功', '保存失败')

      expect(mockToastManager.error).toHaveBeenCalledWith('保存失败')
    })

    it('应该显示网络错误提示', async () => {
      const retryCallback = vi.fn()
      const promise = uiManager.showNetworkError(retryCallback)

      expect(uiManager.uiState.confirmDialog.visible).toBe(true)
      expect(uiManager.uiState.confirmDialog.options.type).toBe('error')
      expect(uiManager.uiState.confirmDialog.options.confirmText).toBe('重试')

      // 模拟点击重试
      uiManager.uiState.confirmDialog.options.onConfirm?.()
      
      expect(retryCallback).toHaveBeenCalled()
    })

    it('应该显示权限错误提示', async () => {
      const promise = uiManager.showPermissionError('权限不足')

      expect(uiManager.uiState.confirmDialog.visible).toBe(true)
      expect(uiManager.uiState.confirmDialog.options.type).toBe('warning')
      expect(uiManager.uiState.confirmDialog.options.message).toBe('权限不足')
      expect(uiManager.uiState.confirmDialog.options.showCancel).toBe(false)
    })

    it('应该清除所有通知', () => {
      const mockToastManager = {
        clearAllToasts: vi.fn()
      }
      uiManager.setToastManager(mockToastManager)
      
      const hideLoadingSpy = vi.spyOn(uni, 'hideLoading')
      const hideToastSpy = vi.spyOn(uni, 'hideToast')

      uiManager.setGlobalLoading(true)
      uiManager.uiState.confirmDialog.visible = true

      uiManager.clearAllNotifications()

      expect(mockToastManager.clearAllToasts).toHaveBeenCalled()
      expect(uiManager.uiState.confirmDialog.visible).toBe(false)
      expect(uiManager.uiState.loading.visible).toBe(false)
      expect(hideLoadingSpy).toHaveBeenCalled()
      expect(hideToastSpy).toHaveBeenCalled()
    })
  })
})

describe('globalUIManager', () => {
  it('应该是useUIManager的实例', () => {
    expect(globalUIManager).toBeDefined()
    expect(typeof globalUIManager.showSuccess).toBe('function')
    expect(typeof globalUIManager.showError).toBe('function')
    expect(typeof globalUIManager.showConfirm).toBe('function')
  })
})
