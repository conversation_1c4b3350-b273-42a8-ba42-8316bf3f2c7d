/**
 * 全局UI管理器
 * 统一管理Toast、确认对话框、加载状态等UI组件
 */

import { ref, reactive } from 'vue'
import type { ToastOptions } from '../components/common/ToastManager.vue'
import type { ConfirmDialogOptions } from '../components/common/ConfirmDialog.vue'

interface LoadingState {
  visible: boolean
  message: string
  progress?: number
}

interface UIState {
  loading: LoadingState
  toastManager: any
  confirmDialog: {
    visible: boolean
    options: ConfirmDialogOptions
  }
}

// 全局UI状态
const uiState = reactive<UIState>({
  loading: {
    visible: false,
    message: '加载中...',
    progress: undefined
  },
  toastManager: null,
  confirmDialog: {
    visible: false,
    options: {
      message: ''
    }
  }
})

// Toast管理器引用
const toastManagerRef = ref<any>(null)

export function useUIManager() {
  
  /**
   * 设置Toast管理器引用
   */
  const setToastManager = (manager: any) => {
    toastManagerRef.value = manager
    uiState.toastManager = manager
  }

  /**
   * 显示成功提示
   */
  const showSuccess = (message: string, options?: Partial<ToastOptions>) => {
    if (toastManagerRef.value) {
      return toastManagerRef.value.success(message, options)
    } else {
      // 降级到uni.showToast
      uni.showToast({
        title: message,
        icon: 'success',
        duration: options?.duration || 2000
      })
    }
  }

  /**
   * 显示错误提示
   */
  const showError = (message: string, options?: Partial<ToastOptions>) => {
    if (toastManagerRef.value) {
      return toastManagerRef.value.error(message, options)
    } else {
      // 降级到uni.showToast
      uni.showToast({
        title: message,
        icon: 'error',
        duration: options?.duration || 3000
      })
    }
  }

  /**
   * 显示警告提示
   */
  const showWarning = (message: string, options?: Partial<ToastOptions>) => {
    if (toastManagerRef.value) {
      return toastManagerRef.value.warning(message, options)
    } else {
      // 降级到uni.showToast
      uni.showToast({
        title: message,
        icon: 'none',
        duration: options?.duration || 3000
      })
    }
  }

  /**
   * 显示信息提示
   */
  const showInfo = (message: string, options?: Partial<ToastOptions>) => {
    if (toastManagerRef.value) {
      return toastManagerRef.value.info(message, options)
    } else {
      // 降级到uni.showToast
      uni.showToast({
        title: message,
        icon: 'none',
        duration: options?.duration || 3000
      })
    }
  }

  /**
   * 显示加载提示
   */
  const showLoading = (message: string = '加载中...', options?: Partial<ToastOptions>) => {
    if (toastManagerRef.value) {
      return toastManagerRef.value.loading(message, options)
    } else {
      // 降级到uni.showLoading
      uni.showLoading({
        title: message,
        mask: true
      })
    }
  }

  /**
   * 隐藏加载提示
   */
  const hideLoading = (toastId?: number) => {
    if (toastManagerRef.value && toastId) {
      toastManagerRef.value.removeToast(toastId)
    } else {
      uni.hideLoading()
    }
  }

  /**
   * 显示确认对话框
   */
  const showConfirm = (options: ConfirmDialogOptions): Promise<string | boolean> => {
    return new Promise((resolve, reject) => {
      uiState.confirmDialog.options = {
        ...options,
        onConfirm: async (inputValue?: string) => {
          try {
            if (options.onConfirm) {
              await options.onConfirm(inputValue)
            }
            resolve(inputValue || true)
          } catch (error) {
            reject(error)
          }
        },
        onCancel: () => {
          if (options.onCancel) {
            options.onCancel()
          }
          resolve(false)
        }
      }
      uiState.confirmDialog.visible = true
    })
  }

  /**
   * 显示删除确认对话框
   */
  const showDeleteConfirm = (itemName?: string): Promise<boolean> => {
    const message = itemName 
      ? `确定要删除"${itemName}"吗？此操作不可撤销。`
      : '确定要删除选中的项目吗？此操作不可撤销。'
    
    return showConfirm({
      type: 'error',
      title: '删除确认',
      message,
      confirmText: '删除',
      cancelText: '取消'
    }) as Promise<boolean>
  }

  /**
   * 显示输入对话框
   */
  const showPrompt = (
    message: string, 
    placeholder?: string,
    validator?: (value: string) => string | null
  ): Promise<string | false> => {
    return showConfirm({
      type: 'info',
      title: '输入',
      message,
      showInput: true,
      inputPlaceholder: placeholder,
      inputValidator: validator,
      confirmText: '确定',
      cancelText: '取消'
    }) as Promise<string | false>
  }

  /**
   * 设置全局加载状态
   */
  const setGlobalLoading = (visible: boolean, message?: string, progress?: number) => {
    uiState.loading.visible = visible
    if (message !== undefined) {
      uiState.loading.message = message
    }
    if (progress !== undefined) {
      uiState.loading.progress = progress
    }
  }

  /**
   * 显示操作结果
   */
  const showOperationResult = (
    success: boolean, 
    successMessage: string = '操作成功',
    errorMessage: string = '操作失败'
  ) => {
    if (success) {
      showSuccess(successMessage)
    } else {
      showError(errorMessage)
    }
  }

  /**
   * 批量操作确认
   */
  const showBatchConfirm = (
    action: string,
    count: number,
    itemType: string = '项目'
  ): Promise<boolean> => {
    return showConfirm({
      type: 'warning',
      title: '批量操作确认',
      message: `确定要${action} ${count} 个${itemType}吗？`,
      confirmText: '确定',
      cancelText: '取消'
    }) as Promise<boolean>
  }

  /**
   * 显示网络错误提示
   */
  const showNetworkError = (retryCallback?: () => void) => {
    if (retryCallback) {
      showConfirm({
        type: 'error',
        title: '网络错误',
        message: '网络连接失败，请检查网络设置后重试',
        confirmText: '重试',
        cancelText: '取消',
        onConfirm: retryCallback
      })
    } else {
      showError('网络连接失败，请检查网络设置')
    }
  }

  /**
   * 显示权限错误提示
   */
  const showPermissionError = (message: string = '没有权限执行此操作') => {
    showConfirm({
      type: 'warning',
      title: '权限不足',
      message,
      confirmText: '知道了',
      showCancel: false
    })
  }

  /**
   * 清除所有提示
   */
  const clearAllNotifications = () => {
    if (toastManagerRef.value) {
      toastManagerRef.value.clearAllToasts()
    }
    uiState.confirmDialog.visible = false
    uiState.loading.visible = false
    uni.hideLoading()
    uni.hideToast()
  }

  return {
    // 状态
    uiState,
    
    // Toast相关
    setToastManager,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
    hideLoading,
    
    // 对话框相关
    showConfirm,
    showDeleteConfirm,
    showPrompt,
    showBatchConfirm,
    
    // 加载状态
    setGlobalLoading,
    
    // 便捷方法
    showOperationResult,
    showNetworkError,
    showPermissionError,
    clearAllNotifications
  }
}

// 创建全局实例
export const globalUIManager = useUIManager()
