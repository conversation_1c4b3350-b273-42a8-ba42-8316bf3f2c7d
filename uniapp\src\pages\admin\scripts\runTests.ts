/**
 * 测试运行脚本
 * 用于运行管理系统的各种测试
 */

import { performanceMonitor } from '../utils/performance'

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行管理系统测试...')
  
  performanceMonitor.mark('tests-start')
  
  try {
    // 运行单元测试
    await runUnitTests()
    
    // 运行集成测试
    await runIntegrationTests()
    
    // 运行性能测试
    await runPerformanceTests()
    
    performanceMonitor.mark('tests-end')
    const duration = performanceMonitor.measure('total-tests', 'tests-start', 'tests-end')
    
    console.log(`✅ 所有测试完成，总耗时: ${duration.toFixed(2)}ms`)
    
    // 输出性能报告
    printPerformanceReport()
    
  } catch (error) {
    console.error('❌ 测试运行失败:', error)
    throw error
  }
}

/**
 * 运行单元测试
 */
async function runUnitTests() {
  console.log('📋 运行单元测试...')
  
  performanceMonitor.mark('unit-tests-start')
  
  // 这里可以集成vitest或其他测试框架
  // 目前只是模拟测试运行
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  performanceMonitor.mark('unit-tests-end')
  performanceMonitor.measure('unit-tests', 'unit-tests-start', 'unit-tests-end')
  
  console.log('✅ 单元测试完成')
}

/**
 * 运行集成测试
 */
async function runIntegrationTests() {
  console.log('🔗 运行集成测试...')
  
  performanceMonitor.mark('integration-tests-start')
  
  // 模拟集成测试
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  performanceMonitor.mark('integration-tests-end')
  performanceMonitor.measure('integration-tests', 'integration-tests-start', 'integration-tests-end')
  
  console.log('✅ 集成测试完成')
}

/**
 * 运行性能测试
 */
async function runPerformanceTests() {
  console.log('⚡ 运行性能测试...')
  
  performanceMonitor.mark('performance-tests-start')
  
  // 测试数据加载性能
  await testDataLoadingPerformance()
  
  // 测试UI渲染性能
  await testUIRenderingPerformance()
  
  // 测试内存使用
  await testMemoryUsage()
  
  performanceMonitor.mark('performance-tests-end')
  performanceMonitor.measure('performance-tests', 'performance-tests-start', 'performance-tests-end')
  
  console.log('✅ 性能测试完成')
}

/**
 * 测试数据加载性能
 */
async function testDataLoadingPerformance() {
  console.log('  📊 测试数据加载性能...')
  
  performanceMonitor.mark('data-loading-start')
  
  // 模拟大量数据加载
  const testData = Array.from({ length: 1000 }, (_, i) => ({
    id: i + 1,
    name: `项目${i + 1}`,
    description: `这是第${i + 1}个测试项目`,
    status: i % 2 === 0 ? 'active' : 'inactive',
    createdAt: new Date().toISOString()
  }))
  
  // 模拟数据处理
  const processedData = testData.map(item => ({
    ...item,
    displayName: `${item.name} (${item.status})`
  }))
  
  performanceMonitor.mark('data-loading-end')
  const duration = performanceMonitor.measure('data-loading', 'data-loading-start', 'data-loading-end')
  
  console.log(`    ✓ 数据加载测试完成，处理${testData.length}条记录耗时: ${duration.toFixed(2)}ms`)
  
  // 性能断言
  if (duration > 100) {
    console.warn(`    ⚠️  数据加载性能警告: ${duration.toFixed(2)}ms > 100ms`)
  }
}

/**
 * 测试UI渲染性能
 */
async function testUIRenderingPerformance() {
  console.log('  🎨 测试UI渲染性能...')
  
  performanceMonitor.mark('ui-rendering-start')
  
  // 模拟DOM操作
  const elements = []
  for (let i = 0; i < 100; i++) {
    elements.push({
      id: i,
      type: 'div',
      className: 'test-element',
      innerHTML: `测试元素 ${i}`
    })
  }
  
  // 模拟渲染延迟
  await new Promise(resolve => setTimeout(resolve, 50))
  
  performanceMonitor.mark('ui-rendering-end')
  const duration = performanceMonitor.measure('ui-rendering', 'ui-rendering-start', 'ui-rendering-end')
  
  console.log(`    ✓ UI渲染测试完成，渲染${elements.length}个元素耗时: ${duration.toFixed(2)}ms`)
  
  // 性能断言
  if (duration > 200) {
    console.warn(`    ⚠️  UI渲染性能警告: ${duration.toFixed(2)}ms > 200ms`)
  }
}

/**
 * 测试内存使用
 */
async function testMemoryUsage() {
  console.log('  💾 测试内存使用...')
  
  // 模拟内存使用测试
  const initialMemory = performance.memory?.usedJSHeapSize || 0
  
  // 创建一些测试对象
  const testObjects = []
  for (let i = 0; i < 1000; i++) {
    testObjects.push({
      id: i,
      data: new Array(100).fill(`test-data-${i}`),
      timestamp: Date.now()
    })
  }
  
  const finalMemory = performance.memory?.usedJSHeapSize || 0
  const memoryUsed = finalMemory - initialMemory
  
  console.log(`    ✓ 内存使用测试完成，使用内存: ${(memoryUsed / 1024 / 1024).toFixed(2)}MB`)
  
  // 清理测试对象
  testObjects.length = 0
  
  // 内存使用断言
  if (memoryUsed > 10 * 1024 * 1024) { // 10MB
    console.warn(`    ⚠️  内存使用警告: ${(memoryUsed / 1024 / 1024).toFixed(2)}MB > 10MB`)
  }
}

/**
 * 打印性能报告
 */
function printPerformanceReport() {
  console.log('\n📊 性能测试报告:')
  console.log('=' .repeat(50))
  
  const measures = performanceMonitor.getAllMeasures()
  
  Object.entries(measures).forEach(([name, duration]) => {
    const status = getPerformanceStatus(name, duration)
    console.log(`${status} ${name}: ${duration.toFixed(2)}ms`)
  })
  
  console.log('=' .repeat(50))
}

/**
 * 获取性能状态图标
 */
function getPerformanceStatus(testName: string, duration: number): string {
  const thresholds: Record<string, number> = {
    'data-loading': 100,
    'ui-rendering': 200,
    'unit-tests': 5000,
    'integration-tests': 10000,
    'performance-tests': 3000,
    'total-tests': 20000
  }
  
  const threshold = thresholds[testName] || 1000
  
  if (duration <= threshold * 0.5) {
    return '🟢' // 优秀
  } else if (duration <= threshold) {
    return '🟡' // 良好
  } else {
    return '🔴' // 需要优化
  }
}

/**
 * 生成测试报告
 */
export function generateTestReport() {
  const measures = performanceMonitor.getAllMeasures()
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests: Object.keys(measures).length,
      totalDuration: measures['total-tests'] || 0,
      status: 'completed'
    },
    details: measures,
    recommendations: generateRecommendations(measures)
  }
  
  return report
}

/**
 * 生成性能优化建议
 */
function generateRecommendations(measures: Record<string, number>): string[] {
  const recommendations: string[] = []
  
  if (measures['data-loading'] > 100) {
    recommendations.push('考虑实现数据分页或虚拟滚动来优化数据加载性能')
  }
  
  if (measures['ui-rendering'] > 200) {
    recommendations.push('考虑使用虚拟DOM或减少DOM操作来优化UI渲染性能')
  }
  
  if (measures['total-tests'] > 20000) {
    recommendations.push('考虑并行运行测试或优化测试用例来减少总测试时间')
  }
  
  if (recommendations.length === 0) {
    recommendations.push('性能表现良好，无需特别优化')
  }
  
  return recommendations
}

// 如果直接运行此脚本
if (typeof window !== 'undefined' && (window as any).__TEST_MODE__) {
  runAllTests().catch(console.error)
}
