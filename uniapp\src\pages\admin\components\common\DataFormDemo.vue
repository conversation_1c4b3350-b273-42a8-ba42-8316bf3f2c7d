<template>
  <view class="data-form-demo">
    <view class="demo-header">
      <text class="demo-title">DataForm 组件演示</text>
      <view class="demo-controls">
        <button @click="switchMode('create')" :class="{ active: currentMode === 'create' }">创建模式</button>
        <button @click="switchMode('edit')" :class="{ active: currentMode === 'edit' }">编辑模式</button>
        <button @click="switchMode('view')" :class="{ active: currentMode === 'view' }">查看模式</button>
      </view>
    </view>

    <DataForm
      :fields="formFields"
      :data="formData"
      :mode="currentMode"
      :validation="validationRules"
      :loading="loading"
      :submitting="submitting"
      :columns="2"
      @submit="handleSubmit"
      @cancel="handleCancel"
      @reset="handleReset"
      @change="handleFieldChange"
    />

    <view class="demo-info">
      <view class="info-section">
        <text class="info-title">当前表单数据:</text>
        <text class="info-content">{{ JSON.stringify(formData, null, 2) }}</text>
      </view>
      
      <view class="info-section">
        <text class="info-title">表单验证状态:</text>
        <text class="info-content">{{ formValid ? '有效' : '无效' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import DataForm from './DataForm.vue'
import type { FormField, ValidationRules } from '../../types/admin'

// 响应式数据
const currentMode = ref<'create' | 'edit' | 'view'>('create')
const loading = ref(false)
const submitting = ref(false)
const formValid = ref(false)

// 表单数据
const formData = reactive({
  username: '',
  email: '',
  age: null,
  gender: '',
  interests: [],
  bio: '',
  birthDate: '',
  isActive: false,
  avatar: null,
  favoriteColor: '#1890ff',
  priority: 50
})

// 表单字段定义
const formFields: FormField[] = [
  {
    key: 'username',
    label: '用户名',
    type: 'text',
    required: true,
    placeholder: '请输入用户名',
    rules: [
      { type: 'required', message: '用户名不能为空' },
      { type: 'min', value: 3, message: '用户名至少3个字符' },
      { type: 'max', value: 20, message: '用户名最多20个字符' }
    ],
    help: '用户名用于登录系统',
    section: '基本信息'
  },
  {
    key: 'email',
    label: '邮箱地址',
    type: 'email',
    required: true,
    placeholder: '请输入邮箱地址',
    rules: [
      { type: 'required', message: '邮箱不能为空' },
      { type: 'email', message: '请输入有效的邮箱地址' }
    ],
    section: '基本信息'
  },
  {
    key: 'age',
    label: '年龄',
    type: 'number',
    placeholder: '请输入年龄',
    min: 1,
    max: 120,
    rules: [
      { type: 'min', value: 1, message: '年龄不能小于1' },
      { type: 'max', value: 120, message: '年龄不能大于120' }
    ],
    section: '基本信息'
  },
  {
    key: 'gender',
    label: '性别',
    type: 'radio',
    required: true,
    options: [
      { label: '男', value: 'male' },
      { label: '女', value: 'female' },
      { label: '其他', value: 'other' }
    ],
    rules: [
      { type: 'required', message: '请选择性别' }
    ],
    section: '基本信息'
  },
  {
    key: 'interests',
    label: '兴趣爱好',
    type: 'checkbox',
    options: [
      { label: '阅读', value: 'reading' },
      { label: '运动', value: 'sports' },
      { label: '音乐', value: 'music' },
      { label: '旅行', value: 'travel' },
      { label: '编程', value: 'programming' }
    ],
    rules: [
      { type: 'min', value: 1, message: '至少选择一个兴趣爱好' }
    ],
    section: '个人偏好'
  },
  {
    key: 'bio',
    label: '个人简介',
    type: 'textarea',
    placeholder: '请输入个人简介',
    rules: [
      { type: 'max', value: 500, message: '个人简介不能超过500个字符' }
    ],
    help: '简单介绍一下自己',
    section: '个人偏好'
  },
  {
    key: 'birthDate',
    label: '出生日期',
    type: 'date',
    section: '个人偏好'
  },
  {
    key: 'isActive',
    label: '账户状态',
    type: 'switch',
    help: '开启后账户将处于活跃状态',
    section: '账户设置'
  },
  {
    key: 'avatar',
    label: '头像',
    type: 'file',
    accept: 'image/*',
    help: '支持 JPG、PNG 格式，大小不超过 2MB',
    section: '账户设置'
  },
  {
    key: 'favoriteColor',
    label: '喜欢的颜色',
    type: 'color',
    section: '账户设置'
  },
  {
    key: 'priority',
    label: '优先级',
    type: 'range',
    min: 0,
    max: 100,
    step: 10,
    help: '设置用户优先级（0-100）',
    section: '账户设置'
  }
]

// 验证规则
const validationRules: ValidationRules = {
  username: [
    { type: 'required', message: '用户名不能为空' },
    { type: 'min', value: 3, message: '用户名至少3个字符' }
  ],
  email: [
    { type: 'required', message: '邮箱不能为空' },
    { type: 'email', message: '请输入有效的邮箱地址' }
  ]
}

// 方法
const switchMode = (mode: 'create' | 'edit' | 'view') => {
  currentMode.value = mode
  
  // 在编辑模式下填充一些示例数据
  if (mode === 'edit') {
    Object.assign(formData, {
      username: 'john_doe',
      email: '<EMAIL>',
      age: 25,
      gender: 'male',
      interests: ['reading', 'programming'],
      bio: '这是一个示例用户的个人简介。',
      birthDate: '1998-01-01',
      isActive: true,
      favoriteColor: '#ff6b6b',
      priority: 80
    })
  } else if (mode === 'create') {
    // 创建模式下清空数据
    Object.assign(formData, {
      username: '',
      email: '',
      age: null,
      gender: '',
      interests: [],
      bio: '',
      birthDate: '',
      isActive: false,
      avatar: null,
      favoriteColor: '#1890ff',
      priority: 50
    })
  }
}

const handleSubmit = async (data: Record<string, any>) => {
  console.log('表单提交:', data)
  
  submitting.value = true
  
  // 模拟API调用
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    uni.showToast({
      title: currentMode.value === 'create' ? '创建成功' : '更新成功',
      icon: 'success'
    })
    
    console.log('提交成功:', data)
  } catch (error) {
    uni.showToast({
      title: '提交失败',
      icon: 'error'
    })
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  console.log('取消操作')
  uni.showToast({
    title: '已取消',
    icon: 'none'
  })
}

const handleReset = () => {
  console.log('重置表单')
  uni.showToast({
    title: '表单已重置',
    icon: 'none'
  })
}

const handleFieldChange = (key: string, value: any) => {
  console.log('字段变更:', key, value)
  // 这里可以添加实时验证或其他逻辑
}
</script>

<style lang="scss" scoped>
.data-form-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .demo-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .demo-title {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      display: block;
      margin-bottom: 16px;
    }

    .demo-controls {
      display: flex;
      gap: 8px;

      button {
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.active {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;
        }
      }
    }
  }

  .demo-info {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e8e8e8;

    .info-section {
      margin-bottom: 16px;

      .info-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        display: block;
        margin-bottom: 8px;
      }

      .info-content {
        font-size: 14px;
        color: #666;
        background-color: #f5f5f5;
        padding: 12px;
        border-radius: 4px;
        white-space: pre-wrap;
        font-family: 'Courier New', monospace;
        display: block;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .data-form-demo {
    padding: 16px;

    .demo-header {
      .demo-controls {
        flex-direction: column;

        button {
          width: 100%;
        }
      }
    }
  }
}
</style>