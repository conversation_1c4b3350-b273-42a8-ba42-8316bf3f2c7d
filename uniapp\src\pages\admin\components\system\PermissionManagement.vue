<template>
  <view class="permission-management">
    <!-- 权限管理头部 -->
    <view class="management-header">
      <view class="header-title">权限管理</view>
      <view class="header-actions">
        <button class="action-btn" @click="toggleTreeView">
          <text class="btn-icon">{{ isTreeView ? '📋' : '🌳' }}</text>
          {{ isTreeView ? '列表视图' : '树形视图' }}
        </button>
        <button class="action-btn primary" @click="handleAddPermission">
          <text class="btn-icon">➕</text>
          添加权限
        </button>
      </view>
    </view>

    <!-- 树形视图 -->
    <view v-if="isTreeView" class="tree-view">
      <view class="tree-container">
        <view 
          v-for="permission in permissionTree" 
          :key="permission.id"
          class="tree-node"
        >
          <view class="node-content">
            <view class="node-info">
              <view class="node-name">{{ permission.name }}</view>
              <view class="node-code">{{ permission.code }}</view>
              <view class="node-type">{{ getPermissionTypeLabel(permission.type) }}</view>
              <view class="node-status" :class="{ active: permission.status === 1 }">
                {{ permission.status === 1 ? '启用' : '禁用' }}
              </view>
            </view>
            <view class="node-actions">
              <button class="node-btn" @click="handleViewPermission(permission)">查看</button>
              <button class="node-btn" @click="handleEditPermission(permission)">编辑</button>
              <button class="node-btn danger" @click="handleDeletePermission(permission)">删除</button>
            </view>
          </view>
          
          <!-- 子权限 -->
          <view v-if="permission.children && permission.children.length > 0" class="tree-children">
            <view 
              v-for="child in permission.children" 
              :key="child.id"
              class="tree-node child"
            >
              <view class="node-content">
                <view class="node-info">
                  <view class="node-name">{{ child.name }}</view>
                  <view class="node-code">{{ child.code }}</view>
                  <view class="node-type">{{ getPermissionTypeLabel(child.type) }}</view>
                  <view class="node-status" :class="{ active: child.status === 1 }">
                    {{ child.status === 1 ? '启用' : '禁用' }}
                  </view>
                </view>
                <view class="node-actions">
                  <button class="node-btn" @click="handleViewPermission(child)">查看</button>
                  <button class="node-btn" @click="handleEditPermission(child)">编辑</button>
                  <button class="node-btn danger" @click="handleDeletePermission(child)">删除</button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 列表视图 -->
    <view v-else class="list-view">
      <DataTable
        :data="permissionCrud.data.value"
        :columns="permissionColumns"
        :loading="permissionCrud.loading.value"
        :pagination="permissionCrud.pagination"
        :searchable="true"
        :sortable="true"
        :actions="tableActions"
        :selectable="true"
        :batch-actions="batchActions"
        title="权限列表"
        search-placeholder="搜索权限名称、代码"
        @search="permissionCrud.search"
        @sort="permissionCrud.sort"
        @page-change="permissionCrud.changePage"
        @page-size-change="permissionCrud.changePageSize"
        @batch-action="handleBatchAction"
      />
    </view>

    <!-- 权限表单弹窗 -->
    <view v-if="showPermissionForm" class="modal-overlay" @click="closePermissionForm">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="modal-title">{{ formMode === 'create' ? '添加权限' : formMode === 'edit' ? '编辑权限' : '查看权限' }}</view>
          <view class="modal-close" @click="closePermissionForm">✕</view>
        </view>
        
        <view class="modal-body">
          <DataForm
            :fields="permissionFormFields"
            :data="formData"
            :mode="formMode"
            :loading="permissionCrud.submitting.value"
            @submit="handlePermissionSubmit"
            @cancel="closePermissionForm"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useCrud } from '../../composables/useCrud'
import { mockPermissionApi, permissionExtendedApi } from '../../api/user'
import DataTable from '../common/DataTable.vue'
import DataForm from '../common/DataForm.vue'
import type { Permission, PermissionFormData, TableColumn, TableAction, FormField } from '../../types/admin'

// 响应式数据
const isTreeView = ref(true)
const showPermissionForm = ref(false)
const formMode = ref<'create' | 'edit' | 'view'>('create')
const formData = ref<Partial<PermissionFormData>>({})
const permissionTree = ref<Permission[]>([])

// 使用CRUD composable
const permissionCrud = useCrud<Permission>(mockPermissionApi, {
  initialPagination: { pageSize: 10 },
  autoLoad: true,
  cacheKey: 'permissions'
})

// 表格列配置
const permissionColumns: TableColumn[] = [
  {
    key: 'id',
    title: 'ID',
    width: '80px',
    sortable: true
  },
  {
    key: 'name',
    title: '权限名称',
    sortable: true
  },
  {
    key: 'code',
    title: '权限代码',
    sortable: true
  },
  {
    key: 'type',
    title: '权限类型',
    render: (value: string) => getPermissionTypeLabel(value)
  },
  {
    key: 'path',
    title: '权限路径'
  },
  {
    key: 'sort',
    title: '排序',
    sortable: true
  },
  {
    key: 'status',
    title: '状态',
    render: (value: number) => value === 1 ? '启用' : '禁用'
  },
  {
    key: 'createTime',
    title: '创建时间'
  }
]

// 表格操作
const tableActions: TableAction[] = [
  {
    type: 'view',
    label: '查看',
    handler: handleViewPermission
  },
  {
    type: 'edit',
    label: '编辑',
    handler: handleEditPermission
  },
  {
    type: 'delete',
    label: '删除',
    handler: handleDeletePermission
  }
]

// 批量操作
const batchActions: TableAction[] = [
  {
    type: 'delete',
    label: '批量删除',
    handler: handleBatchDelete
  }
]

// 权限表单字段
const permissionFormFields = computed<FormField[]>(() => [
  {
    key: 'name',
    label: '权限名称',
    type: 'text',
    required: true,
    placeholder: '请输入权限名称',
    rules: [
      { type: 'required', message: '权限名称不能为空' },
      { type: 'max', value: 50, message: '权限名称最多50个字符' }
    ]
  },
  {
    key: 'code',
    label: '权限代码',
    type: 'text',
    required: true,
    placeholder: '请输入权限代码（英文字母、数字、下划线、冒号）',
    disabled: formMode.value === 'edit', // 编辑时不允许修改代码
    rules: [
      { type: 'required', message: '权限代码不能为空' },
      { type: 'pattern', value: '^[a-zA-Z0-9_:]+$', message: '权限代码只能包含字母、数字、下划线和冒号' },
      { type: 'max', value: 100, message: '权限代码最多100个字符' }
    ]
  },
  {
    key: 'type',
    label: '权限类型',
    type: 'select',
    required: true,
    options: [
      { label: '菜单权限', value: 'menu' },
      { label: '按钮权限', value: 'button' },
      { label: 'API权限', value: 'api' }
    ],
    rules: [
      { type: 'required', message: '请选择权限类型' }
    ]
  },
  {
    key: 'parentId',
    label: '父权限',
    type: 'select',
    options: getParentPermissionOptions(),
    help: '选择父权限，留空表示顶级权限'
  },
  {
    key: 'path',
    label: '权限路径',
    type: 'text',
    placeholder: '请输入权限路径（菜单权限必填）',
    condition: (data) => data.type === 'menu',
    rules: [
      { type: 'required', message: '菜单权限必须填写路径' }
    ]
  },
  {
    key: 'description',
    label: '权限描述',
    type: 'textarea',
    placeholder: '请输入权限描述',
    rules: [
      { type: 'max', value: 200, message: '权限描述最多200个字符' }
    ]
  },
  {
    key: 'sort',
    label: '排序',
    type: 'number',
    required: true,
    placeholder: '请输入排序值（数字越小越靠前）',
    min: 0,
    max: 9999,
    rules: [
      { type: 'required', message: '排序不能为空' }
    ]
  },
  {
    key: 'status',
    label: '权限状态',
    type: 'radio',
    required: true,
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  }
])

// 计算属性和方法
function getPermissionTypeLabel(type: string): string {
  const typeMap: Record<string, string> = {
    menu: '菜单权限',
    button: '按钮权限',
    api: 'API权限'
  }
  return typeMap[type] || type
}

function getParentPermissionOptions() {
  // 这里应该从权限列表中获取可作为父权限的选项
  // 暂时返回空数组，实际使用时需要实现
  return []
}

function toggleTreeView() {
  isTreeView.value = !isTreeView.value
  if (isTreeView.value) {
    loadPermissionTree()
  }
}

async function loadPermissionTree() {
  try {
    const permissions = await mockPermissionApi.list({ page: 1, pageSize: 1000 })
    permissionTree.value = buildPermissionTree(permissions.items || [])
  } catch (error) {
    console.error('Load permission tree error:', error)
    permissionCrud.showError('加载权限树失败')
  }
}

function buildPermissionTree(permissions: Permission[]): Permission[] {
  const tree: Permission[] = []
  const map = new Map<number, Permission>()
  
  // 创建映射
  permissions.forEach(permission => {
    map.set(permission.id, { ...permission, children: [] })
  })
  
  // 构建树结构
  permissions.forEach(permission => {
    const node = map.get(permission.id)!
    if (permission.parentId && map.has(permission.parentId)) {
      const parent = map.get(permission.parentId)!
      parent.children = parent.children || []
      parent.children.push(node)
    } else {
      tree.push(node)
    }
  })
  
  return tree
}

function handleAddPermission() {
  formMode.value = 'create'
  formData.value = {
    name: '',
    code: '',
    type: 'menu',
    parentId: undefined,
    path: '',
    description: '',
    sort: 0,
    status: 1
  }
  showPermissionForm.value = true
}

function handleViewPermission(permission: Permission) {
  formMode.value = 'view'
  formData.value = { ...permission }
  showPermissionForm.value = true
}

function handleEditPermission(permission: Permission) {
  formMode.value = 'edit'
  formData.value = { ...permission }
  showPermissionForm.value = true
}

async function handleDeletePermission(permission: Permission) {
  await permissionCrud.remove(permission.id)
  if (isTreeView.value) {
    await loadPermissionTree()
  }
}

function closePermissionForm() {
  showPermissionForm.value = false
  formData.value = {}
}

async function handlePermissionSubmit(data: PermissionFormData) {
  try {
    if (formMode.value === 'create') {
      await permissionCrud.create(data)
    } else {
      await permissionCrud.update(formData.value.id!, data)
    }
    
    closePermissionForm()
    
    if (isTreeView.value) {
      await loadPermissionTree()
    }
  } catch (error) {
    console.error('Submit permission error:', error)
  }
}

async function handleBatchAction(action: TableAction, selectedItems: Permission[]) {
  if (action.type === 'delete') {
    await handleBatchDelete(selectedItems)
  }
}

async function handleBatchDelete(permissions: Permission[]) {
  const ids = permissions.map(permission => permission.id)
  await permissionCrud.batchRemove(ids)
}

// 生命周期
onMounted(() => {
  if (isTreeView.value) {
    loadPermissionTree()
  }
})
</script>

<style lang="scss" scoped>
.permission-management {
  height: 100%;
  display: flex;
  flex-direction: column;

  .management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .action-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background-color: #fff;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;

          &:hover {
            background-color: #40a9ff;
          }
        }

        .btn-icon {
          font-size: 12px;
        }
      }
    }
  }

  // 树形视图样式
  .tree-view {
    flex: 1;
    overflow-y: auto;

    .tree-container {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;

      .tree-node {
        margin-bottom: 8px;

        &.child {
          margin-left: 24px;
          
          .node-content {
            background-color: #fafafa;
          }
        }

        .node-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border: 1px solid #e8e8e8;
          border-radius: 6px;
          background-color: #fff;

          .node-info {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1;

            .node-name {
              font-size: 14px;
              font-weight: 500;
              color: #333;
            }

            .node-code {
              font-size: 12px;
              color: #666;
              background-color: #f5f5f5;
              padding: 2px 8px;
              border-radius: 4px;
            }

            .node-type {
              font-size: 12px;
              color: #1890ff;
              background-color: #e6f7ff;
              padding: 2px 8px;
              border-radius: 4px;
            }

            .node-status {
              font-size: 12px;
              color: #999;
              background-color: #f5f5f5;
              padding: 2px 8px;
              border-radius: 4px;

              &.active {
                color: #52c41a;
                background-color: #f6ffed;
              }
            }
          }

          .node-actions {
            display: flex;
            gap: 8px;

            .node-btn {
              padding: 4px 8px;
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              background-color: #fff;
              color: #333;
              font-size: 12px;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                border-color: #1890ff;
                color: #1890ff;
              }

              &.danger {
                &:hover {
                  border-color: #ff4d4f;
                  color: #ff4d4f;
                }
              }
            }
          }
        }

        .tree-children {
          margin-top: 8px;
        }
      }
    }
  }

  // 列表视图样式
  .list-view {
    flex: 1;
  }

  // 弹窗样式
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background-color: #fff;
      border-radius: 8px;
      width: 90%;
      max-width: 600px;
      max-height: 80vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #e8e8e8;

        .modal-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .modal-close {
          cursor: pointer;
          font-size: 18px;
          color: #999;
          
          &:hover {
            color: #333;
          }
        }
      }

      .modal-body {
        flex: 1;
        padding: 24px;
        overflow-y: auto;
      }
    }
  }
}
</style>
