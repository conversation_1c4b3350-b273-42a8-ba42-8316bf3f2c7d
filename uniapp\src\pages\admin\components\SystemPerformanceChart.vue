<template>
  <view class="system-performance-chart">
    <BaseChart
      :type="chartType"
      :data="chartData"
      title="系统性能监控"
      :width="400"
      :height="300"
      :loading="loading"
      :show-legend="true"
      :options="chartOptions"
      @click="handleChartClick"
      @hover="handleChartHover"
    />
    
    <!-- 图表类型切换 -->
    <view class="chart-type-selector">
      <view 
        v-for="type in chartTypes" 
        :key="type.value"
        :class="['type-item', { active: chartType === type.value }]"
        @click="selectChartType(type.value)"
      >
        {{ type.label }}
      </view>
    </view>
    
    <!-- 性能指标详情 -->
    <view class="performance-details">
      <view 
        v-for="metric in performanceMetrics" 
        :key="metric.key"
        class="metric-item"
      >
        <view class="metric-name">{{ metric.name }}</view>
        <view class="metric-value" :class="getMetricStatus(metric.value, metric.threshold)">
          {{ metric.value }}{{ metric.unit }}
        </view>
        <view class="metric-status">
          <text :class="getMetricStatus(metric.value, metric.threshold)">
            {{ getStatusText(metric.value, metric.threshold) }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import BaseChart from './BaseChart.vue'
import { useDashboardStore } from '@/stores/dashboard'

interface PerformanceMetric {
  key: string
  name: string
  value: number
  unit: string
  threshold: number
  color: string
}

const dashboardStore = useDashboardStore()

// 响应式数据
const loading = ref(false)
const chartType = ref<'gauge' | 'bar' | 'line'>('gauge')
const performanceMetrics = ref<PerformanceMetric[]>([])

// 图表类型选项
const chartTypes = [
  { label: '仪表盘', value: 'gauge' },
  { label: '柱状图', value: 'bar' },
  { label: '折线图', value: 'line' }
]

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 计算属性
const chartData = computed(() => {
  if (chartType.value === 'gauge') {
    // 仪表盘显示CPU使用率
    const cpuMetric = performanceMetrics.value.find(m => m.key === 'cpu')
    return cpuMetric ? [{ label: 'CPU使用率', value: cpuMetric.value, color: cpuMetric.color }] : []
  } else {
    // 柱状图和折线图显示所有指标
    return performanceMetrics.value.map(metric => ({
      label: metric.name,
      value: metric.value,
      color: metric.color
    }))
  }
})

const chartOptions = computed(() => {
  if (chartType.value === 'gauge') {
    return { max: 100 }
  }
  return {}
})

// 方法
const loadPerformanceData = async () => {
  loading.value = true
  
  try {
    const data = await dashboardStore.fetchChartData('system-performance')
    
    if (data) {
      updatePerformanceMetrics(data)
    } else {
      // 使用模拟数据
      generateMockPerformanceData()
    }
  } catch (error) {
    console.error('加载系统性能数据失败:', error)
    generateMockPerformanceData()
  } finally {
    loading.value = false
  }
}

const updatePerformanceMetrics = (data: any) => {
  performanceMetrics.value = [
    {
      key: 'cpu',
      name: 'CPU使用率',
      value: data.cpuUsage || 0,
      unit: '%',
      threshold: 80,
      color: '#1890ff'
    },
    {
      key: 'memory',
      name: '内存使用率',
      value: data.memoryUsage || 0,
      unit: '%',
      threshold: 85,
      color: '#52c41a'
    },
    {
      key: 'disk',
      name: '磁盘使用率',
      value: data.diskUsage || 0,
      unit: '%',
      threshold: 90,
      color: '#faad14'
    },
    {
      key: 'network',
      name: '网络IO',
      value: data.networkIO || 0,
      unit: 'MB/s',
      threshold: 100,
      color: '#ff4d4f'
    }
  ]
}

const generateMockPerformanceData = () => {
  performanceMetrics.value = [
    {
      key: 'cpu',
      name: 'CPU使用率',
      value: Math.floor(Math.random() * 40) + 30, // 30-70%
      unit: '%',
      threshold: 80,
      color: '#1890ff'
    },
    {
      key: 'memory',
      name: '内存使用率',
      value: Math.floor(Math.random() * 30) + 50, // 50-80%
      unit: '%',
      threshold: 85,
      color: '#52c41a'
    },
    {
      key: 'disk',
      name: '磁盘使用率',
      value: Math.floor(Math.random() * 20) + 40, // 40-60%
      unit: '%',
      threshold: 90,
      color: '#faad14'
    },
    {
      key: 'network',
      name: '网络IO',
      value: Math.floor(Math.random() * 50) + 20, // 20-70 MB/s
      unit: 'MB/s',
      threshold: 100,
      color: '#ff4d4f'
    }
  ]
}

const selectChartType = (type: 'gauge' | 'bar' | 'line') => {
  chartType.value = type
}

const getMetricStatus = (value: number, threshold: number): string => {
  if (value >= threshold) return 'danger'
  if (value >= threshold * 0.8) return 'warning'
  return 'normal'
}

const getStatusText = (value: number, threshold: number): string => {
  if (value >= threshold) return '异常'
  if (value >= threshold * 0.8) return '警告'
  return '正常'
}

const handleChartClick = (item: any, index: number) => {
  const metric = performanceMetrics.value[index]
  if (metric) {
    const statusColor = getMetricStatus(metric.value, metric.threshold)
    const statusEmoji = statusColor === 'danger' ? '🔴' : statusColor === 'warning' ? '🟡' : '🟢'
    
    const content = [
      `${statusEmoji} 指标: ${metric.name}`,
      `📊 当前值: ${metric.value}${metric.unit}`,
      `⚠️ 阈值: ${metric.threshold}${metric.unit}`,
      `📈 使用率: ${((metric.value / metric.threshold) * 100).toFixed(1)}%`,
      `🔍 状态: ${getStatusText(metric.value, metric.threshold)}`
    ].join('\n')
    
    uni.showModal({
      title: '系统性能详情',
      content,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '查看历史',
      success: (res) => {
        if (res.confirm) {
          // 可以跳转到性能历史页面
          console.log('查看性能历史:', metric)
          uni.showToast({
            title: '历史数据功能开发中',
            icon: 'none'
          })
        }
      }
    })
  }
}

const handleChartHover = (item: any, index: number) => {
  const metric = performanceMetrics.value[index]
  if (metric) {
    // 可以在这里添加悬停效果
    console.log('悬停在性能指标:', metric)
  }
}

const startAutoRefresh = () => {
  // 每10秒刷新一次性能数据
  refreshTimer = setInterval(() => {
    loadPerformanceData()
  }, 10000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  loadPerformanceData()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 暴露刷新方法
const refresh = () => {
  loadPerformanceData()
}

defineExpose({
  refresh
})
</script>

<style lang="scss" scoped>
.system-performance-chart {
  .chart-type-selector {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 16px;

    .type-item {
      padding: 6px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 12px;
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.active {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;
      }
    }
  }

  .performance-details {
    margin-top: 16px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;

    .metric-item {
      background-color: #f8f9fa;
      border-radius: 6px;
      padding: 12px;
      text-align: center;

      .metric-name {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .metric-value {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 4px;

        &.normal {
          color: #52c41a;
        }

        &.warning {
          color: #faad14;
        }

        &.danger {
          color: #ff4d4f;
        }
      }

      .metric-status {
        font-size: 11px;

        .normal {
          color: #52c41a;
        }

        .warning {
          color: #faad14;
        }

        .danger {
          color: #ff4d4f;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .system-performance-chart {
    .performance-details {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      .metric-item {
        padding: 8px;

        .metric-value {
          font-size: 16px;
        }
      }
    }
  }
}

// 暗色主题支持
.admin-layout.dark-theme .system-performance-chart {
  .chart-type-selector {
    .type-item {
      border-color: #434343;
      color: #ccc;
      background-color: #1f1f1f;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.active {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;
      }
    }
  }

  .performance-details {
    .metric-item {
      background-color: #2a2a2a;

      .metric-name {
        color: #999;
      }
    }
  }
}
</style>