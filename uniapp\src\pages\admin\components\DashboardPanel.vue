<template>
  <view class="dashboard-panel">
    <!-- 仪表板头部 -->
    <view class="dashboard-header">
      <view class="header-title">数据概览</view>
      <view class="header-actions">
        <view class="refresh-btn" @click="handleRefresh" :class="{ loading: loading }">
          <text class="iconfont icon-refresh"></text>
          <text>刷新</text>
        </view>
      </view>
    </view>

    <!-- 关键指标卡片网格 -->
    <view class="metrics-grid">
      <view 
        v-for="metric in metricsData" 
        :key="metric.key"
        class="metric-card"
        @click="handleMetricClick(metric)"
      >
        <view :class="['metric-icon', metric.iconType]">
          <text class="iconfont" :class="metric.icon"></text>
        </view>
        <view class="metric-value">{{ formatMetricValue(metric.value) }}</view>
        <view class="metric-label">{{ metric.label }}</view>
        <view 
          v-if="metric.change"
          :class="['metric-change', metric.change > 0 ? 'positive' : 'negative']"
        >
          <text class="change-icon">{{ metric.change > 0 ? '↗' : '↘' }}</text>
          <text>{{ Math.abs(metric.change) }}%</text>
        </view>
      </view>
    </view>

    <!-- 图表展示区域 -->
    <view class="charts-section">
      <view class="section-header">
        <view class="section-title">数据可视化</view>
        <view class="section-actions">
          <view class="action-btn" @click="toggleChartView">
            <text class="iconfont" :class="showAdvancedCharts ? 'icon-grid' : 'icon-bar-chart'"></text>
            <text>{{ showAdvancedCharts ? '简化视图' : '高级视图' }}</text>
          </view>
        </view>
      </view>
      
      <!-- 简化图表视图 -->
      <view v-if="!showAdvancedCharts" class="charts-grid">
        <view class="chart-card">
          <UserActivityChart ref="userActivityChartRef" />
        </view>
        
        <view class="chart-card">
          <SystemPerformanceChart ref="systemPerformanceChartRef" />
        </view>
        
        <view class="chart-card">
          <BusinessAnalyticsChart ref="businessAnalyticsChartRef" />
        </view>
        
        <view class="chart-card">
          <RealTimeMonitorChart ref="realTimeMonitorChartRef" />
        </view>
      </view>
      
      <!-- 高级图表展示 -->
      <view v-else class="advanced-charts">
        <ChartShowcase ref="chartShowcaseRef" />
      </view>
    </view>

    <!-- 最近活动 -->
    <view class="activity-section">
      <view class="section-title">最近活动</view>
      <view class="activity-list">
        <view 
          v-for="activity in recentActivities" 
          :key="activity.id"
          class="activity-item"
        >
          <view class="activity-icon">
            <text class="iconfont" :class="getActivityIcon(activity.type)"></text>
          </view>
          <view class="activity-content">
            <view class="activity-description">{{ activity.description }}</view>
            <view class="activity-time">{{ formatTime(activity.createTime) }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUIStore } from '@/stores/ui'
import { useDashboardStore } from '@/stores/dashboard'
import type { DashboardMetrics, Activity } from '../types/admin'
import UserActivityChart from './UserActivityChart.vue'
import SystemPerformanceChart from './SystemPerformanceChart.vue'
import BusinessAnalyticsChart from './BusinessAnalyticsChart.vue'
import RealTimeMonitorChart from './RealTimeMonitorChart.vue'
import ChartShowcase from './ChartShowcase.vue'

const uiStore = useUIStore()
const dashboardStore = useDashboardStore()

// 组件引用
const userActivityChartRef = ref()
const systemPerformanceChartRef = ref()
const businessAnalyticsChartRef = ref()
const realTimeMonitorChartRef = ref()
const chartShowcaseRef = ref()

// 视图状态
const showAdvancedCharts = ref(false)

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 计算属性
const loading = computed(() => dashboardStore.loading)

const metricsData = computed(() => {
  const metrics = dashboardStore.metrics
  if (!metrics) {
    return getMockMetricsData()
  }
  
  return [
    {
      key: 'totalUsers',
      label: '总用户数',
      value: metrics.totalUsers || 0,
      icon: 'icon-user',
      iconType: 'primary',
      change: 12.5
    },
    {
      key: 'activeUsers',
      label: '活跃用户',
      value: metrics.activeUsers || 0,
      icon: 'icon-user-check',
      iconType: 'success',
      change: 8.2
    },
    {
      key: 'systemLoad',
      label: '系统负载',
      value: metrics.systemLoad || 0,
      icon: 'icon-cpu',
      iconType: 'warning',
      change: -2.1
    },
    {
      key: 'errorRate',
      label: '错误率',
      value: metrics.errorRate || 0,
      icon: 'icon-warning',
      iconType: 'danger',
      change: -15.3
    }
  ]
})

const recentActivities = computed(() => {
  return dashboardStore.recentActivities.length > 0 
    ? dashboardStore.recentActivities 
    : getMockActivities()
})

// 方法
const loadDashboardData = async () => {
  try {
    // 检查是否需要刷新数据
    if (dashboardStore.shouldRefresh()) {
      await dashboardStore.fetchDashboardData()
    }
    
    // 如果没有数据，使用模拟数据
    if (!dashboardStore.hasData) {
      loadMockData()
    }
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    loadMockData()
  }
}

const loadMockData = () => {
  // 设置模拟指标数据
  dashboardStore.setMetrics({
    totalUsers: 12580,
    activeUsers: 8420,
    systemLoad: 65.8,
    memoryUsage: 72.3,
    diskUsage: 45.6,
    errorRate: 0.12,
    responseTime: 245
  })
  
  // 设置模拟活动数据
  dashboardStore.setRecentActivities(getMockActivities())
}

const getMockMetricsData = () => [
  {
    key: 'totalUsers',
    label: '总用户数',
    value: 12580,
    icon: 'icon-user',
    iconType: 'primary',
    change: 12.5
  },
  {
    key: 'activeUsers',
    label: '活跃用户',
    value: 8420,
    icon: 'icon-user-check',
    iconType: 'success',
    change: 8.2
  },
  {
    key: 'systemLoad',
    label: '系统负载',
    value: 65.8,
    icon: 'icon-cpu',
    iconType: 'warning',
    change: -2.1
  },
  {
    key: 'errorRate',
    label: '错误率',
    value: 0.12,
    icon: 'icon-warning',
    iconType: 'danger',
    change: -15.3
  }
]

const getMockActivities = (): Activity[] => [
  {
    id: 1,
    type: 'login',
    description: '管理员 admin 登录系统',
    user: 'admin',
    createTime: new Date().toISOString()
  },
  {
    id: 2,
    type: 'create',
    description: '创建了新用户 testuser',
    user: 'admin',
    createTime: new Date(Date.now() - 300000).toISOString()
  },
  {
    id: 3,
    type: 'update',
    description: '更新了系统配置',
    user: 'admin',
    createTime: new Date(Date.now() - 600000).toISOString()
  },
  {
    id: 4,
    type: 'delete',
    description: '删除了过期数据',
    user: 'system',
    createTime: new Date(Date.now() - 900000).toISOString()
  }
]

const formatMetricValue = (value: number): string => {
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + 'w'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'k'
  } else if (value < 1) {
    return (value * 100).toFixed(2) + '%'
  }
  return value.toString()
}

const formatTime = (timeStr: string): string => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return Math.floor(diff / 60000) + '分钟前'
  } else if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前'
  } else {
    return time.toLocaleDateString()
  }
}

const getActivityIcon = (type: string): string => {
  const iconMap = {
    login: 'icon-login',
    create: 'icon-plus',
    update: 'icon-edit',
    delete: 'icon-delete',
    system: 'icon-settings'
  }
  return iconMap[type] || 'icon-info'
}

const handleMetricClick = (metric: any) => {
  console.log('点击指标:', metric.key)
  // 可以在这里添加跳转到详细页面的逻辑
  uni.showToast({
    title: `查看${metric.label}详情`,
    icon: 'none'
  })
}

const toggleChartView = () => {
  showAdvancedCharts.value = !showAdvancedCharts.value
  
  uni.showToast({
    title: showAdvancedCharts.value ? '切换到高级视图' : '切换到简化视图',
    icon: 'none'
  })
}

const startAutoRefresh = () => {
  // 每30秒自动刷新数据
  refreshTimer = setInterval(() => {
    if (dashboardStore.shouldRefresh(30000)) { // 30秒检查一次
      dashboardStore.refresh()
    }
  }, 30000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 手动刷新
const handleRefresh = async () => {
  try {
    await dashboardStore.refresh()
    
    // 刷新图表组件
    if (showAdvancedCharts.value) {
      // 高级视图 - 刷新图表展示组件
      if (chartShowcaseRef.value?.refreshAllCharts) {
        chartShowcaseRef.value.refreshAllCharts()
      }
    } else {
      // 简化视图 - 刷新各个图表组件
      const refreshPromises = []
      
      if (userActivityChartRef.value?.refresh) {
        refreshPromises.push(userActivityChartRef.value.refresh())
      }
      if (systemPerformanceChartRef.value?.refresh) {
        refreshPromises.push(systemPerformanceChartRef.value.refresh())
      }
      if (businessAnalyticsChartRef.value?.refresh) {
        refreshPromises.push(businessAnalyticsChartRef.value.refresh())
      }
      if (realTimeMonitorChartRef.value?.refresh) {
        refreshPromises.push(realTimeMonitorChartRef.value.refresh())
      }
      
      await Promise.all(refreshPromises)
    }
    
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('刷新失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 暴露方法给父组件
defineExpose({
  refresh: handleRefresh,
  loadData: loadDashboardData
})
</script>

<style lang="scss" scoped>
@import '../styles/dashboard-panel.scss';

.charts-section {
  margin-bottom: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .section-actions {
      .action-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 12px;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;

        .iconfont {
          font-size: 14px;
        }

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }
  }

  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 16px;

    .chart-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .advanced-charts {
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }

  .loading-text {
    font-size: 14px;
    color: #666;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 4px;
  color: #6c757d;
  font-size: 14px;
}

// 暗色主题支持
.admin-layout.dark-theme .dashboard-panel {
  .dashboard-header {
    .header-title {
      color: #fff;
    }

    .refresh-btn {
      background-color: #1f1f1f;
      border-color: #434343;
      color: #ccc;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }

  .metric-card,
  .chart-card,
  .activity-section {
    background-color: #1f1f1f;
    color: #fff;
  }

  .metric-label,
  .activity-time {
    color: #999;
  }

  .chart-placeholder {
    background-color: #2a2a2a;
    border-color: #444;
    color: #999;
  }

  .loading-overlay {
    background-color: rgba(31, 31, 31, 0.8);
  }
}
</style>