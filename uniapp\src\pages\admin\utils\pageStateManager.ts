// 页面状态管理工具
export interface PageStateData {
  scrollTop: number
  formData: Record<string, any>
  selectedItems: string[]
  filters: Record<string, any>
  searchKeyword: string
  sortConfig: {
    field: string
    order: 'asc' | 'desc'
  }
  pagination: {
    current: number
    pageSize: number
    total: number
  }
  timestamp: number
}

// 默认页面状态
const defaultPageState: PageStateData = {
  scrollTop: 0,
  formData: {},
  selectedItems: [],
  filters: {},
  searchKeyword: '',
  sortConfig: {
    field: '',
    order: 'asc'
  },
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0
  },
  timestamp: Date.now()
}

// 页面状态存储键前缀
const STORAGE_PREFIX = 'admin_page_state_'

// 状态过期时间（30分钟）
const STATE_EXPIRE_TIME = 30 * 60 * 1000

export class PageStateManager {
  private pageKey: string
  private state: PageStateData

  constructor(pageKey: string) {
    this.pageKey = pageKey
    this.state = this.loadState()
  }

  // 从存储中加载状态
  private loadState(): PageStateData {
    try {
      const storageKey = STORAGE_PREFIX + this.pageKey
      const savedState = uni.getStorageSync(storageKey)
      
      if (savedState && typeof savedState === 'object') {
        // 检查状态是否过期
        const now = Date.now()
        if (now - savedState.timestamp < STATE_EXPIRE_TIME) {
          return { ...defaultPageState, ...savedState }
        }
      }
    } catch (error) {
      console.warn('加载页面状态失败:', error)
    }
    
    return { ...defaultPageState }
  }

  // 保存状态到存储
  private saveState(): void {
    try {
      const storageKey = STORAGE_PREFIX + this.pageKey
      this.state.timestamp = Date.now()
      uni.setStorageSync(storageKey, this.state)
    } catch (error) {
      console.warn('保存页面状态失败:', error)
    }
  }

  // 获取滚动位置
  getScrollTop(): number {
    return this.state.scrollTop
  }

  // 设置滚动位置
  setScrollTop(scrollTop: number): void {
    this.state.scrollTop = scrollTop
    this.saveState()
  }

  // 获取表单数据
  getFormData(): Record<string, any> {
    return { ...this.state.formData }
  }

  // 设置表单数据
  setFormData(formData: Record<string, any>): void {
    this.state.formData = { ...formData }
    this.saveState()
  }

  // 更新表单字段
  updateFormField(field: string, value: any): void {
    this.state.formData[field] = value
    this.saveState()
  }

  // 清空表单数据
  clearFormData(): void {
    this.state.formData = {}
    this.saveState()
  }

  // 获取选中项
  getSelectedItems(): string[] {
    return [...this.state.selectedItems]
  }

  // 设置选中项
  setSelectedItems(items: string[]): void {
    this.state.selectedItems = [...items]
    this.saveState()
  }

  // 添加选中项
  addSelectedItem(item: string): void {
    if (!this.state.selectedItems.includes(item)) {
      this.state.selectedItems.push(item)
      this.saveState()
    }
  }

  // 移除选中项
  removeSelectedItem(item: string): void {
    const index = this.state.selectedItems.indexOf(item)
    if (index > -1) {
      this.state.selectedItems.splice(index, 1)
      this.saveState()
    }
  }

  // 清空选中项
  clearSelectedItems(): void {
    this.state.selectedItems = []
    this.saveState()
  }

  // 获取过滤条件
  getFilters(): Record<string, any> {
    return { ...this.state.filters }
  }

  // 设置过滤条件
  setFilters(filters: Record<string, any>): void {
    this.state.filters = { ...filters }
    this.saveState()
  }

  // 更新过滤字段
  updateFilter(field: string, value: any): void {
    this.state.filters[field] = value
    this.saveState()
  }

  // 清空过滤条件
  clearFilters(): void {
    this.state.filters = {}
    this.saveState()
  }

  // 获取搜索关键词
  getSearchKeyword(): string {
    return this.state.searchKeyword
  }

  // 设置搜索关键词
  setSearchKeyword(keyword: string): void {
    this.state.searchKeyword = keyword
    this.saveState()
  }

  // 获取排序配置
  getSortConfig(): { field: string; order: 'asc' | 'desc' } {
    return { ...this.state.sortConfig }
  }

  // 设置排序配置
  setSortConfig(field: string, order: 'asc' | 'desc'): void {
    this.state.sortConfig = { field, order }
    this.saveState()
  }

  // 获取分页信息
  getPagination(): { current: number; pageSize: number; total: number } {
    return { ...this.state.pagination }
  }

  // 设置分页信息
  setPagination(pagination: Partial<{ current: number; pageSize: number; total: number }>): void {
    this.state.pagination = { ...this.state.pagination, ...pagination }
    this.saveState()
  }

  // 重置分页到第一页
  resetPagination(): void {
    this.state.pagination.current = 1
    this.saveState()
  }

  // 获取完整状态
  getState(): PageStateData {
    return { ...this.state }
  }

  // 重置所有状态
  resetState(): void {
    this.state = { ...defaultPageState }
    this.saveState()
  }

  // 清除存储的状态
  clearStorage(): void {
    try {
      const storageKey = STORAGE_PREFIX + this.pageKey
      uni.removeStorageSync(storageKey)
    } catch (error) {
      console.warn('清除页面状态失败:', error)
    }
  }

  // 检查状态是否过期
  isExpired(): boolean {
    const now = Date.now()
    return now - this.state.timestamp > STATE_EXPIRE_TIME
  }
}

// 全局状态管理器
export class GlobalPageStateManager {
  // 清理所有过期状态
  static cleanupExpiredStates(): void {
    try {
      const storageInfo = uni.getStorageInfoSync()
      const now = Date.now()
      
      storageInfo.keys.forEach(key => {
        if (key.startsWith(STORAGE_PREFIX)) {
          try {
            const state = uni.getStorageSync(key)
            if (state && typeof state === 'object' && state.timestamp) {
              if (now - state.timestamp > STATE_EXPIRE_TIME) {
                uni.removeStorageSync(key)
              }
            }
          } catch (error) {
            // 如果读取失败，直接删除
            uni.removeStorageSync(key)
          }
        }
      })
    } catch (error) {
      console.warn('清理过期状态失败:', error)
    }
  }

  // 清理所有页面状态
  static clearAllStates(): void {
    try {
      const storageInfo = uni.getStorageInfoSync()
      
      storageInfo.keys.forEach(key => {
        if (key.startsWith(STORAGE_PREFIX)) {
          uni.removeStorageSync(key)
        }
      })
    } catch (error) {
      console.warn('清理所有状态失败:', error)
    }
  }

  // 获取状态统计信息
  static getStateStats(): { total: number; expired: number; size: string } {
    let total = 0
    let expired = 0
    let totalSize = 0
    
    try {
      const storageInfo = uni.getStorageInfoSync()
      const now = Date.now()
      
      storageInfo.keys.forEach(key => {
        if (key.startsWith(STORAGE_PREFIX)) {
          total++
          try {
            const state = uni.getStorageSync(key)
            if (state && typeof state === 'object' && state.timestamp) {
              if (now - state.timestamp > STATE_EXPIRE_TIME) {
                expired++
              }
              // 估算大小
              totalSize += JSON.stringify(state).length
            }
          } catch (error) {
            expired++
          }
        }
      })
    } catch (error) {
      console.warn('获取状态统计失败:', error)
    }
    
    return {
      total,
      expired,
      size: `${(totalSize / 1024).toFixed(2)} KB`
    }
  }
}

// 创建页面状态管理器的工厂函数
export function createPageStateManager(pageKey: string): PageStateManager {
  return new PageStateManager(pageKey)
}
