package com.kibi.entity;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.kibi.config.JsonArrayTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class Role {

    @TableId(value = "user_id", type = IdType.INPUT)
    private Integer userId;

    @TableField(typeHandler = JsonArrayTypeHandler.class)
    private JSONArray traits;

    private String description;
    private Integer spontaneous;
    private Integer collaborative;
    private Integer realist;
    private Integer logical;
    private Integer analytical;
    private Integer introvert;
}
