<template>
  <view class="global-ui-container">
    <!-- Toast管理器 -->
    <ToastManager ref="toastManagerRef" />
    
    <!-- 确认对话框 -->
    <ConfirmDialog 
      v-model:visible="uiState.confirmDialog.visible"
      :options="uiState.confirmDialog.options"
      @confirm="handleConfirmDialogConfirm"
      @cancel="handleConfirmDialogCancel"
    />
    
    <!-- 全局加载指示器 -->
    <LoadingIndicator
      :visible="uiState.loading.visible"
      :message="uiState.loading.message"
      :progress="uiState.loading.progress"
      :show-progress="uiState.loading.progress !== undefined"
      :cancelable="false"
      type="spinner"
      size="medium"
    />
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import ToastManager from './ToastManager.vue'
import ConfirmDialog from './ConfirmDialog.vue'
import LoadingIndicator from './LoadingIndicator.vue'
import { globalUIManager } from '../../composables/useUIManager'

// 组件引用
const toastManagerRef = ref<InstanceType<typeof ToastManager> | null>(null)

// 使用全局UI管理器
const { uiState } = globalUIManager

// 生命周期
onMounted(() => {
  // 设置Toast管理器引用
  if (toastManagerRef.value) {
    globalUIManager.setToastManager(toastManagerRef.value)
  }
})

// 事件处理
const handleConfirmDialogConfirm = (inputValue?: string) => {
  // 由UI管理器处理
}

const handleConfirmDialogCancel = () => {
  // 由UI管理器处理
}
</script>

<style lang="scss" scoped>
.global-ui-container {
  // 这个容器不需要样式，只是用来承载全局UI组件
}
</style>
