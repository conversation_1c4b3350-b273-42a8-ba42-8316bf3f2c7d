<template>
  <view class="toast-manager">
    <!-- 自定义Toast列表 -->
    <view 
      v-for="toast in toasts" 
      :key="toast.id"
      class="custom-toast"
      :class="[
        `toast-${toast.type}`,
        { 'toast-show': toast.visible }
      ]"
      :style="{ zIndex: 20000 + toast.id }"
    >
      <view class="toast-content">
        <view class="toast-icon">
          {{ getToastIcon(toast.type) }}
        </view>
        <view class="toast-message">{{ toast.message }}</view>
        <view 
          v-if="toast.closable"
          class="toast-close"
          @click="removeToast(toast.id)"
        >
          ×
        </view>
      </view>
      
      <!-- 进度条 -->
      <view 
        v-if="toast.showProgress"
        class="toast-progress"
      >
        <view 
          class="toast-progress-bar"
          :style="{ 
            width: `${toast.progress}%`,
            backgroundColor: getProgressColor(toast.type)
          }"
        ></view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

export interface ToastOptions {
  type?: 'success' | 'error' | 'warning' | 'info' | 'loading'
  message: string
  duration?: number
  closable?: boolean
  showProgress?: boolean
  position?: 'top' | 'center' | 'bottom'
  onClose?: () => void
}

interface Toast extends ToastOptions {
  id: number
  visible: boolean
  progress: number
  timer?: number
  progressTimer?: number
}

const toasts = ref<Toast[]>([])
let toastIdCounter = 0

/**
 * 显示Toast
 */
const showToast = (options: ToastOptions): number => {
  const toast: Toast = {
    id: ++toastIdCounter,
    type: options.type || 'info',
    message: options.message,
    duration: options.duration || 3000,
    closable: options.closable || false,
    showProgress: options.showProgress || false,
    position: options.position || 'center',
    visible: false,
    progress: 100,
    onClose: options.onClose
  }
  
  toasts.value.push(toast)
  
  // 延迟显示以触发动画
  setTimeout(() => {
    toast.visible = true
  }, 50)
  
  // 设置自动关闭
  if (toast.duration > 0 && toast.type !== 'loading') {
    toast.timer = setTimeout(() => {
      removeToast(toast.id)
    }, toast.duration)
    
    // 进度条动画
    if (toast.showProgress) {
      const progressInterval = 50
      const progressStep = (progressInterval / toast.duration) * 100
      
      toast.progressTimer = setInterval(() => {
        toast.progress -= progressStep
        if (toast.progress <= 0) {
          toast.progress = 0
          clearInterval(toast.progressTimer)
        }
      }, progressInterval)
    }
  }
  
  return toast.id
}

/**
 * 移除Toast
 */
const removeToast = (id: number) => {
  const index = toasts.value.findIndex(t => t.id === id)
  if (index > -1) {
    const toast = toasts.value[index]
    
    // 清除定时器
    if (toast.timer) {
      clearTimeout(toast.timer)
    }
    if (toast.progressTimer) {
      clearInterval(toast.progressTimer)
    }
    
    // 隐藏动画
    toast.visible = false
    
    // 延迟移除以完成动画
    setTimeout(() => {
      const currentIndex = toasts.value.findIndex(t => t.id === id)
      if (currentIndex > -1) {
        const removedToast = toasts.value.splice(currentIndex, 1)[0]
        if (removedToast.onClose) {
          removedToast.onClose()
        }
      }
    }, 300)
  }
}

/**
 * 清除所有Toast
 */
const clearAllToasts = () => {
  toasts.value.forEach(toast => {
    if (toast.timer) clearTimeout(toast.timer)
    if (toast.progressTimer) clearInterval(toast.progressTimer)
  })
  toasts.value = []
}

/**
 * 获取Toast图标
 */
const getToastIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️',
    loading: '⏳'
  }
  return iconMap[type] || 'ℹ️'
}

/**
 * 获取进度条颜色
 */
const getProgressColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    success: '#52c41a',
    error: '#ff4d4f',
    warning: '#faad14',
    info: '#1890ff',
    loading: '#1890ff'
  }
  return colorMap[type] || '#1890ff'
}

// 便捷方法
const success = (message: string, options?: Partial<ToastOptions>) => {
  return showToast({ ...options, type: 'success', message })
}

const error = (message: string, options?: Partial<ToastOptions>) => {
  return showToast({ ...options, type: 'error', message })
}

const warning = (message: string, options?: Partial<ToastOptions>) => {
  return showToast({ ...options, type: 'warning', message })
}

const info = (message: string, options?: Partial<ToastOptions>) => {
  return showToast({ ...options, type: 'info', message })
}

const loading = (message: string = '加载中...', options?: Partial<ToastOptions>) => {
  return showToast({ ...options, type: 'loading', message, duration: 0 })
}

// 暴露方法
defineExpose({
  showToast,
  removeToast,
  clearAllToasts,
  success,
  error,
  warning,
  info,
  loading
})
</script>

<style lang="scss" scoped>
.toast-manager {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 20000;
}

.custom-toast {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  min-width: 200px;
  max-width: 80%;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 0;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: auto;
  backdrop-filter: blur(10px);
  
  &.toast-show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  
  &:not(.toast-show) {
    transform: translate(-50%, -50%) scale(0.8);
  }
  
  // 不同类型的样式
  &.toast-success {
    background: rgba(82, 196, 26, 0.9);
    color: white;
  }
  
  &.toast-error {
    background: rgba(255, 77, 79, 0.9);
    color: white;
  }
  
  &.toast-warning {
    background: rgba(250, 173, 20, 0.9);
    color: white;
  }
  
  &.toast-info {
    background: rgba(24, 144, 255, 0.9);
    color: white;
  }
  
  &.toast-loading {
    background: rgba(0, 0, 0, 0.8);
    color: white;
  }
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 8px;
}

.toast-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
}

.toast-close {
  font-size: 18px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
  
  &:hover {
    opacity: 1;
  }
}

.toast-progress {
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  overflow: hidden;
  border-radius: 0 0 8px 8px;
}

.toast-progress-bar {
  height: 100%;
  transition: width 0.1s linear;
  border-radius: 0 0 8px 8px;
}

// 响应式适配
@media (max-width: 768px) {
  .custom-toast {
    max-width: 90%;
    min-width: 280px;
  }
  
  .toast-content {
    padding: 10px 14px;
  }
  
  .toast-message {
    font-size: 13px;
  }
}
</style>
