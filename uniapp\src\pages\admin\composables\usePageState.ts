import { ref, reactive, computed } from 'vue'

// 页面状态接口
interface PageState {
  scrollTop: number
  formData: Record<string, any>
  selectedItems: string[]
  filters: Record<string, any>
  pagination: {
    current: number
    pageSize: number
    total: number
  }
  lastUpdateTime: number
}

// 页面状态缓存
const pageStateCache = reactive<Record<string, PageState>>({})

// 页面状态管理
export function usePageState(pageKey: string) {
  // 默认页面状态
  const defaultState: PageState = {
    scrollTop: 0,
    formData: {},
    selectedItems: [],
    filters: {},
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0
    },
    lastUpdateTime: Date.now()
  }
  
  // 获取或创建页面状态
  const getPageState = (): PageState => {
    if (!pageStateCache[pageKey]) {
      pageStateCache[pageKey] = { ...defaultState }
    }
    return pageStateCache[pageKey]
  }
  
  // 当前页面状态
  const pageState = computed(() => getPageState())
  
  // 保存滚动位置
  const saveScrollPosition = (scrollTop: number) => {
    const state = getPageState()
    state.scrollTop = scrollTop
    state.lastUpdateTime = Date.now()
  }
  
  // 恢复滚动位置
  const restoreScrollPosition = () => {
    const state = getPageState()
    return state.scrollTop
  }
  
  // 保存表单数据
  const saveFormData = (formData: Record<string, any>) => {
    const state = getPageState()
    state.formData = { ...formData }
    state.lastUpdateTime = Date.now()
  }
  
  // 获取表单数据
  const getFormData = () => {
    const state = getPageState()
    return state.formData
  }
  
  // 清空表单数据
  const clearFormData = () => {
    const state = getPageState()
    state.formData = {}
    state.lastUpdateTime = Date.now()
  }
  
  // 保存选中项
  const saveSelectedItems = (items: string[]) => {
    const state = getPageState()
    state.selectedItems = [...items]
    state.lastUpdateTime = Date.now()
  }
  
  // 获取选中项
  const getSelectedItems = () => {
    const state = getPageState()
    return state.selectedItems
  }
  
  // 清空选中项
  const clearSelectedItems = () => {
    const state = getPageState()
    state.selectedItems = []
    state.lastUpdateTime = Date.now()
  }
  
  // 保存过滤条件
  const saveFilters = (filters: Record<string, any>) => {
    const state = getPageState()
    state.filters = { ...filters }
    state.lastUpdateTime = Date.now()
  }
  
  // 获取过滤条件
  const getFilters = () => {
    const state = getPageState()
    return state.filters
  }
  
  // 清空过滤条件
  const clearFilters = () => {
    const state = getPageState()
    state.filters = {}
    state.lastUpdateTime = Date.now()
  }
  
  // 保存分页信息
  const savePagination = (pagination: Partial<PageState['pagination']>) => {
    const state = getPageState()
    state.pagination = { ...state.pagination, ...pagination }
    state.lastUpdateTime = Date.now()
  }
  
  // 获取分页信息
  const getPagination = () => {
    const state = getPageState()
    return state.pagination
  }
  
  // 重置分页
  const resetPagination = () => {
    const state = getPageState()
    state.pagination = {
      current: 1,
      pageSize: 20,
      total: 0
    }
    state.lastUpdateTime = Date.now()
  }
  
  // 清空页面状态
  const clearPageState = () => {
    pageStateCache[pageKey] = { ...defaultState }
  }
  
  // 检查状态是否过期
  const isStateExpired = (maxAge = 30 * 60 * 1000): boolean => {
    const state = getPageState()
    return Date.now() - state.lastUpdateTime > maxAge
  }
  
  // 自动清理过期状态
  const cleanupExpiredStates = () => {
    const now = Date.now()
    const maxAge = 30 * 60 * 1000 // 30分钟
    
    Object.keys(pageStateCache).forEach(key => {
      if (now - pageStateCache[key].lastUpdateTime > maxAge) {
        delete pageStateCache[key]
      }
    })
  }
  
  // 获取所有页面状态键
  const getAllPageKeys = () => {
    return Object.keys(pageStateCache)
  }
  
  // 获取页面状态大小（用于调试）
  const getStateCacheSize = () => {
    return Object.keys(pageStateCache).length
  }
  
  return {
    // 状态
    pageState,
    
    // 滚动位置
    saveScrollPosition,
    restoreScrollPosition,
    
    // 表单数据
    saveFormData,
    getFormData,
    clearFormData,
    
    // 选中项
    saveSelectedItems,
    getSelectedItems,
    clearSelectedItems,
    
    // 过滤条件
    saveFilters,
    getFilters,
    clearFilters,
    
    // 分页信息
    savePagination,
    getPagination,
    resetPagination,
    
    // 状态管理
    clearPageState,
    isStateExpired,
    cleanupExpiredStates,
    getAllPageKeys,
    getStateCacheSize
  }
}

// 全局状态清理
export function useGlobalPageStateCleanup() {
  // 定期清理过期状态
  const startCleanupTimer = () => {
    return setInterval(() => {
      const now = Date.now()
      const maxAge = 30 * 60 * 1000 // 30分钟
      
      Object.keys(pageStateCache).forEach(key => {
        if (now - pageStateCache[key].lastUpdateTime > maxAge) {
          delete pageStateCache[key]
        }
      })
    }, 5 * 60 * 1000) // 每5分钟清理一次
  }
  
  // 清空所有状态
  const clearAllStates = () => {
    Object.keys(pageStateCache).forEach(key => {
      delete pageStateCache[key]
    })
  }
  
  // 获取状态统计
  const getStateStats = () => {
    const keys = Object.keys(pageStateCache)
    const now = Date.now()
    
    return {
      total: keys.length,
      active: keys.filter(key => now - pageStateCache[key].lastUpdateTime < 5 * 60 * 1000).length,
      expired: keys.filter(key => now - pageStateCache[key].lastUpdateTime > 30 * 60 * 1000).length
    }
  }
  
  return {
    startCleanupTimer,
    clearAllStates,
    getStateStats
  }
}
