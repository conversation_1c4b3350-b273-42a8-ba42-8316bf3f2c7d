<template>
  <view class="advanced-chart">
    <!-- 图表控制面板 -->
    <view class="chart-controls">
      <view class="control-row">
        <view class="control-group">
          <text class="control-label">图表类型:</text>
          <view class="chart-type-selector">
            <view 
              v-for="type in chartTypes" 
              :key="type.value"
              :class="['type-btn', { active: selectedType === type.value }]"
              @click="selectChartType(type.value)"
            >
              <text class="iconfont" :class="type.icon"></text>
              <text>{{ type.label }}</text>
            </view>
          </view>
        </view>
        
        <view class="control-group">
          <text class="control-label">数据处理:</text>
          <view class="data-processing">
            <view 
              :class="['process-btn', { active: enableSmoothing }]"
              @click="toggleSmoothing"
            >
              <text class="iconfont icon-filter"></text>
              <text>平滑</text>
            </view>
            <view 
              :class="['process-btn', { active: showTrend }]"
              @click="toggleTrend"
            >
              <text class="iconfont icon-trending-up"></text>
              <text>趋势</text>
            </view>
            <view 
              :class="['process-btn', { active: showPrediction }]"
              @click="togglePrediction"
            >
              <text class="iconfont icon-crystal-ball"></text>
              <text>预测</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="control-row">
        <view class="control-group">
          <text class="control-label">自动刷新:</text>
          <view class="auto-refresh">
            <view 
              :class="['refresh-toggle', { active: autoRefresh }]"
              @click="toggleAutoRefresh"
            >
              <text class="iconfont" :class="autoRefresh ? 'icon-pause' : 'icon-play'"></text>
              <text>{{ autoRefresh ? '暂停' : '开始' }}</text>
            </view>
            <view class="refresh-interval">
              <text>间隔:</text>
              <picker 
                :value="refreshIntervalIndex" 
                :range="refreshIntervals"
                range-key="label"
                @change="onRefreshIntervalChange"
              >
                <view class="interval-display">
                  {{ refreshIntervals[refreshIntervalIndex].label }}
                  <text class="iconfont icon-down"></text>
                </view>
              </picker>
            </view>
          </view>
        </view>
        
        <view class="control-group">
          <text class="control-label">数据操作:</text>
          <view class="data-actions">
            <view class="action-btn" @click="exportData">
              <text class="iconfont icon-download"></text>
              <text>导出</text>
            </view>
            <view class="action-btn" @click="resetZoom">
              <text class="iconfont icon-zoom-out"></text>
              <text>重置</text>
            </view>
            <view class="action-btn" @click="refreshData">
              <text class="iconfont icon-refresh" :class="{ spinning: loading }"></text>
              <text>刷新</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主图表区域 -->
    <view class="chart-container">
      <BaseChart
        :type="selectedType"
        :data="processedChartData"
        :title="chartTitle"
        :width="chartWidth"
        :height="chartHeight"
        :loading="loading"
        :show-legend="true"
        :options="chartOptions"
        @click="handleChartClick"
        @hover="handleChartHover"
        ref="baseChart"
      />
      
      <!-- 数据统计信息 -->
      <view class="chart-stats">
        <view class="stat-item">
          <text class="stat-label">数据点</text>
          <text class="stat-value">{{ rawData.length }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">最大值</text>
          <text class="stat-value">{{ formatValue(statistics.max) }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">平均值</text>
          <text class="stat-value">{{ formatValue(statistics.avg) }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">趋势</text>
          <text :class="['stat-value', `trend-${trendAnalysis.trend}`]">
            <text class="iconfont" :class="getTrendIcon(trendAnalysis.trend)"></text>
            {{ getTrendText(trendAnalysis.trend) }}
          </text>
        </view>
      </view>
    </view>

    <!-- 高级功能面板 -->
    <view class="advanced-features">
      <!-- 数据过滤器 -->
      <view class="feature-section">
        <view class="section-header">
          <text class="section-title">数据过滤</text>
          <view 
            :class="['section-toggle', { active: showFilters }]"
            @click="showFilters = !showFilters"
          >
            <text class="iconfont" :class="showFilters ? 'icon-up' : 'icon-down'"></text>
          </view>
        </view>
        
        <view v-if="showFilters" class="section-content">
          <view class="filter-row">
            <text class="filter-label">数值范围:</text>
            <view class="range-filter">
              <input 
                type="number" 
                v-model.number="valueFilter.min" 
                placeholder="最小值"
                class="range-input"
                @input="applyFilters"
              />
              <text>-</text>
              <input 
                type="number" 
                v-model.number="valueFilter.max" 
                placeholder="最大值"
                class="range-input"
                @input="applyFilters"
              />
            </view>
          </view>
          
          <view class="filter-row">
            <text class="filter-label">异常值检测:</text>
            <view class="outlier-filter">
              <view 
                :class="['filter-btn', { active: showOutliers }]"
                @click="toggleOutliers"
              >
                <text class="iconfont icon-warning"></text>
                <text>显示异常值 ({{ outliers.length }})</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 数据分析 -->
      <view class="feature-section">
        <view class="section-header">
          <text class="section-title">数据分析</text>
          <view 
            :class="['section-toggle', { active: showAnalysis }]"
            @click="showAnalysis = !showAnalysis"
          >
            <text class="iconfont" :class="showAnalysis ? 'icon-up' : 'icon-down'"></text>
          </view>
        </view>
        
        <view v-if="showAnalysis" class="section-content">
          <view class="analysis-grid">
            <view class="analysis-item">
              <text class="analysis-label">相关系数</text>
              <text class="analysis-value">{{ trendAnalysis.correlation.toFixed(3) }}</text>
            </view>
            <view class="analysis-item">
              <text class="analysis-label">波动率</text>
              <text class="analysis-value">{{ (trendAnalysis.volatility * 100).toFixed(1) }}%</text>
            </view>
            <view class="analysis-item">
              <text class="analysis-label">斜率</text>
              <text class="analysis-value">{{ trendAnalysis.slope.toFixed(3) }}</text>
            </view>
            <view class="analysis-item">
              <text class="analysis-label">标准差</text>
              <text class="analysis-value">{{ standardDeviation.toFixed(2) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据详情弹窗 -->
    <view v-if="showDataDetail" class="data-detail-modal" @click="closeDataDetail">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">数据详情</text>
          <view class="modal-close" @click="closeDataDetail">
            <text class="iconfont icon-close"></text>
          </view>
        </view>
        
        <view class="modal-body">
          <view v-if="selectedDataPoint" class="data-detail">
            <view class="detail-row">
              <text class="detail-label">标签:</text>
              <text class="detail-value">{{ selectedDataPoint.label }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">数值:</text>
              <text class="detail-value">{{ formatValue(selectedDataPoint.value) }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">颜色:</text>
              <view class="color-preview" :style="{ backgroundColor: selectedDataPoint.color }"></view>
            </view>
            <view class="detail-row">
              <text class="detail-label">在数据集中的位置:</text>
              <text class="detail-value">{{ selectedDataIndex + 1 }} / {{ rawData.length }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import BaseChart from './BaseChart.vue'
import { 
  formatValue, 
  calculateStats, 
  smoothData, 
  analyzeTrend, 
  detectOutliers,
  predictData,
  exportChartData,
  sampleData,
  calculateMovingAverage,
  type ChartDataPoint 
} from '../utils/chartUtils'

interface Props {
  /** 数据源 */
  dataSource: ChartDataPoint[]
  /** 图表标题 */
  title?: string
  /** 图表宽度 */
  width?: number
  /** 图表高度 */
  height?: number
  /** 自动刷新间隔（毫秒） */
  defaultRefreshInterval?: number
  /** 是否默认开启自动刷新 */
  defaultAutoRefresh?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  dataSource: () => [],
  title: '高级图表',
  width: 500,
  height: 350,
  defaultRefreshInterval: 5000,
  defaultAutoRefresh: false
})

const emit = defineEmits<{
  dataRefresh: []
  dataExport: [data: ChartDataPoint[], format: string]
  dataPointClick: [point: ChartDataPoint, index: number]
}>()

// 响应式数据
const baseChart = ref()
const rawData = ref<ChartDataPoint[]>([])
const loading = ref(false)
const selectedType = ref<'line' | 'bar' | 'pie' | 'gauge'>('line')
const autoRefresh = ref(props.defaultAutoRefresh)
const refreshIntervalIndex = ref(1)
const enableSmoothing = ref(false)
const showTrend = ref(false)
const showPrediction = ref(false)
const showFilters = ref(false)
const showAnalysis = ref(false)
const showOutliers = ref(false)
const showDataDetail = ref(false)
const selectedDataPoint = ref<ChartDataPoint | null>(null)
const selectedDataIndex = ref(-1)

// 数据过滤器
const valueFilter = ref({
  min: null as number | null,
  max: null as number | null
})

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 图表类型选项
const chartTypes = [
  { label: '折线图', value: 'line', icon: 'icon-line-chart' },
  { label: '柱状图', value: 'bar', icon: 'icon-bar-chart' },
  { label: '饼图', value: 'pie', icon: 'icon-pie-chart' },
  { label: '仪表盘', value: 'gauge', icon: 'icon-gauge' }
]

// 刷新间隔选项
const refreshIntervals = [
  { label: '1秒', value: 1000 },
  { label: '5秒', value: 5000 },
  { label: '10秒', value: 10000 },
  { label: '30秒', value: 30000 },
  { label: '1分钟', value: 60000 }
]

// 计算属性
const chartTitle = computed(() => {
  let title = props.title
  if (enableSmoothing.value) title += ' (平滑)'
  if (showTrend.value) title += ' (趋势)'
  if (showPrediction.value) title += ' (预测)'
  return title
})

const chartWidth = computed(() => props.width)
const chartHeight = computed(() => props.height)

const filteredData = computed(() => {
  let data = [...rawData.value]
  
  // 应用数值范围过滤
  if (valueFilter.value.min !== null) {
    data = data.filter(item => item.value >= valueFilter.value.min!)
  }
  if (valueFilter.value.max !== null) {
    data = data.filter(item => item.value <= valueFilter.value.max!)
  }
  
  return data
})

const processedChartData = computed(() => {
  let data = [...filteredData.value]
  
  if (data.length === 0) return []
  
  // 数据采样（性能优化）
  if (data.length > 200) {
    data = sampleData(data, 200, 'adaptive')
  }
  
  // 应用平滑处理
  if (enableSmoothing.value) {
    data = smoothData(data, 5)
  }
  
  // 添加移动平均线（趋势）
  if (showTrend.value && data.length > 10) {
    const movingAvg = calculateMovingAverage(data, 10)
    // 为趋势线添加不同的颜色
    const trendData = movingAvg.map(item => ({
      ...item,
      label: `${item.label} (趋势)`,
      color: '#ff7875'
    }))
    data = [...data, ...trendData]
  }
  
  // 添加预测数据
  if (showPrediction.value && data.length > 5) {
    const predictions = predictData(data.slice(0, Math.min(data.length, 50)), 5)
    data = [...data, ...predictions]
  }
  
  return data
})

const statistics = computed(() => calculateStats(filteredData.value))

const trendAnalysis = computed(() => analyzeTrend(filteredData.value))

const outliers = computed(() => detectOutliers(filteredData.value, 2))

const standardDeviation = computed(() => {
  const stats = statistics.value
  if (filteredData.value.length === 0) return 0
  
  const variance = filteredData.value.reduce((sum, item) => 
    sum + Math.pow(item.value - stats.avg, 2), 0
  ) / filteredData.value.length
  
  return Math.sqrt(variance)
})

const chartOptions = computed(() => ({
  animation: !autoRefresh.value, // 自动刷新时禁用动画以提高性能
  showGrid: true,
  responsive: true,
  realtime: autoRefresh.value
}))

// 方法
const selectChartType = (type: 'line' | 'bar' | 'pie' | 'gauge') => {
  selectedType.value = type
}

const toggleSmoothing = () => {
  enableSmoothing.value = !enableSmoothing.value
}

const toggleTrend = () => {
  showTrend.value = !showTrend.value
}

const togglePrediction = () => {
  showPrediction.value = !showPrediction.value
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const toggleOutliers = () => {
  showOutliers.value = !showOutliers.value
  
  if (showOutliers.value && outliers.value.length > 0) {
    // 高亮显示异常值
    uni.showToast({
      title: `发现 ${outliers.value.length} 个异常值`,
      icon: 'none'
    })
  }
}

const onRefreshIntervalChange = (e: any) => {
  refreshIntervalIndex.value = e.detail.value
  
  if (autoRefresh.value) {
    stopAutoRefresh()
    startAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  const interval = refreshIntervals[refreshIntervalIndex.value].value
  refreshTimer = setInterval(() => {
    refreshData()
  }, interval)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

const refreshData = () => {
  emit('dataRefresh')
}

const exportData = () => {
  const data = processedChartData.value
  const csvData = exportChartData(data, 'csv')
  
  // 在实际应用中，这里应该触发文件下载
  emit('dataExport', data, 'csv')
  
  uni.showModal({
    title: '数据导出',
    content: `已导出 ${data.length} 条数据记录`,
    showCancel: false,
    confirmText: '确定'
  })
}

const resetZoom = () => {
  // 重置所有过滤器和处理选项
  valueFilter.value = { min: null, max: null }
  enableSmoothing.value = false
  showTrend.value = false
  showPrediction.value = false
  showOutliers.value = false
  
  // 刷新图表
  nextTick(() => {
    baseChart.value?.refresh()
  })
}

const applyFilters = () => {
  // 过滤器变化时自动应用
  nextTick(() => {
    baseChart.value?.refresh()
  })
}

const handleChartClick = (item: ChartDataPoint, index: number) => {
  selectedDataPoint.value = item
  selectedDataIndex.value = index
  showDataDetail.value = true
  
  emit('dataPointClick', item, index)
}

const handleChartHover = (item: ChartDataPoint, index: number) => {
  // 悬停效果处理
  console.log('悬停在数据点:', item, index)
}

const closeDataDetail = () => {
  showDataDetail.value = false
  selectedDataPoint.value = null
  selectedDataIndex.value = -1
}

const getTrendIcon = (trend: string): string => {
  switch (trend) {
    case 'up': return 'icon-trending-up'
    case 'down': return 'icon-trending-down'
    default: return 'icon-minus'
  }
}

const getTrendText = (trend: string): string => {
  switch (trend) {
    case 'up': return '上升'
    case 'down': return '下降'
    default: return '稳定'
  }
}

// 监听数据源变化
watch(() => props.dataSource, (newData) => {
  rawData.value = [...newData]
}, { immediate: true, deep: true })

// 生命周期
onMounted(() => {
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 暴露方法
const refresh = () => {
  baseChart.value?.refresh()
}

const updateData = (newData: ChartDataPoint[]) => {
  rawData.value = [...newData]
}

defineExpose({
  refresh,
  updateData,
  exportData,
  resetZoom
})
</script>

<style lang="scss" scoped>
.advanced-chart {
  .chart-controls {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;

    .control-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .control-group {
      display: flex;
      align-items: center;
      gap: 8px;

      .control-label {
        font-size: 12px;
        color: #666;
        white-space: nowrap;
      }
    }

    .chart-type-selector,
    .data-processing,
    .auto-refresh,
    .data-actions {
      display: flex;
      gap: 6px;
    }

    .type-btn,
    .process-btn,
    .refresh-toggle,
    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
      padding: 6px 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 10px;
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 50px;

      .iconfont {
        font-size: 14px;
      }

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.active {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;
      }
    }

    .refresh-interval {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 11px;
      color: #666;

      .interval-display {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border: 1px solid #d9d9d9;
        border-radius: 3px;
        cursor: pointer;
        min-width: 60px;
        justify-content: center;

        .iconfont {
          font-size: 10px;
        }
      }
    }
  }

  .chart-container {
    position: relative;

    .chart-stats {
      display: flex;
      justify-content: space-around;
      margin-top: 12px;
      padding: 8px;
      background-color: #f8f9fa;
      border-radius: 4px;

      .stat-item {
        text-align: center;

        .stat-label {
          display: block;
          font-size: 11px;
          color: #999;
          margin-bottom: 2px;
        }

        .stat-value {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;
          font-size: 13px;
          font-weight: 600;
          color: #333;

          &.trend-up {
            color: #52c41a;
          }

          &.trend-down {
            color: #ff4d4f;
          }

          &.trend-stable {
            color: #999;
          }
        }
      }
    }
  }

  .advanced-features {
    margin-top: 16px;

    .feature-section {
      margin-bottom: 12px;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      overflow: hidden;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background-color: #f8f9fa;
        cursor: pointer;

        .section-title {
          font-size: 13px;
          font-weight: 500;
          color: #333;
        }

        .section-toggle {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.3s ease;

          .iconfont {
            font-size: 12px;
            color: #666;
          }

          &.active {
            background-color: #1890ff;

            .iconfont {
              color: #fff;
            }
          }
        }
      }

      .section-content {
        padding: 12px;

        .filter-row {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .filter-label {
            font-size: 12px;
            color: #666;
            min-width: 80px;
          }
        }

        .range-filter {
          display: flex;
          align-items: center;
          gap: 6px;

          .range-input {
            width: 80px;
            padding: 4px 6px;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            font-size: 11px;
          }
        }

        .outlier-filter,
        .filter-btn {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 4px 8px;
          border: 1px solid #d9d9d9;
          border-radius: 3px;
          font-size: 11px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }

          &.active {
            background-color: #1890ff;
            border-color: #1890ff;
            color: #fff;
          }
        }

        .analysis-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 8px;

          .analysis-item {
            text-align: center;
            padding: 6px;
            background-color: #f8f9fa;
            border-radius: 4px;

            .analysis-label {
              display: block;
              font-size: 10px;
              color: #999;
              margin-bottom: 2px;
            }

            .analysis-value {
              font-size: 12px;
              font-weight: 600;
              color: #333;
            }
          }
        }
      }
    }
  }

  .data-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background-color: #fff;
      border-radius: 8px;
      width: 90%;
      max-width: 400px;
      max-height: 80%;
      overflow: hidden;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #e8e8e8;

        .modal-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .modal-close {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: #f5f5f5;
          }

          .iconfont {
            font-size: 16px;
            color: #666;
          }
        }
      }

      .modal-body {
        padding: 16px;

        .data-detail {
          .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            .detail-label {
              font-size: 13px;
              color: #666;
            }

            .detail-value {
              font-size: 13px;
              color: #333;
              font-weight: 500;
            }

            .color-preview {
              width: 20px;
              height: 20px;
              border-radius: 3px;
              border: 1px solid #e8e8e8;
            }
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .advanced-chart {
    .chart-controls {
      .control-row {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
      }

      .control-group {
        justify-content: space-between;
      }

      .chart-type-selector,
      .data-processing,
      .data-actions {
        justify-content: center;
      }
    }

    .chart-stats {
      .stat-item {
        .stat-value {
          font-size: 11px;
        }
      }
    }

    .advanced-features {
      .feature-section {
        .section-content {
          .analysis-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}

// 暗色主题支持
.admin-layout.dark-theme .advanced-chart {
  .chart-controls {
    background-color: #2a2a2a;

    .control-label {
      color: #ccc;
    }

    .type-btn,
    .process-btn,
    .refresh-toggle,
    .action-btn {
      border-color: #434343;
      color: #ccc;
      background-color: #1f1f1f;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.active {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;
      }
    }

    .interval-display {
      border-color: #434343;
      background-color: #1f1f1f;
      color: #ccc;
    }
  }

  .chart-stats {
    background-color: #2a2a2a;

    .stat-item {
      .stat-label {
        color: #999;
      }

      .stat-value {
        color: #fff;
      }
    }
  }

  .advanced-features {
    .feature-section {
      border-color: #434343;

      .section-header {
        background-color: #2a2a2a;

        .section-title {
          color: #fff;
        }
      }

      .section-content {
        background-color: #1f1f1f;

        .filter-label {
          color: #ccc;
        }

        .range-input {
          border-color: #434343;
          background-color: #2a2a2a;
          color: #fff;
        }

        .analysis-item {
          background-color: #2a2a2a;

          .analysis-label {
            color: #999;
          }

          .analysis-value {
            color: #fff;
          }
        }
      }
    }
  }

  .data-detail-modal {
    .modal-content {
      background-color: #1f1f1f;

      .modal-header {
        border-bottom-color: #434343;

        .modal-title {
          color: #fff;
        }
      }

      .modal-body {
        .detail-row {
          border-bottom-color: #434343;

          .detail-label {
            color: #ccc;
          }

          .detail-value {
            color: #fff;
          }
        }
      }
    }
  }
}

@keyframes spinning {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinning {
  animation: spinning 1s linear infinite;
}
</style>