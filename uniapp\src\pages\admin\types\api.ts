import type { 
  AdminUser, 
  LoginCredentials, 
  DashboardMetrics, 
  ChartData, 
  Activity, 
  QueryParams, 
  SystemStatus, 
  PerformanceMetrics, 
  Alert, 
  LogEntry, 
  LogQueryParams 
} from './admin'
import type { PageResult } from '@/types/global'

// ==================== 认证API类型 ====================

/** 认证API接口 */
export interface AuthAPI {
  /** 登录 */
  login: (credentials: LoginCredentials) => Promise<AdminUser>
  /** 退出登录 */
  logout: () => Promise<void>
  /** 验证令牌 */
  validateToken: () => Promise<AdminUser>
  /** 刷新令牌 */
  refreshToken: () => Promise<string>
}

// ==================== 仪表板API类型 ====================

/** 仪表板API接口 */
export interface DashboardAPI {
  /** 获取指标 */
  getMetrics: () => Promise<DashboardMetrics>
  /** 获取图表数据 */
  getChartData: (type: string) => Promise<ChartData[]>
  /** 获取最近活动 */
  getRecentActivities: () => Promise<Activity[]>
}

// ==================== 用户管理API类型 ====================

/** 用户角色 */
export interface Role {
  /** 角色ID */
  id: number
  /** 角色名称 */
  name: string
  /** 角色描述 */
  description: string
  /** 权限列表 */
  permissions: string[]
}

/** 用户信息 */
export interface User {
  /** 用户ID */
  id: number
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname: string
  /** 邮箱 */
  email: string
  /** 手机号 */
  phone: string
  /** 状态 */
  status: number
  /** 角色 */
  role: Role
  /** 创建时间 */
  createTime: string
  /** 最后登录时间 */
  lastLoginTime: string
}

/** 创建用户请求 */
export interface CreateUserRequest {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 昵称 */
  nickname: string
  /** 邮箱 */
  email: string
  /** 手机号 */
  phone: string
  /** 角色ID */
  roleId: number
}

/** 更新用户请求 */
export interface UpdateUserRequest {
  /** 昵称 */
  nickname?: string
  /** 邮箱 */
  email?: string
  /** 手机号 */
  phone?: string
  /** 角色ID */
  roleId?: number
  /** 状态 */
  status?: number
}

/** 用户管理API接口 */
export interface UserManagementAPI {
  /** 获取用户列表 */
  getUsers: (params: QueryParams) => Promise<PageResult<User>>
  /** 创建用户 */
  createUser: (user: CreateUserRequest) => Promise<User>
  /** 更新用户 */
  updateUser: (id: number, user: UpdateUserRequest) => Promise<User>
  /** 删除用户 */
  deleteUser: (id: number) => Promise<boolean>
  /** 获取用户角色 */
  getUserRoles: () => Promise<Role[]>
  /** 获取单个用户 */
  getUser: (id: number) => Promise<User>
}

// ==================== 系统监控API类型 ====================

/** 性能指标 */
export interface PerformanceMetrics {
  /** CPU使用率 */
  cpuUsage: number
  /** 内存使用率 */
  memoryUsage: number
  /** 磁盘使用率 */
  diskUsage: number
  /** 网络IO */
  networkIO: number
  /** 磁盘IO */
  diskIO: number
}

/** 系统监控API接口 */
export interface MonitorAPI {
  /** 获取系统状态 */
  getSystemStatus: () => Promise<SystemStatus>
  /** 获取性能指标 */
  getPerformanceMetrics: () => Promise<PerformanceMetrics>
  /** 获取告警 */
  getAlerts: () => Promise<Alert[]>
  /** 获取日志条目 */
  getLogEntries: (params: LogQueryParams) => Promise<PageResult<LogEntry>>
}

// ==================== 系统工具API类型 ====================

/** 备份结果 */
export interface BackupResult {
  /** 是否成功 */
  success: boolean
  /** 备份文件路径 */
  filePath?: string
  /** 错误消息 */
  error?: string
}

/** 清理结果 */
export interface CleanupResult {
  /** 是否成功 */
  success: boolean
  /** 清理的文件数量 */
  cleanedFiles?: number
  /** 释放的空间大小 */
  freedSpace?: number
  /** 错误消息 */
  error?: string
}

/** 系统工具API接口 */
export interface SystemToolsAPI {
  /** 数据库备份 */
  backupDatabase: () => Promise<BackupResult>
  /** 获取日志文件列表 */
  getLogFiles: () => Promise<string[]>
  /** 下载日志文件 */
  downloadLogFile: (filename: string) => Promise<Blob>
  /** 清理缓存 */
  clearCache: (type?: string) => Promise<boolean>
  /** 系统清理 */
  systemCleanup: () => Promise<CleanupResult>
}

// ==================== 通用API响应类型 ====================

/** API响应基础类型 */
export interface ApiResponse<T = any> {
  /** 状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 时间戳 */
  timestamp: number
}

/** API错误类型 */
export interface ApiError {
  /** 错误码 */
  code: number
  /** 错误消息 */
  message: string
  /** 错误详情 */
  details?: any
}

// ==================== HTTP错误类型 ====================

/** 认证错误 */
export interface AuthError extends ApiError {
  /** 错误类型 */
  type: 'AUTH_ERROR'
}

/** 网络错误 */
export interface NetworkError extends ApiError {
  /** 错误类型 */
  type: 'NETWORK_ERROR'
}

/** 验证错误 */
export interface ValidationError extends ApiError {
  /** 错误类型 */
  type: 'VALIDATION_ERROR'
  /** 字段错误 */
  fieldErrors?: Record<string, string>
}

/** 服务器错误 */
export interface ServerError extends ApiError {
  /** 错误类型 */
  type: 'SERVER_ERROR'
}