<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { BreadcrumbItem } from '@/pages/admin/types/admin'
import { useUserStore, useUIStore } from '@/stores'

// Emits
const emit = defineEmits<{
  'toggle-sidebar': []
}>()

// Stores
const userStore = useUserStore()
const uiStore = useUIStore()

// 响应式状态
const showUserDropdown = ref(false)
const showNotifications = ref(false)
const notifications = ref([
  {
    id: 1,
    type: 'info',
    title: '系统更新',
    description: '系统将在今晚23:00进行维护更新',
    createTime: '2024-01-15 14:30:00',
    isRead: false
  },
  {
    id: 2,
    type: 'warning',
    title: '存储空间不足',
    description: '服务器存储空间使用率已达到85%',
    createTime: '2024-01-15 13:15:00',
    isRead: false
  },
  {
    id: 3,
    type: 'success',
    title: '备份完成',
    description: '数据库备份已成功完成',
    createTime: '2024-01-15 12:00:00',
    isRead: true
  }
])

// 计算属性
const breadcrumbItems = computed(() => adminStore.breadcrumb)
const isDarkTheme = computed(() => uiStore.isDarkTheme)

const avatarColor = computed(() => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068']
  const username = userStore.profile.nickname || ''
  const index = username.charCodeAt(0) % colors.length
  return colors[index]
})

const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.isRead).length
})

const systemStatus = computed(() => {
  // 这里可以根据实际系统状态来设置
  return {
    status: 'healthy',
    text: '系统正常'
  }
})

// 方法
const toggleMenu = () => {
  emit('toggle-sidebar')
  adminStore.toggleMenuCollapsed()
}

const toggleUserDropdown = () => {
  showUserDropdown.value = !showUserDropdown.value
  if (showUserDropdown.value) {
    showNotifications.value = false
  }
}

const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  if (showNotifications.value) {
    showUserDropdown.value = false
  }
}

const toggleTheme = () => {
  uiStore.toggleTheme()
  uni.showToast({
    title: `已切换到${uiStore.isDarkTheme ? '暗色' : '亮色'}主题`,
    icon: 'none'
  })
}

const toggleFullscreen = () => {
  // UniApp中全屏功能的实现
  uni.showToast({
    title: '全屏功能待实现',
    icon: 'none'
  })
}

const closeAllDropdowns = () => {
  showUserDropdown.value = false
  showNotifications.value = false
}

const formatRole = (role?: string) => {
  const roleMap: Record<string, string> = {
    'super_admin': '超级管理员',
    'admin': '管理员',
    'operator': '操作员',
    'viewer': '查看者',
  }
  return role ? roleMap[role] || role : '未知角色'
}

const formatLastLogin = (lastLoginTime?: string) => {
  if (!lastLoginTime) return ''
  const now = new Date()
  const loginTime = new Date(lastLoginTime)
  const diff = now.getTime() - loginTime.getTime()
  
  if (diff < 60000) return '刚刚登录'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前登录`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前登录`
  return `${Math.floor(diff / 86400000)}天前登录`
}

const formatTime = (time: string) => {
  const now = new Date()
  const targetTime = new Date(time)
  const diff = now.getTime() - targetTime.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const handleBreadcrumbClick = (item: BreadcrumbItem, index: number) => {
  if (item.path && index < breadcrumbItems.value.length - 1) {
    // 处理面包屑点击，跳转到对应路径
    adminStore.setCurrentModule(item.path)
    uni.$emit('admin-route-change', item.path)

    // 更新浏览器历史状态
    const state = { adminPath: item.path }
    const url = `#/admin/${item.path}`
    window.history.pushState(state, '', url)
  }
}

const handleProfile = () => {
  showUserDropdown.value = false
  uni.showToast({
    title: '个人资料功能待实现',
    icon: 'none',
  })
}

const handleSettings = () => {
  showUserDropdown.value = false
  uni.showToast({
    title: '系统设置功能待实现',
    icon: 'none',
  })
}

const handlePreferences = () => {
  showUserDropdown.value = false
  uni.showToast({
    title: '偏好设置功能待实现',
    icon: 'none',
  })
}

const handleHelp = () => {
  showUserDropdown.value = false
  uni.showToast({
    title: '帮助中心功能待实现',
    icon: 'none',
  })
}

const handleLockScreen = () => {
  showUserDropdown.value = false
  uni.showToast({
    title: '锁定屏幕功能待实现',
    icon: 'none',
  })
}

const handleLogout = async () => {
  showUserDropdown.value = false
  
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          userStore.clearProfile
          tokenStore.clearToken
          uni.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        } catch (error) {
          uni.showToast({
            title: '退出登录失败',
            icon: 'error'
          })
        }
      }
    },
  })
}

const getNotificationIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'info': 'ℹ️',
    'warning': '⚠️',
    'error': '❌',
    'success': '✅',
    'system': '🔧'
  }
  return iconMap[type] || 'ℹ️'
}

const handleNotificationClick = (notification: any) => {
  // 标记为已读
  notification.isRead = true
  showNotifications.value = false
  
  // 处理通知点击事件
  uni.showToast({
    title: `点击了通知: ${notification.title}`,
    icon: 'none'
  })
}

const markAllAsRead = () => {
  notifications.value.forEach(n => n.isRead = true)
  uni.showToast({
    title: '已标记全部为已读',
    icon: 'success'
  })
}

const viewAllNotifications = () => {
  showNotifications.value = false
  uni.showToast({
    title: '跳转到通知中心',
    icon: 'none'
  })
}

// 点击外部关闭下拉菜单
const handleClickOutside = () => {
  closeAllDropdowns()
}

// 键盘快捷键处理
const handleKeydown = (e: KeyboardEvent) => {
  if (e.ctrlKey) {
    switch (e.key) {
      case 'p':
        e.preventDefault()
        handleProfile()
        break
      case ',':
        e.preventDefault()
        handleSettings()
        break
      case 'l':
        e.preventDefault()
        handleLockScreen()
        break
      case 'q':
        e.preventDefault()
        handleLogout()
        break
    }
  } else if (e.key === 'F1') {
    e.preventDefault()
    handleHelp()
  }
}

// 生命周期
onMounted(() => {
  // 监听页面点击事件
  uni.$on('click', handleClickOutside)
  
  // 监听键盘事件
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  uni.$off('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <view class="admin-header">
    <!-- 左侧区域 -->
    <view class="header-left">
      <view class="menu-toggle" @click="toggleMenu">
        <text class="menu-icon">☰</text>
      </view>
      
      <view class="breadcrumb">
        <view 
          v-for="(item, index) in breadcrumbItems" 
          :key="index"
          class="breadcrumb-item"
          :class="{ active: index === breadcrumbItems.length - 1 }"
        >
          <text v-if="index > 0" class="separator">/</text>
          <text 
            class="breadcrumb-text"
            @click="handleBreadcrumbClick(item, index)"
          >
            {{ item.title }}
          </text>
        </view>
      </view>
    </view>
    
    <!-- 中间区域 - 系统状态指示器 -->
    <view class="header-center">
      <view class="system-status" :class="systemStatus.status">
        <text class="status-dot"></text>
        <text class="status-text">{{ systemStatus.text }}</text>
      </view>
    </view>
    
    <!-- 右侧区域 -->
    <view class="header-right">
      <!-- 通知中心 -->
      <view class="notification-center" @click="toggleNotifications">
        <text class="notification-icon">🔔</text>
        <view v-if="unreadCount > 0" class="notification-badge">
          <text>{{ unreadCount > 99 ? '99+' : unreadCount }}</text>
        </view>
      </view>

      <!-- 主题切换 -->
      <view class="theme-toggle" @click="toggleTheme">
        <text class="theme-icon">{{ isDarkTheme ? '🌙' : '☀️' }}</text>
      </view>

      <!-- 全屏切换 -->
      <view class="fullscreen-toggle" @click="toggleFullscreen">
        <text class="fullscreen-icon">⛶</text>
      </view>

      <!-- 用户信息 -->
      <view class="user-info" @click="toggleUserDropdown">
        <view class="avatar" :style="{ backgroundColor: avatarColor }">
          <image
            style="width: 100rpx;height: 100rpx;"
            :src="userStore.profile.avatar"
            mode="scaleToFill"
          />
        </view>
        <view class="user-details">
          <view class="username">{{ userStore.profile.nickname || '未登录' }}</view>
          <view class="role">超级管理员</view>
          <view class="last-login">{{ formatLastLogin(currentUser?.lastLoginTime) }}</view>
        </view>
        <text class="dropdown-icon" :class="{ expanded: showUserDropdown }">▼</text>
      </view>
    </view>
    
    <!-- 用户下拉菜单 -->
    <view v-if="showUserDropdown" class="user-dropdown" @click.stop>
      <view class="dropdown-header">
        <view class="user-avatar" :style="{ backgroundColor: avatarColor }">
          <image
            style="width: 100rpx;height: 100rpx;"
            :src="userStore.profile.avatar"
            mode="scaleToFill"
          />
        </view>
        <view class="user-info-detail">
          <view class="username">{{ userStore.profile.nickname }}</view>
          <view class="role">超级管理员</view>
          <view class="user-id">ID: {{ userStore.profile.id }}</view>
        </view>
      </view>
      
      <view class="dropdown-divider"></view>
      
      <view class="dropdown-item" @click="handleProfile">
        <text class="icon">👤</text>
        <text>个人资料</text>
        <text class="shortcut">Ctrl+P</text>
      </view>
      
      <view class="dropdown-item" @click="handleSettings">
        <text class="icon">⚙️</text>
        <text>系统设置</text>
        <text class="shortcut">Ctrl+,</text>
      </view>
      
      <view class="dropdown-item" @click="handlePreferences">
        <text class="icon">🎨</text>
        <text>偏好设置</text>
      </view>
      
      <view class="dropdown-item" @click="handleHelp">
        <text class="icon">❓</text>
        <text>帮助中心</text>
        <text class="shortcut">F1</text>
      </view>
      
      <view class="dropdown-divider"></view>
      
      <view class="dropdown-item" @click="handleLockScreen">
        <text class="icon">🔒</text>
        <text>锁定屏幕</text>
        <text class="shortcut">Ctrl+L</text>
      </view>
      
      <view class="dropdown-item danger" @click="handleLogout">
        <text class="icon">🚪</text>
        <text>退出登录</text>
        <text class="shortcut">Ctrl+Q</text>
      </view>
    </view>

    <!-- 通知下拉菜单 -->
    <view v-if="showNotifications" class="notification-dropdown" @click.stop>
      <view class="notification-header">
        <text class="title">通知中心</text>
        <text class="mark-all-read" @click="markAllAsRead">全部已读</text>
      </view>
      
      <view class="notification-list">
        <view 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item"
          :class="{ unread: !notification.isRead }"
          @click="handleNotificationClick(notification)"
        >
          <view class="notification-icon">
            <text>{{ getNotificationIcon(notification.type) }}</text>
          </view>
          <view class="notification-content">
            <view class="notification-title">{{ notification.title }}</view>
            <view class="notification-desc">{{ notification.description }}</view>
            <view class="notification-time">{{ formatTime(notification.createTime) }}</view>
          </view>
          <view v-if="!notification.isRead" class="unread-dot"></view>
        </view>
      </view>
      
      <view class="notification-footer">
        <text @click="viewAllNotifications">查看全部通知</text>
      </view>
    </view>

    <!-- 遮罩层 -->
    <view 
      v-if="showUserDropdown || showNotifications" 
      class="dropdown-mask" 
      @click="closeAllDropdowns"
    ></view>
  </view>
</template>

<style lang="scss" scoped>
@import '../styles/admin-header.scss';
</style>