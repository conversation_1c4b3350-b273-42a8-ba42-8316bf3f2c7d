<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores'
import { updateNickname, updatePartner, updatePhone } from '@/services/profile'

const userStore = useUserStore()

// 响应式数据
const profileData = ref({
  name: userStore.profile.nickname,
  pronouns: '',
  friendName: userStore.profile.partner.nickName,
  phone: userStore.profile.phone,
  locationOn: false,
  planetMusicOn: false
})

// 弹框相关数据
const showEditDialog = ref(false)
const editType = ref('')
const editValue = ref('')
const editTitle = ref('')

// 方法
const togglePlanetLocation = () => {
  uni.showToast({
    title: '敬请期待',
    icon: 'none'
  })
  profileData.value.locationOn = !profileData.value.locationOn
}

// 方法
const togglePlanetMusic = () => {
  uni.showToast({
    title: '敬请期待',
    icon: 'none'
  })
  profileData.value.planetMusicOn = !profileData.value.planetMusicOn
}

const openNotebook = () => {
  uni.showToast({
    title: '敬请期待',
    icon: 'none'
  })
}

const restorePurchases = () => {
  console.log('恢复购买')
}

const goBack = () => {
  if (getCurrentPages().length == 1) {
    uni.reLaunch({ url: '/pages/ai/ai' })
  } else {
    uni.navigateBack({ delta: 1})
  }
}

// 编辑相关方法
const openEditDialog = (type: string) => {
  editType.value = type
  switch (type) {
    case 'name':
      editTitle.value = '修改昵称'
      editValue.value = profileData.value.name
      break
    case 'friendName':
      editTitle.value = '修改伙伴'
      editValue.value = profileData.value.friendName
      break
    case 'phone':
      editTitle.value = '修改联系号码'
      editValue.value = profileData.value.phone
      break
  }
  showEditDialog.value = true
}

const closeEditDialog = () => {
  showEditDialog.value = false
  editType.value = ''
  editValue.value = ''
  editTitle.value = ''
}

const saveEdit = async () => {
  if (!editValue.value.trim()) {
    uni.showToast({
      title: '请输入内容',
      icon: 'none'
    })
    return
  }

  // 显示加载提示
  uni.showLoading({
    title: '保存中...'
  })

  try {
    switch (editType.value) {
      case 'name':
        // 调用更新昵称接口
        const nicknameRes = await updateNickname(editValue.value.trim())
        if (nicknameRes.code === 200) {
          profileData.value.name = editValue.value.trim()
          // 更新用户store中的昵称
          userStore.profile.nickname = editValue.value.trim()
          uni.showToast({
            title: '已保存',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: nicknameRes.msg || '更新失败',
            icon: 'none'
          })
          return
        }
        break
      case 'friendName':
        console.log('...');
        
        const partnerRes = await updatePartner(editValue.value.trim())
        if (partnerRes.code === 200) {
          profileData.value.friendName = editValue.value.trim()
          // 更新用户store中的昵称
          userStore.profile.partner.nickName = editValue.value.trim()
          uni.showToast({
            title: '已保存',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: partnerRes.msg || '更新失败',
            icon: 'none'
          })
          return
        }
        break
      case 'phone':
        // 调用更新联系号码接口
        const phoneRes = await updatePhone(editValue.value.trim())
        if (phoneRes.code === 200) {
          profileData.value.phone = editValue.value.trim()
          // 更新用户store中的手机号
          userStore.profile.mobile = editValue.value.trim()
          uni.showToast({
            title: '联系号码更新成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: phoneRes.msg || '更新失败',
            icon: 'none'
          })
          return
        }
        break
    }
    
    closeEditDialog()
  } catch (error) {
    console.error('更新失败:', error)
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}
</script>

<template>
  <view class="profile-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="title-container">
        <text class="title">个人资料</text>
      </view>
    </view>

    <!-- 个人资料内容 -->
    <view class="profile-content">
      <!-- 头像部分 -->
      <view class="profile-item">
        <text class="item-label">头像</text>
        <view class="avatar-container">
          <image 
            class="avatar" 
            :src="userStore.profile?.avatar" 
            mode="aspectFill"
          />
        </view>
      </view>

      <!-- 姓名 -->
      <view class="profile-item clickable" @click="openEditDialog('name')">
        <text class="item-label">昵称</text>
        <text class="item-value">{{ profileData.name }}</text>
      </view>

      <!-- 代词 -->
      <!-- <view class="profile-item">
        <text class="item-label">代词</text>
        <text class="item-value placeholder">{{ profileData.pronouns || '' }}</text>
      </view> -->

      <!-- 朋友名字 -->
      <view class="profile-item clickable" @click="openEditDialog('friendName')">
        <text class="item-label">星灵</text>
        <text class="item-value">{{ profileData.friendName }}</text>
      </view>

      <!-- 笔记本解锁 -->
      <view class="profile-item clickable" @click="openNotebook">
        <text class="item-label">笔记本解锁</text>
        <view class="unlock-icon">
          <text class="icon">🔓</text>
        </view>
      </view>

      <!-- 地图展示 -->
      <view class="profile-item">
        <view class="music-content">
          <view class="music-info">
            <view class="item-label">地图展示</view>
            <view class="item-description">把你的星灵展示在地图上，不会显示个人信息</view>
          </view>
          <view class="switch-container">
            <switch 
              :checked="profileData.locationOn" 
              @change="togglePlanetLocation"
              color="#4CD964"
            />
            <text class="switch-status">{{ profileData.locationOn ? 'ON' : 'OFF' }}</text>
          </view>
        </view>
      </view>

      <!-- 星球音乐 -->
      <view class="profile-item">
        <view class="music-content">
          <view class="music-info">
            <view class="item-label">星球音乐</view>
            <view class="item-description">当你不和{{ profileData.friendName }}谈话时，播放舒缓的音乐。</view>
          </view>
          <view class="switch-container">
            <switch 
              :checked="profileData.planetMusicOn" 
              @change="togglePlanetMusic"
              color="#4CD964"
            />
            <text class="switch-status">{{ profileData.planetMusicOn ? 'ON' : 'OFF' }}</text>
          </view>
        </view>
      </view>

      <!-- 电话 -->
      <view class="profile-item clickable" @click="openEditDialog('phone')">
        <view class="phone-content">
          <view class="item-label">联系号码</view>
          <view class="item-description">您的电话号码已验证</view>
        </view>
        <text class="phone-number">{{ profileData.phone }}</text>
      </view>

      <!-- 恢复购买 -->
      <!-- <view class="profile-item clickable" @click="restorePurchases">
        <text class="item-label">Restore purchases</text>
      </view> -->
    </view>

    <!-- 编辑弹框 -->
    <view v-if="showEditDialog" class="dialog-overlay" @click="closeEditDialog">
      <view class="dialog-container" @click.stop>
        <view class="dialog-header">
          <text class="dialog-title">{{ editTitle }}</text>
          <view class="cancel-btn" @click="closeEditDialog">
            <image class="cancel-icon" src="/static/ai/cancel.png" mode="aspectFit" />
          </view>
        </view>
        <view class="dialog-content">
          <input 
            v-model="editValue" 
            class="dialog-input"
            :placeholder="editTitle"
            :type="editType === 'phone' ? 'number' : 'text'"
            :maxlength="editType === 'phone' ? 11 : 20"
          />
        </view>
        <view class="dialog-actions">
          <button class="dialog-btn save-btn" @click="saveEdit">保存</button>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.profile-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-top: env(safe-area-inset-top);
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 20rpx 20px;
  background-color: white;
  border-bottom: 1px solid #e5e5e5;

  .back-btn {
    position: absolute;
    left: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .back-icon {
      font-size: 50px;
      color: #333;
      font-weight: bold;
    }
  }

  .title-container {
    border: 2px solid #333;
    border-radius: 25px;
    padding: 4px 24px;
    margin-top: 10px;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
}

.profile-content {
  padding: 0;
}

.profile-item {
  background-color: white;
  border-bottom: 1px solid #e5e5e5;
  padding: 40rpx 60rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &.clickable {
    &:active {
      background-color: #f0f0f0;
    }
  }

  .item-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    flex: 1;
  }

  .item-value {
    font-size: 16px;
    color: #666;
    text-align: right;

    &.placeholder {
      color: #ccc;
    }
  }

  .item-description {
    font-size: 14px;
    color: #999;
    margin-top: 4px;
    line-height: 1.4;
  }
}

.avatar-container {
  width: 50px;
  height: 50px;
  border-radius: 30px;
  overflow: hidden;
  background: linear-gradient(45deg, #8B4513, #2F4F4F);
  display: flex;
  align-items: center;
  justify-content: center;

  .avatar {
    width: 100%;
    height: 100%;
  }
}

.music-content {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;

  .music-info {
    flex: 1;
    margin-right: 20px;
  }

  .switch-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .switch-status {
      font-size: 12px;
      color: #4CD964;
      font-weight: 600;
    }
  }
}

.unlock-icon {
  .icon {
    font-size: 20px;
  }
}

.phone-content {
  flex: 1;
  margin-right: 20px;
}

.phone-number {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

/* uni-app 特定样式调整 */
switch {
  transform: scale(0.8);
}

/* 弹框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-container {
  background-color: white;
  border-radius: 12px;
  width: 80%;
  max-width: 400px;
  margin: 0 20px;
  overflow: hidden;
}

.dialog-header {
  padding: 20px;
  border-bottom: 1px solid #e5e5e5;
  text-align: center;
  position: relative;

  .dialog-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .cancel-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .cancel-icon {
      width: 30px;
      height: 30px;
    }

    &:active {
      opacity: 0.7;
    }
  }
}

.dialog-content {
  padding: 20px;

  .dialog-input {
    width: 90%;
    height: 44px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 0 12px;
    font-size: 16px;
    color: #333;
    background-color: #f8f8f8;

    &:focus {
      border-color: #4CD964;
      background-color: white;
    }
  }
}

.dialog-actions {
  padding: 20px;

  .dialog-btn {
    width: 100%;
    height: 50px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;

    &.save-btn {
      background-color: #333;
      color: white;

      &:active {
        background-color: #555;
      }
    }
  }
}
</style>