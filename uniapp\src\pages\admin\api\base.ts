import type { CrudOperation, QueryParams } from '../types/admin'
import type { PageResult } from '@/types/global'
import { handleError, retryOperation } from '../middleware/errorHandler'
import { globalUIManager } from '../composables/useUIManager'

/**
 * API基础配置
 */
export const API_BASE_URL = '/admin'

/**
 * HTTP请求工具
 */
export class HttpClient {
  private baseURL: string
  
  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }
  
  /**
   * 发送HTTP请求
   */
  private async request<T>(
    url: string,
    options: {
      method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
      data?: any
      params?: Record<string, any>
      headers?: Record<string, string>
    } = {}
  ): Promise<T> {
    const {
      method = 'GET',
      data,
      params,
      headers = {}
    } = options
    
    // 构建完整URL
    let fullUrl = `${this.baseURL}${url}`
    
    // 添加查询参数
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value))
        }
      })
      const queryString = searchParams.toString()
      if (queryString) {
        fullUrl += `?${queryString}`
      }
    }
    
    // 设置默认headers
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers
    }
    
    // 获取token
    const token = uni.getStorageSync('admin_token')
    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`
    }
    
    return new Promise((resolve, reject) => {
      uni.request({
        url: fullUrl,
        method,
        data: data ? JSON.stringify(data) : undefined,
        header: defaultHeaders,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            const responseData = res.data as any
            
            // 检查业务状态码
            if (responseData.code === 0 || responseData.success) {
              resolve(responseData.data || responseData)
            } else {
              const error = new Error(responseData.message || '请求失败')
              const errorInfo = handleError(error, {
                showToast: true,
                logError: true
              })
              reject(new Error(errorInfo.message))
            }
          } else {
            const error = { statusCode: res.statusCode, errMsg: res.errMsg, data: res.data }
            const errorInfo = handleError(error, {
              showToast: true,
              logError: true
            })
            reject(new Error(errorInfo.message))
          }
        },
        fail: (err) => {
          console.error('Request failed:', err)
          const errorInfo = handleError(err, {
            showToast: true,
            logError: true
          })
          reject(new Error(errorInfo.message))
        }
      })
    })
  }
  
  /**
   * GET请求
   */
  get<T>(url: string, params?: Record<string, any>, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'GET', params, headers })
  }
  
  /**
   * POST请求
   */
  post<T>(url: string, data?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'POST', data, headers })
  }
  
  /**
   * PUT请求
   */
  put<T>(url: string, data?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'PUT', data, headers })
  }
  
  /**
   * DELETE请求
   */
  delete<T>(url: string, params?: Record<string, any>, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'DELETE', params, headers })
  }
}

/**
 * 默认HTTP客户端实例
 */
export const httpClient = new HttpClient()

/**
 * 创建基础CRUD API
 */
export function createCrudApi<T extends Record<string, any>>(
  resourcePath: string,
  client: HttpClient = httpClient
): CrudOperation<T> {
  return {
    /**
     * 获取列表
     */
    async list(params: QueryParams): Promise<PageResult<T>> {
      return retryOperation(
        () => client.get<PageResult<T>>(`/${resourcePath}`, params),
        `list_${resourcePath}_${JSON.stringify(params)}`
      )
    },
    
    /**
     * 创建记录
     */
    async create(data: Partial<T>): Promise<T> {
      const response = await client.post<T>(`/${resourcePath}`, data)
      return response
    },
    
    /**
     * 更新记录
     */
    async update(id: number, data: Partial<T>): Promise<T> {
      const response = await client.put<T>(`/${resourcePath}/${id}`, data)
      return response
    },
    
    /**
     * 删除记录
     */
    async delete(id: number): Promise<boolean> {
      await client.delete(`/${resourcePath}/${id}`)
      return true
    },
    
    /**
     * 获取单条记录
     */
    async get(id: number): Promise<T> {
      return retryOperation(
        () => client.get<T>(`/${resourcePath}/${id}`),
        `get_${resourcePath}_${id}`
      )
    }
  }
}

/**
 * 处理API错误
 */
export function handleApiError(error: any): string {
  if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        return data?.message || '请求参数错误'
      case 401:
        // 未授权，跳转到登录页
        uni.navigateTo({
          url: '/pages/login/login'
        })
        return '登录已过期，请重新登录'
      case 403:
        return '没有权限执行此操作'
      case 404:
        return '请求的资源不存在'
      case 500:
        return '服务器内部错误'
      default:
        return data?.message || `请求失败 (${status})`
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查网络设置'
  } else {
    // 其他错误
    return error.message || '未知错误'
  }
}

/**
 * 请求拦截器
 */
export function setupRequestInterceptor() {
  // 这里可以添加全局请求拦截逻辑
  // 比如添加loading状态、token刷新等
}

/**
 * 响应拦截器
 */
export function setupResponseInterceptor() {
  // 这里可以添加全局响应拦截逻辑
  // 比如统一错误处理、token过期处理等
}

/**
 * 模拟API数据（用于开发测试）
 */
export function createMockCrudApi<T extends Record<string, any>>(
  mockData: T[],
  delay: number = 500
): CrudOperation<T> {
  let data = [...mockData]
  let nextId = Math.max(...data.map(item => item.id || 0)) + 1
  
  const mockDelay = (result: any) => {
    return new Promise(resolve => {
      setTimeout(() => resolve(result), delay)
    })
  }
  
  return {
    async list(params: QueryParams): Promise<PageResult<T>> {
      await mockDelay(null)
      
      let filteredData = [...data]
      
      // 搜索过滤
      if (params.keyword) {
        filteredData = filteredData.filter(item =>
          Object.values(item).some(value =>
            String(value).toLowerCase().includes(params.keyword!.toLowerCase())
          )
        )
      }
      
      // 排序
      if (params.sortBy) {
        filteredData.sort((a, b) => {
          const aValue = a[params.sortBy!]
          const bValue = b[params.sortBy!]
          
          let result = 0
          if (aValue < bValue) result = -1
          else if (aValue > bValue) result = 1
          
          return params.sortOrder === 'desc' ? -result : result
        })
      }
      
      // 分页
      const page = params.page || 1
      const pageSize = params.pageSize || 10
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const items = filteredData.slice(start, end)
      
      return {
        items,
        total: filteredData.length,
        page,
        pageSize,
        totalPages: Math.ceil(filteredData.length / pageSize)
      }
    },
    
    async create(formData: Partial<T>): Promise<T> {
      await mockDelay(null)
      
      const newItem = {
        ...formData,
        id: nextId++,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      } as T
      
      data.push(newItem)
      return newItem
    },
    
    async update(id: number, formData: Partial<T>): Promise<T> {
      await mockDelay(null)
      
      const index = data.findIndex(item => item.id === id)
      if (index === -1) {
        throw new Error('记录不存在')
      }
      
      const updatedItem = {
        ...data[index],
        ...formData,
        updateTime: new Date().toISOString()
      }
      
      data[index] = updatedItem
      return updatedItem
    },
    
    async delete(id: number): Promise<boolean> {
      await mockDelay(null)
      
      const index = data.findIndex(item => item.id === id)
      if (index === -1) {
        throw new Error('记录不存在')
      }
      
      data.splice(index, 1)
      return true
    },
    
    async get(id: number): Promise<T> {
      await mockDelay(null)
      
      const item = data.find(item => item.id === id)
      if (!item) {
        throw new Error('记录不存在')
      }
      
      return item
    }
  }
}
