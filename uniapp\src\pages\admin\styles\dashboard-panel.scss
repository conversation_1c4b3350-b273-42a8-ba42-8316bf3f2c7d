// BI仪表板面板样式
.dashboard-panel {
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 4px;

    .header-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .refresh-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        font-size: 14px;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.loading {
          opacity: 0.6;
          pointer-events: none;

          .icon-refresh {
            animation: spin 1s linear infinite;
          }
        }

        .iconfont {
          font-size: 16px;
        }
      }
    }
  }
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .metric-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .metric-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #fff;
        margin-bottom: 12px;

        &.primary { background-color: #1890ff; }
        &.success { background-color: #52c41a; }
        &.warning { background-color: #faad14; }
        &.danger { background-color: #ff4d4f; }
      }

      .metric-value {
        font-size: 28px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .metric-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .metric-change {
        font-size: 12px;
        display: flex;
        align-items: center;

        &.positive {
          color: #52c41a;
        }

        &.negative {
          color: #ff4d4f;
        }

        .change-icon {
          margin-right: 4px;
        }
      }
    }
  }

  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    margin-bottom: 24px;

    .chart-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .chart-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
      }

      .chart-container {
        height: 300px;
        position: relative;
      }
    }
  }

  .activity-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .activity-list {
      .activity-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .activity-icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: #f0f0f0;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          font-size: 14px;
          color: #666;
        }

        .activity-content {
          flex: 1;

          .activity-description {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
          }

          .activity-time {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .dashboard-panel {
    .metrics-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
    }

    .charts-grid {
      grid-template-columns: 1fr;
      gap: 16px;

      .chart-card {
        padding: 16px;

        .chart-container {
          height: 250px;
        }
      }
    }

    .activity-section {
      padding: 16px;
    }
  }
}