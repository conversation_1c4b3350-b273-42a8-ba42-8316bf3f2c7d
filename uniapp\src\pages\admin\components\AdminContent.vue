<template>
  <view class="admin-content">
    <!-- 页面切换容器 -->
    <view class="content-container" :class="{ transitioning: isTransitioning }">
      <!-- 动态加载对应的管理模块 -->
      <transition :name="transitionName" mode="out-in">
        <component :is="currentComponent" :key="currentModule" />
      </transition>
    </view>

    <!-- 返回按钮（移动端） -->
    <view
      v-if="routeGuard.canGoBack.value && isMobile"
      class="back-button"
      @click="goBack"
    >
      <text class="back-icon">←</text>
      <text class="back-text">返回</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useUIStore } from '@/stores/ui'
import { useDashboardStore } from '@/stores/dashboard'
import { useRouteGuard } from '../composables/useRouteGuard'
import { usePageState, useGlobalPageStateCleanup } from '../composables/usePageState'
import DashboardPanel from './DashboardPanel.vue'
import SystemMonitorPanel from '../modules/SystemMonitorPanel.vue'
import SystemFunctionPanel from '../modules/SystemFunctionPanel.vue'
import SystemToolsPanel from '../modules/SystemToolsPanel.vue'

const uiStore = useUIStore()
const dashboardStore = useDashboardStore()
const routeGuard = useRouteGuard()
const globalCleanup = useGlobalPageStateCleanup()

// 当前选中的模块
const currentModule = ref('dashboard')

// 页面状态管理
const pageState = usePageState('admin-content')

// 组件映射
const componentMap = {
  dashboard: DashboardPanel,
  'system-monitor': SystemMonitorPanel,
  'system-function': SystemFunctionPanel,
  'system-tools': SystemToolsPanel,
  // 子路由映射 - 系统监控
  'system-monitor/server-monitor': SystemMonitorPanel,
  'system-monitor/database-monitor': SystemMonitorPanel,
  'system-monitor/application-monitor': SystemMonitorPanel,
  // 子路由映射 - 系统功能
  'system-function/user-management': SystemFunctionPanel,
  'system-function/role-management': SystemFunctionPanel,
  'system-function/partner-management': SystemFunctionPanel,
  'system-function/createChat-management': SystemFunctionPanel,
  'system-function/permission-management': SystemFunctionPanel,
  // 子路由映射 - 系统工具
  'system-tools/database-backup': SystemToolsPanel,
  'system-tools/log-viewer': SystemToolsPanel,
  'system-tools/cache-management': SystemToolsPanel,
  'system-tools/system-cleanup': SystemToolsPanel,
}

// 当前组件
const currentComponent = computed(() => {
  return componentMap[currentModule.value] || DashboardPanel
})

// 页面切换动画状态
const isTransitioning = ref(false)
const transitionName = ref('slide-left')

// 移动端检测
const isMobile = ref(false)

// 检查是否为移动端
const checkMobile = () => {
  const systemInfo = uni.getSystemInfoSync()
  isMobile.value = systemInfo.windowWidth <= 768
}

// 监听路由变化事件
const handleRouteChange = async (path: string) => {
  // 保存当前页面的滚动位置
  const scrollTop = uni.getSystemInfoSync().scrollTop || 0
  pageState.saveScrollPosition(scrollTop)

  // 设置切换动画
  isTransitioning.value = true

  // 确定动画方向
  const currentIndex = getRouteIndex(currentModule.value)
  const newIndex = getRouteIndex(path)
  transitionName.value = newIndex > currentIndex ? 'slide-left' : 'slide-right'

  // 延迟切换模块以显示动画
  setTimeout(() => {
    currentModule.value = path

    // 动画完成后恢复滚动位置
    nextTick(() => {
      const newPageState = usePageState(`admin-${path}`)
      const savedScrollTop = newPageState.restoreScrollPosition()

      if (savedScrollTop > 0) {
        uni.pageScrollTo({
          scrollTop: savedScrollTop,
          duration: 0
        })
      }

      isTransitioning.value = false
    })
  }, 150)

  console.log('AdminContent: 切换到模块', path)
}

// 获取路由索引（用于动画方向判断）
const getRouteIndex = (path: string): number => {
  const routes = [
    'dashboard',
    'system-function',
    'system-monitor',
    'system-tools'
  ]

  const mainRoute = path.split('/')[0]
  return routes.indexOf(mainRoute)
}

// 处理浏览器前进后退
const handlePopState = (event: PopStateEvent) => {
  if (event.state && event.state.adminPath) {
    const path = event.state.adminPath
    if (routeGuard.canNavigate(path)) {
      handleRouteChange(path)
    }
  }
}

// 更新浏览器历史状态
const updateBrowserHistory = (path: string) => {
  const state = { adminPath: path }
  const url = `#/admin/${path}`

  if (window.history.state?.adminPath !== path) {
    window.history.pushState(state, '', url)
  }
}

onMounted(() => {
  // 检查移动端
  checkMobile()

  // 初始化路由守卫
  routeGuard.initRoute('dashboard')

  // 监听路由切换事件
  uni.$on('admin-route-change', handleRouteChange)

  // 监听浏览器前进后退
  window.addEventListener('popstate', handlePopState)

  // 监听窗口大小变化
  uni.onWindowResize(checkMobile)

  // 启动全局状态清理定时器
  const cleanupTimer = globalCleanup.startCleanupTimer()

  // 监听dashboard store的模块变化
  const unwatch = dashboardStore.$subscribe((mutation, state) => {
    if (mutation.type === 'direct' && mutation.events?.key === 'currentModule') {
      const newModule = state.currentModule || 'dashboard'
      if (newModule !== currentModule.value) {
        handleRouteChange(newModule)
        updateBrowserHistory(newModule)
      }
    }
  })

  // 设置初始浏览器历史状态
  updateBrowserHistory('dashboard')

  onUnmounted(() => {
    unwatch()
    clearInterval(cleanupTimer)
    uni.offWindowResize(checkMobile)
  })
})

onUnmounted(() => {
  // 移除事件监听
  uni.$off('admin-route-change', handleRouteChange)
  window.removeEventListener('popstate', handlePopState)
})

// 暴露切换模块的方法
const switchModule = (module: string) => {
  if (routeGuard.canNavigate(module)) {
    routeGuard.navigateTo(module)
    updateBrowserHistory(module)
  }
}

// 返回上一页
const goBack = () => {
  if (routeGuard.goBack()) {
    updateBrowserHistory(routeGuard.currentPath.value)
  }
}

defineExpose({
  switchModule,
  goBack,
  canGoBack: routeGuard.canGoBack
})
</script>

<style lang="scss" scoped>
@import '../styles/admin-content.scss';

// 页面切换动画
.content-container {
  height: 100%;
  position: relative;

  &.transitioning {
    pointer-events: none;
  }
}

.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 返回按钮
.back-button {
  position: fixed;
  bottom: 80px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background-color: #1890ff;
  color: #fff;
  border-radius: 25px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  cursor: pointer;
  z-index: 999;
  transition: all 0.3s ease;

  &:hover {
    background-color: #40a9ff;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  .back-icon {
    font-size: 16px;
    font-weight: bold;
  }

  .back-text {
    font-size: 14px;
    font-weight: 500;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .content-container {
    .slide-left-enter-active,
    .slide-left-leave-active,
    .slide-right-enter-active,
    .slide-right-leave-active {
      transition: transform 0.25s ease-out;
    }
  }

  .back-button {
    bottom: 60px;
    right: 16px;
    padding: 8px 12px;

    .back-icon {
      font-size: 14px;
    }

    .back-text {
      font-size: 12px;
    }
  }
}

// 减少动画以提高性能
@media (prefers-reduced-motion: reduce) {
  .slide-left-enter-active,
  .slide-left-leave-active,
  .slide-right-enter-active,
  .slide-right-leave-active {
    transition: opacity 0.2s ease;
  }

  .slide-left-enter-from,
  .slide-left-leave-to,
  .slide-right-enter-from,
  .slide-right-leave-to {
    transform: none;
  }
}
</style>