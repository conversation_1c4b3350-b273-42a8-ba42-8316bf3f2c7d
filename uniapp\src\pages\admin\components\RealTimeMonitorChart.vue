<template>
  <view class="realtime-monitor-chart">
    <view class="chart-header">
      <view class="header-left">
        <text class="chart-title">实时监控</text>
        <view class="status-indicator" :class="{ active: isRealTime }">
          <view class="indicator-dot"></view>
          <text>{{ isRealTime ? '实时' : '已暂停' }}</text>
        </view>
      </view>
      
      <view class="header-right">
        <view class="refresh-interval">
          <text>刷新间隔:</text>
          <view class="interval-selector">
            <view 
              v-for="interval in refreshIntervals" 
              :key="interval.value"
              :class="['interval-item', { active: selectedInterval === interval.value }]"
              @click="selectInterval(interval.value)"
            >
              {{ interval.label }}
            </view>
          </view>
        </view>
        
        <view class="control-buttons">
          <view 
            class="control-btn" 
            @click="toggleRealTime"
            :class="{ active: isRealTime }"
          >
            <text class="iconfont" :class="isRealTime ? 'icon-pause' : 'icon-play'"></text>
          </view>
          <view class="control-btn" @click="handleRefresh">
            <text class="iconfont icon-refresh" :class="{ spinning: loading }"></text>
          </view>
        </view>
      </view>
    </view>

    <BaseChart
      type="line"
      :data="chartData"
      :width="500"
      :height="300"
      :loading="loading"
      :show-legend="true"
      :options="chartOptions"
      @click="handleChartClick"
      @hover="handleChartHover"
    />
    
    <!-- 实时数据指标 -->
    <view class="realtime-metrics">
      <view 
        v-for="metric in realtimeMetrics" 
        :key="metric.key"
        class="metric-card"
        :class="getMetricStatusClass(metric)"
      >
        <view class="metric-header">
          <text class="metric-name">{{ metric.name }}</text>
          <view class="metric-trend" :class="metric.trend">
            <text class="iconfont" :class="getTrendIcon(metric.trend)"></text>
            <text>{{ Math.abs(metric.change).toFixed(1) }}%</text>
          </view>
        </view>
        
        <view class="metric-value">
          {{ formatMetricValue(metric.value, metric.unit) }}
        </view>
        
        <view class="metric-status">
          <view class="status-bar">
            <view 
              class="status-fill" 
              :style="{ 
                width: getStatusPercentage(metric) + '%',
                backgroundColor: getStatusColor(metric)
              }"
            ></view>
          </view>
          <text class="status-text">{{ getStatusText(metric) }}</text>
        </view>
      </view>
    </view>
    
    <!-- 告警信息 -->
    <view v-if="alerts.length > 0" class="alerts-section">
      <view class="alerts-header">
        <text class="alerts-title">系统告警</text>
        <text class="alerts-count">{{ alerts.length }}</text>
      </view>
      
      <view class="alerts-list">
        <view 
          v-for="alert in alerts" 
          :key="alert.id"
          class="alert-item"
          :class="alert.level"
          @click="handleAlertClick(alert)"
        >
          <view class="alert-icon">
            <text class="iconfont" :class="getAlertIcon(alert.level)"></text>
          </view>
          <view class="alert-content">
            <text class="alert-message">{{ alert.message }}</text>
            <text class="alert-time">{{ formatTime(alert.timestamp) }}</text>
          </view>
          <view class="alert-action">
            <text class="iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import BaseChart from './BaseChart.vue'
import { useDashboardStore } from '@/stores/dashboard'

interface RealtimeMetric {
  key: string
  name: string
  value: number
  unit: string
  threshold: {
    warning: number
    critical: number
  }
  change: number
  trend: 'up' | 'down' | 'stable'
}

interface Alert {
  id: string
  level: 'info' | 'warning' | 'error' | 'critical'
  message: string
  timestamp: string
  resolved: boolean
}

interface DataPoint {
  timestamp: string
  cpu: number
  memory: number
  network: number
  disk: number
}

const dashboardStore = useDashboardStore()

// 响应式数据
const loading = ref(false)
const isRealTime = ref(true)
const selectedInterval = ref(5000)
const dataPoints = ref<DataPoint[]>([])
const realtimeMetrics = ref<RealtimeMetric[]>([])
const alerts = ref<Alert[]>([])

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 刷新间隔选项
const refreshIntervals = [
  { label: '1s', value: 1000 },
  { label: '5s', value: 5000 },
  { label: '10s', value: 10000 },
  { label: '30s', value: 30000 }
]

// 计算属性
const chartData = computed(() => {
  if (dataPoints.value.length === 0) return []
  
  // 只显示最近50个数据点
  const recentData = dataPoints.value.slice(-50)
  
  return recentData.map((point, index) => ({
    label: formatTimeLabel(point.timestamp),
    value: point.cpu, // 默认显示CPU数据
    color: '#1890ff'
  }))
})

const chartOptions = computed(() => ({
  animation: false, // 实时数据不需要动画
  showGrid: true,
  responsive: true,
  realtime: true
}))

// 方法
const loadRealtimeData = async () => {
  if (!isRealTime.value) return
  
  loading.value = true
  
  try {
    const data = await dashboardStore.fetchChartData('realtime-monitor')
    
    if (data) {
      updateRealtimeData(data)
    } else {
      // 使用模拟数据
      generateMockRealtimeData()
    }
  } catch (error) {
    console.error('加载实时监控数据失败:', error)
    generateMockRealtimeData()
  } finally {
    loading.value = false
  }
}

const updateRealtimeData = (data: any) => {
  const now = new Date().toISOString()
  
  // 添加新数据点
  const newDataPoint: DataPoint = {
    timestamp: now,
    cpu: data.cpu || 0,
    memory: data.memory || 0,
    network: data.network || 0,
    disk: data.disk || 0
  }
  
  dataPoints.value.push(newDataPoint)
  
  // 保持最多100个数据点
  if (dataPoints.value.length > 100) {
    dataPoints.value = dataPoints.value.slice(-100)
  }
  
  // 更新实时指标
  updateRealtimeMetrics(newDataPoint)
  
  // 检查告警
  checkAlerts(newDataPoint)
}

const generateMockRealtimeData = () => {
  const now = new Date().toISOString()
  
  // 生成模拟的实时数据
  const newDataPoint: DataPoint = {
    timestamp: now,
    cpu: Math.random() * 100,
    memory: Math.random() * 100,
    network: Math.random() * 1000,
    disk: Math.random() * 100
  }
  
  dataPoints.value.push(newDataPoint)
  
  // 保持最多100个数据点
  if (dataPoints.value.length > 100) {
    dataPoints.value = dataPoints.value.slice(-100)
  }
  
  updateRealtimeMetrics(newDataPoint)
  checkAlerts(newDataPoint)
}

const updateRealtimeMetrics = (dataPoint: DataPoint) => {
  const previousPoint = dataPoints.value[dataPoints.value.length - 2]
  
  realtimeMetrics.value = [
    {
      key: 'cpu',
      name: 'CPU使用率',
      value: dataPoint.cpu,
      unit: '%',
      threshold: { warning: 70, critical: 90 },
      change: previousPoint ? ((dataPoint.cpu - previousPoint.cpu) / previousPoint.cpu) * 100 : 0,
      trend: previousPoint ? (dataPoint.cpu > previousPoint.cpu ? 'up' : dataPoint.cpu < previousPoint.cpu ? 'down' : 'stable') : 'stable'
    },
    {
      key: 'memory',
      name: '内存使用率',
      value: dataPoint.memory,
      unit: '%',
      threshold: { warning: 80, critical: 95 },
      change: previousPoint ? ((dataPoint.memory - previousPoint.memory) / previousPoint.memory) * 100 : 0,
      trend: previousPoint ? (dataPoint.memory > previousPoint.memory ? 'up' : dataPoint.memory < previousPoint.memory ? 'down' : 'stable') : 'stable'
    },
    {
      key: 'network',
      name: '网络IO',
      value: dataPoint.network,
      unit: 'MB/s',
      threshold: { warning: 500, critical: 800 },
      change: previousPoint ? ((dataPoint.network - previousPoint.network) / previousPoint.network) * 100 : 0,
      trend: previousPoint ? (dataPoint.network > previousPoint.network ? 'up' : dataPoint.network < previousPoint.network ? 'down' : 'stable') : 'stable'
    },
    {
      key: 'disk',
      name: '磁盘使用率',
      value: dataPoint.disk,
      unit: '%',
      threshold: { warning: 85, critical: 95 },
      change: previousPoint ? ((dataPoint.disk - previousPoint.disk) / previousPoint.disk) * 100 : 0,
      trend: previousPoint ? (dataPoint.disk > previousPoint.disk ? 'up' : dataPoint.disk < previousPoint.disk ? 'down' : 'stable') : 'stable'
    }
  ]
}

const checkAlerts = (dataPoint: DataPoint) => {
  const now = new Date().toISOString()
  
  // 检查CPU告警
  if (dataPoint.cpu > 90) {
    addAlert({
      id: `cpu-critical-${Date.now()}`,
      level: 'critical',
      message: `CPU使用率过高: ${dataPoint.cpu.toFixed(1)}%`,
      timestamp: now,
      resolved: false
    })
  } else if (dataPoint.cpu > 70) {
    addAlert({
      id: `cpu-warning-${Date.now()}`,
      level: 'warning',
      message: `CPU使用率警告: ${dataPoint.cpu.toFixed(1)}%`,
      timestamp: now,
      resolved: false
    })
  }
  
  // 检查内存告警
  if (dataPoint.memory > 95) {
    addAlert({
      id: `memory-critical-${Date.now()}`,
      level: 'critical',
      message: `内存使用率过高: ${dataPoint.memory.toFixed(1)}%`,
      timestamp: now,
      resolved: false
    })
  }
}

const addAlert = (alert: Alert) => {
  // 避免重复告警
  const existingAlert = alerts.value.find(a => 
    a.level === alert.level && 
    a.message.includes(alert.message.split(':')[0]) &&
    Date.now() - new Date(a.timestamp).getTime() < 60000 // 1分钟内不重复
  )
  
  if (!existingAlert) {
    alerts.value.unshift(alert)
    
    // 保持最多10条告警
    if (alerts.value.length > 10) {
      alerts.value = alerts.value.slice(0, 10)
    }
  }
}

const formatTimeLabel = (timestamp: string): string => {
  const date = new Date(timestamp)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
}

const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else {
    return date.toLocaleTimeString()
  }
}

const formatMetricValue = (value: number, unit: string): string => {
  if (unit === '%') {
    return `${value.toFixed(1)}%`
  } else if (unit === 'MB/s') {
    return `${value.toFixed(1)} MB/s`
  }
  return `${value.toFixed(1)} ${unit}`
}

const getMetricStatusClass = (metric: RealtimeMetric): string => {
  if (metric.value >= metric.threshold.critical) return 'critical'
  if (metric.value >= metric.threshold.warning) return 'warning'
  return 'normal'
}

const getStatusPercentage = (metric: RealtimeMetric): number => {
  const max = metric.unit === '%' ? 100 : metric.threshold.critical * 1.2
  return Math.min((metric.value / max) * 100, 100)
}

const getStatusColor = (metric: RealtimeMetric): string => {
  if (metric.value >= metric.threshold.critical) return '#ff4d4f'
  if (metric.value >= metric.threshold.warning) return '#faad14'
  return '#52c41a'
}

const getStatusText = (metric: RealtimeMetric): string => {
  if (metric.value >= metric.threshold.critical) return '严重'
  if (metric.value >= metric.threshold.warning) return '警告'
  return '正常'
}

const getTrendIcon = (trend: string): string => {
  switch (trend) {
    case 'up': return 'icon-arrow-up'
    case 'down': return 'icon-arrow-down'
    default: return 'icon-minus'
  }
}

const getAlertIcon = (level: string): string => {
  switch (level) {
    case 'critical': return 'icon-close-circle'
    case 'error': return 'icon-exclamation-circle'
    case 'warning': return 'icon-warning'
    default: return 'icon-info-circle'
  }
}

const selectInterval = (interval: number) => {
  selectedInterval.value = interval
  if (isRealTime.value) {
    stopRealTimeUpdate()
    startRealTimeUpdate()
  }
}

const toggleRealTime = () => {
  isRealTime.value = !isRealTime.value
  
  if (isRealTime.value) {
    startRealTimeUpdate()
  } else {
    stopRealTimeUpdate()
  }
}

const startRealTimeUpdate = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  refreshTimer = setInterval(() => {
    loadRealtimeData()
  }, selectedInterval.value)
  
  // 立即加载一次数据
  loadRealtimeData()
}

const stopRealTimeUpdate = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

const handleRefresh = () => {
  loadRealtimeData()
}

const handleChartClick = (item: any, index: number) => {
  const dataPoint = dataPoints.value[dataPoints.value.length - 50 + index]
  if (dataPoint) {
    const content = [
      `⏰ 时间: ${formatTimeLabel(dataPoint.timestamp)}`,
      `💻 CPU: ${dataPoint.cpu.toFixed(1)}%`,
      `🧠 内存: ${dataPoint.memory.toFixed(1)}%`,
      `🌐 网络: ${dataPoint.network.toFixed(1)} MB/s`,
      `💾 磁盘: ${dataPoint.disk.toFixed(1)}%`
    ].join('\n')
    
    uni.showModal({
      title: '系统状态详情',
      content,
      showCancel: false,
      confirmText: '确定'
    })
  }
}

const handleChartHover = (item: any, index: number) => {
  // 悬停效果处理
  console.log('悬停在实时数据:', item, index)
}

const handleAlertClick = (alert: Alert) => {
  uni.showModal({
    title: '告警详情',
    content: `级别: ${alert.level}\n消息: ${alert.message}\n时间: ${formatTime(alert.timestamp)}`,
    showCancel: true,
    cancelText: '关闭',
    confirmText: '标记已解决',
    success: (res) => {
      if (res.confirm) {
        // 标记告警为已解决
        const alertIndex = alerts.value.findIndex(a => a.id === alert.id)
        if (alertIndex !== -1) {
          alerts.value[alertIndex].resolved = true
          uni.showToast({
            title: '已标记为解决',
            icon: 'success'
          })
        }
      }
    }
  })
}

// 监听实时状态变化
watch(isRealTime, (newValue) => {
  if (newValue) {
    startRealTimeUpdate()
  } else {
    stopRealTimeUpdate()
  }
})

// 生命周期
onMounted(() => {
  if (isRealTime.value) {
    startRealTimeUpdate()
  }
})

onUnmounted(() => {
  stopRealTimeUpdate()
})

// 暴露方法
const refresh = () => {
  loadRealtimeData()
}

defineExpose({
  refresh,
  toggleRealTime,
  clearAlerts: () => { alerts.value = [] }
})
</script>

<style lang="scss" scoped>
.realtime-monitor-chart {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #999;

        .indicator-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #ccc;
          transition: all 0.3s ease;
        }

        &.active .indicator-dot {
          background-color: #52c41a;
          animation: pulse 2s infinite;
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 16px;

      .refresh-interval {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #666;

        .interval-selector {
          display: flex;
          gap: 4px;

          .interval-item {
            padding: 2px 6px;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              border-color: #1890ff;
              color: #1890ff;
            }

            &.active {
              background-color: #1890ff;
              border-color: #1890ff;
              color: #fff;
            }
          }
        }
      }

      .control-buttons {
        display: flex;
        gap: 8px;

        .control-btn {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s ease;

          .iconfont {
            font-size: 14px;
            color: #666;

            &.spinning {
              animation: spin 1s linear infinite;
            }
          }

          &:hover {
            border-color: #1890ff;

            .iconfont {
              color: #1890ff;
            }
          }

          &.active {
            background-color: #1890ff;
            border-color: #1890ff;

            .iconfont {
              color: #fff;
            }
          }
        }
      }
    }
  }

  .realtime-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 16px;

    .metric-card {
      padding: 12px;
      background-color: #fff;
      border-radius: 6px;
      border-left: 4px solid #52c41a;
      transition: all 0.3s ease;

      &.warning {
        border-left-color: #faad14;
      }

      &.critical {
        border-left-color: #ff4d4f;
      }

      .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .metric-name {
          font-size: 12px;
          color: #666;
          font-weight: 500;
        }

        .metric-trend {
          display: flex;
          align-items: center;
          gap: 2px;
          font-size: 11px;

          &.up {
            color: #ff4d4f;
          }

          &.down {
            color: #52c41a;
          }

          &.stable {
            color: #999;
          }
        }
      }

      .metric-value {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .metric-status {
        .status-bar {
          height: 4px;
          background-color: #f0f0f0;
          border-radius: 2px;
          overflow: hidden;
          margin-bottom: 4px;

          .status-fill {
            height: 100%;
            transition: all 0.3s ease;
          }
        }

        .status-text {
          font-size: 11px;
          color: #999;
        }
      }
    }
  }

  .alerts-section {
    margin-top: 16px;

    .alerts-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .alerts-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .alerts-count {
        background-color: #ff4d4f;
        color: #fff;
        font-size: 11px;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
      }
    }

    .alerts-list {
      .alert-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 12px;
        background-color: #fff;
        border-radius: 4px;
        margin-bottom: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-left: 3px solid #52c41a;

        &.warning {
          border-left-color: #faad14;
        }

        &.error {
          border-left-color: #ff4d4f;
        }

        &.critical {
          border-left-color: #ff4d4f;
          background-color: #fff2f0;
        }

        &:hover {
          background-color: #f8f9fa;
        }

        .alert-icon {
          .iconfont {
            font-size: 16px;
            color: #52c41a;
          }
        }

        &.warning .alert-icon .iconfont {
          color: #faad14;
        }

        &.error .alert-icon .iconfont,
        &.critical .alert-icon .iconfont {
          color: #ff4d4f;
        }

        .alert-content {
          flex: 1;

          .alert-message {
            display: block;
            font-size: 13px;
            color: #333;
            margin-bottom: 2px;
          }

          .alert-time {
            font-size: 11px;
            color: #999;
          }
        }

        .alert-action {
          .iconfont {
            font-size: 12px;
            color: #ccc;
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 移动端适配
@media (max-width: 768px) {
  .realtime-monitor-chart {
    .chart-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .header-right {
        justify-content: space-between;
      }
    }

    .realtime-metrics {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      .metric-card {
        padding: 8px;

        .metric-value {
          font-size: 16px;
        }
      }
    }
  }
}

// 暗色主题支持
.admin-layout.dark-theme .realtime-monitor-chart {
  .chart-header {
    background-color: #2a2a2a;

    .chart-title {
      color: #fff;
    }

    .status-indicator {
      color: #ccc;
    }

    .refresh-interval {
      color: #ccc;

      .interval-item {
        border-color: #434343;
        color: #ccc;
        background-color: #1f1f1f;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.active {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;
        }
      }
    }

    .control-btn {
      border-color: #434343;
      background-color: #1f1f1f;

      .iconfont {
        color: #ccc;
      }

      &:hover {
        border-color: #1890ff;

        .iconfont {
          color: #1890ff;
        }
      }
    }
  }

  .metric-card {
    background-color: #1f1f1f;

    .metric-name {
      color: #ccc;
    }

    .metric-value {
      color: #fff;
    }

    .status-bar {
      background-color: #434343;
    }

    .status-text {
      color: #999;
    }
  }

  .alerts-section {
    .alerts-title {
      color: #fff;
    }

    .alert-item {
      background-color: #1f1f1f;

      &:hover {
        background-color: #2a2a2a;
      }

      &.critical {
        background-color: #2a1f1f;
      }

      .alert-message {
        color: #fff;
      }
    }
  }
}
</style>