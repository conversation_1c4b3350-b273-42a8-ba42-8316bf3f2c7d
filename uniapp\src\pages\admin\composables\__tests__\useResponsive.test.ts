/**
 * 响应式工具测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useResponsive, globalResponsive } from '../useResponsive'

describe('useResponsive', () => {
  let responsive: ReturnType<typeof useResponsive>

  beforeEach(() => {
    // 重置uni.getSystemInfoSync的mock
    vi.mocked(uni.getSystemInfoSync).mockReturnValue({
      windowWidth: 375,
      windowHeight: 667,
      pixelRatio: 2,
      platform: 'ios',
      system: 'iOS 14.0',
      safeAreaInsets: {
        top: 44,
        bottom: 34,
        left: 0,
        right: 0
      }
    })

    responsive = useResponsive()
  })

  describe('设备类型检测', () => {
    it('应该正确检测移动设备', () => {
      // 模拟移动设备屏幕尺寸
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 375,
        windowHeight: 667,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iOS 14.0'
      })

      responsive.updateScreenInfo()

      expect(responsive.deviceType.value.isMobile).toBe(true)
      expect(responsive.deviceType.value.isTablet).toBe(false)
      expect(responsive.deviceType.value.isDesktop).toBe(false)
    })

    it('应该正确检测平板设备', () => {
      // 模拟平板屏幕尺寸
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 900,
        windowHeight: 1200,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iPadOS 14.0'
      })

      responsive.updateScreenInfo()

      expect(responsive.deviceType.value.isMobile).toBe(false)
      expect(responsive.deviceType.value.isTablet).toBe(true)
      expect(responsive.deviceType.value.isDesktop).toBe(false)
    })

    it('应该正确检测桌面设备', () => {
      // 模拟桌面屏幕尺寸
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 1440,
        windowHeight: 900,
        pixelRatio: 1,
        platform: 'windows',
        system: 'Windows 10'
      })

      responsive.updateScreenInfo()

      expect(responsive.deviceType.value.isMobile).toBe(false)
      expect(responsive.deviceType.value.isTablet).toBe(false)
      expect(responsive.deviceType.value.isDesktop).toBe(true)
    })
  })

  describe('断点检测', () => {
    it('应该正确识别xs断点', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 400,
        windowHeight: 600,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iOS 14.0'
      })

      responsive.updateScreenInfo()

      expect(responsive.currentBreakpoint.value).toBe('xs')
    })

    it('应该正确识别sm断点', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 600,
        windowHeight: 800,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iOS 14.0'
      })

      responsive.updateScreenInfo()

      expect(responsive.currentBreakpoint.value).toBe('sm')
    })

    it('应该正确识别md断点', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 900,
        windowHeight: 1200,
        pixelRatio: 1,
        platform: 'windows',
        system: 'Windows 10'
      })

      responsive.updateScreenInfo()

      expect(responsive.currentBreakpoint.value).toBe('md')
    })

    it('应该正确识别lg断点', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 1100,
        windowHeight: 800,
        pixelRatio: 1,
        platform: 'windows',
        system: 'Windows 10'
      })

      responsive.updateScreenInfo()

      expect(responsive.currentBreakpoint.value).toBe('lg')
    })

    it('应该正确识别xl断点', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 1800,
        windowHeight: 1200,
        pixelRatio: 1,
        platform: 'windows',
        system: 'Windows 10'
      })

      responsive.updateScreenInfo()

      expect(responsive.currentBreakpoint.value).toBe('xl')
    })
  })

  describe('屏幕方向检测', () => {
    it('应该正确检测横屏', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 800,
        windowHeight: 600,
        pixelRatio: 1,
        platform: 'windows',
        system: 'Windows 10'
      })

      responsive.updateScreenInfo()

      expect(responsive.orientation.value).toBe('landscape')
    })

    it('应该正确检测竖屏', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 375,
        windowHeight: 667,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iOS 14.0'
      })

      responsive.updateScreenInfo()

      expect(responsive.orientation.value).toBe('portrait')
    })
  })

  describe('触摸设备检测', () => {
    it('应该正确检测触摸设备', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 375,
        windowHeight: 667,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iOS 14.0'
      })

      responsive.updateScreenInfo()

      expect(responsive.isTouchDevice.value).toBe(true)
    })

    it('应该正确检测非触摸设备', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 1440,
        windowHeight: 900,
        pixelRatio: 1,
        platform: 'windows',
        system: 'Windows 10'
      })

      responsive.updateScreenInfo()

      expect(responsive.isTouchDevice.value).toBe(false)
    })
  })

  describe('工具方法', () => {
    it('应该正确匹配断点', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 600,
        windowHeight: 800,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iOS 14.0'
      })

      responsive.updateScreenInfo()

      expect(responsive.matchBreakpoint('sm')).toBe(true)
      expect(responsive.matchBreakpoint('xs')).toBe(false)
    })

    it('应该正确匹配范围', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 800,
        windowHeight: 600,
        pixelRatio: 1,
        platform: 'windows',
        system: 'Windows 10'
      })

      responsive.updateScreenInfo()

      expect(responsive.matchRange(700, 900)).toBe(true)
      expect(responsive.matchRange(900, 1200)).toBe(false)
      expect(responsive.matchRange(700)).toBe(true)
      expect(responsive.matchRange(undefined, 900)).toBe(true)
    })

    it('应该获取响应式值', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 600,
        windowHeight: 800,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iOS 14.0'
      })

      responsive.updateScreenInfo()

      const value = responsive.getResponsiveValue({
        xs: 1,
        sm: 2,
        md: 3,
        lg: 4,
        xl: 5,
        default: 0
      })

      expect(value).toBe(2) // sm断点
    })

    it('应该获取响应式类名', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 375,
        windowHeight: 667,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iOS 14.0'
      })

      responsive.updateScreenInfo()

      const className = responsive.getResponsiveClass('container')

      expect(className).toContain('container')
      expect(className).toContain('container--mobile')
      expect(className).toContain('container--xs')
      expect(className).toContain('container--portrait')
    })

    it('应该获取网格列数', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 900,
        windowHeight: 1200,
        pixelRatio: 1,
        platform: 'windows',
        system: 'Windows 10'
      })

      responsive.updateScreenInfo()

      const columns = responsive.getGridColumns({
        xs: 1,
        sm: 2,
        md: 3,
        lg: 4,
        xl: 5,
        default: 2
      })

      expect(columns).toBe(3) // md断点
    })

    it('应该获取间距大小', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 1100,
        windowHeight: 800,
        pixelRatio: 1,
        platform: 'windows',
        system: 'Windows 10'
      })

      responsive.updateScreenInfo()

      const spacing = responsive.getSpacing({
        xs: '8px',
        sm: '12px',
        md: '16px',
        lg: '20px',
        xl: '24px',
        default: '16px'
      })

      expect(spacing).toBe('20px') // lg断点
    })
  })

  describe('布局检测', () => {
    it('应该正确判断是否使用移动端布局', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 500,
        windowHeight: 800,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iOS 14.0'
      })

      responsive.updateScreenInfo()

      expect(responsive.shouldUseMobileLayout.value).toBe(true)
    })

    it('应该正确判断是否使用紧凑布局', () => {
      vi.mocked(uni.getSystemInfoSync).mockReturnValue({
        windowWidth: 900,
        windowHeight: 1200,
        pixelRatio: 2,
        platform: 'ios',
        system: 'iPadOS 14.0'
      })

      responsive.updateScreenInfo()

      expect(responsive.shouldUseCompactLayout.value).toBe(true)
    })
  })

  describe('安全区域', () => {
    it('应该获取安全区域信息', () => {
      const safeArea = responsive.getSafeArea()

      expect(safeArea).toEqual({
        top: 44,
        bottom: 34,
        left: 0,
        right: 0
      })
    })

    it('应该处理获取安全区域失败的情况', () => {
      vi.mocked(uni.getSystemInfoSync).mockImplementation(() => {
        throw new Error('获取系统信息失败')
      })

      const safeArea = responsive.getSafeArea()

      expect(safeArea).toEqual({
        top: 0,
        bottom: 0,
        left: 0,
        right: 0
      })
    })
  })
})

describe('globalResponsive', () => {
  it('应该是useResponsive的实例', () => {
    expect(globalResponsive).toBeDefined()
    expect(typeof globalResponsive.updateScreenInfo).toBe('function')
    expect(typeof globalResponsive.matchBreakpoint).toBe('function')
  })
})
