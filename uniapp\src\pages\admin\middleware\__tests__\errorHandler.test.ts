/**
 * 错误处理中间件测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { errorHandler, handleError, retryOperation } from '../errorHandler'

describe('ErrorHandler', () => {
  beforeEach(() => {
    errorHandler.clearErrorLog()
    vi.clearAllMocks()
  })

  describe('错误解析', () => {
    it('应该正确解析网络错误', () => {
      const networkError = {
        errMsg: 'request:fail timeout'
      }

      const errorInfo = errorHandler.handle(networkError, { showToast: false })

      expect(errorInfo.type).toBe('network')
      expect(errorInfo.message).toBe('网络连接超时，请检查网络设置')
    })

    it('应该正确解析API错误', () => {
      const apiError = {
        statusCode: 404,
        data: { message: '资源不存在' }
      }

      const errorInfo = errorHandler.handle(apiError, { showToast: false })

      expect(errorInfo.type).toBe('api')
      expect(errorInfo.code).toBe(404)
      expect(errorInfo.message).toBe('请求的资源不存在')
    })

    it('应该正确解析超时错误', () => {
      const timeoutError = {
        errMsg: 'request:fail timeout'
      }

      const errorInfo = errorHandler.handle(timeoutError, { showToast: false })

      expect(errorInfo.type).toBe('timeout')
      expect(errorInfo.message).toBe('请求超时，请检查网络连接')
    })

    it('应该正确解析权限错误', () => {
      const permissionError = {
        message: '没有权限执行此操作'
      }

      const errorInfo = errorHandler.handle(permissionError, { showToast: false })

      expect(errorInfo.type).toBe('permission')
      expect(errorInfo.message).toBe('没有权限执行此操作')
    })

    it('应该正确解析验证错误', () => {
      const validationError = {
        name: 'ValidationError',
        message: '数据验证失败'
      }

      const errorInfo = errorHandler.handle(validationError, { showToast: false })

      expect(errorInfo.type).toBe('validation')
      expect(errorInfo.message).toBe('数据验证失败')
    })

    it('应该正确解析系统错误', () => {
      const systemError = new Error('系统内部错误')

      const errorInfo = errorHandler.handle(systemError, { showToast: false })

      expect(errorInfo.type).toBe('system')
      expect(errorInfo.message).toBe('系统内部错误')
    })
  })

  describe('用户反馈', () => {
    it('应该显示Toast提示', () => {
      const error = new Error('测试错误')
      const showToastSpy = vi.spyOn(uni, 'showToast')

      errorHandler.handle(error, { showToast: true })

      expect(showToastSpy).toHaveBeenCalledWith({
        title: '测试错误',
        icon: 'none',
        duration: 3000,
        mask: false
      })
    })

    it('应该显示Modal对话框', () => {
      const error = new Error('严重错误')
      const showModalSpy = vi.spyOn(uni, 'showModal')

      errorHandler.handle(error, { showModal: true, showToast: false })

      expect(showModalSpy).toHaveBeenCalledWith({
        title: '错误提示',
        content: '严重错误',
        showCancel: false,
        confirmText: '确定'
      })
    })
  })

  describe('错误日志', () => {
    it('应该记录错误日志', () => {
      const error = new Error('测试错误')

      errorHandler.handle(error, { logError: true, showToast: false })

      const errorLog = errorHandler.getErrorLog()
      expect(errorLog).toHaveLength(1)
      expect(errorLog[0].message).toBe('测试错误')
      expect(errorLog[0].type).toBe('system')
    })

    it('应该限制日志大小', () => {
      // 添加超过最大限制的错误
      for (let i = 0; i < 150; i++) {
        errorHandler.handle(new Error(`错误 ${i}`), { showToast: false })
      }

      const errorLog = errorHandler.getErrorLog()
      expect(errorLog.length).toBeLessThanOrEqual(100)
    })

    it('应该持久化重要错误', () => {
      const systemError = {
        statusCode: 500,
        data: { message: '服务器内部错误' }
      }

      const setStorageSpy = vi.spyOn(uni, 'setStorageSync')
      errorHandler.handle(systemError, { showToast: false })

      expect(setStorageSpy).toHaveBeenCalledWith(
        'admin_error_log',
        expect.arrayContaining([
          expect.objectContaining({
            type: 'api',
            code: 500
          })
        ])
      )
    })

    it('应该清除错误日志', () => {
      errorHandler.handle(new Error('测试'), { showToast: false })
      expect(errorHandler.getErrorLog()).toHaveLength(1)

      errorHandler.clearErrorLog()
      expect(errorHandler.getErrorLog()).toHaveLength(0)
    })
  })

  describe('特殊错误处理', () => {
    it('应该处理401认证错误', () => {
      const authError = {
        statusCode: 401,
        data: { message: '未授权' }
      }

      const navigateToSpy = vi.spyOn(uni, 'navigateTo')
      
      errorHandler.handle(authError, { redirectOnAuth: true, showToast: false })

      // 使用setTimeout模拟延迟跳转
      setTimeout(() => {
        expect(navigateToSpy).toHaveBeenCalledWith({
          url: '/pages/login/login'
        })
      }, 1600)
    })
  })

  describe('重试机制', () => {
    it('应该成功执行操作', async () => {
      const operation = vi.fn().mockResolvedValue('success')

      const result = await errorHandler.retry(operation, 'test-key')

      expect(result).toBe('success')
      expect(operation).toHaveBeenCalledTimes(1)
    })

    it('应该重试失败的操作', async () => {
      const operation = vi.fn()
        .mockRejectedValueOnce(new Error('第一次失败'))
        .mockRejectedValueOnce(new Error('第二次失败'))
        .mockResolvedValue('成功')

      const result = await errorHandler.retry(operation, 'test-key', 3)

      expect(result).toBe('成功')
      expect(operation).toHaveBeenCalledTimes(3)
    })

    it('应该在达到最大重试次数后抛出错误', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('持续失败'))

      await expect(
        errorHandler.retry(operation, 'test-key', 2)
      ).rejects.toThrow('持续失败')

      expect(operation).toHaveBeenCalledTimes(3) // 初始调用 + 2次重试
    })
  })
})

describe('便捷函数', () => {
  it('handleError应该调用errorHandler.handle', () => {
    const handleSpy = vi.spyOn(errorHandler, 'handle')
    const error = new Error('测试')
    const options = { showToast: false }

    handleError(error, options)

    expect(handleSpy).toHaveBeenCalledWith(error, options)
  })

  it('retryOperation应该调用errorHandler.retry', async () => {
    const retrySpy = vi.spyOn(errorHandler, 'retry')
    const operation = vi.fn().mockResolvedValue('success')

    await retryOperation(operation, 'test-key', 3)

    expect(retrySpy).toHaveBeenCalledWith(operation, 'test-key', 3)
  })
})
